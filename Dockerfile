FROM node:22-slim as builder

WORKDIR /usr/app

ARG KICK_NPM_REGISTRY_URL
ARG KICK_NPM_REGISTRY_TOKEN

RUN corepack enable && corepack prepare pnpm@latest --activate
RUN pnpm config set -- //$KICK_NPM_REGISTRY_URL:_authToken $KICK_NPM_REGISTRY_TOKEN
RUN pnpm config set prefer-frozen-lockfile true

COPY package.json ./
COPY pnpm-lock.yaml ./
COPY .npmrc ./

RUN pnpm install --frozen-lockfile

COPY . .

RUN export $(cat .env.BUILD_ARGS | xargs) && pnpm build

FROM node:22-slim as runner

WORKDIR /usr/app

COPY --from=builder /usr/app/.next/standalone ./
COPY --from=builder /usr/app/.next/static .next/static
COPY public public


ENV PORT=3001
EXPOSE 3001

CMD ["node", "server.js"]
