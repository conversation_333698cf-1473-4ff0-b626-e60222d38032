#!/bin/sh

echo "🔍 Running Prettier on staged files (pre-commit)..."

STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|tsx|js|jsx|json|md|css|scss)$')

if [ -n "$STAGED_FILES" ]; then
  echo "📝 Formatting staged files..."
  pnpm prettier --write $STAGED_FILES
  git add $STAGED_FILES
  echo "✅ Files formatted and staged"
else
  echo "✅ No staged files to format"
fi

echo "🔍 Running linting and type checking..."
pnpm run fix
echo "✅ Pre-commit validation completed"