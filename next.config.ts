import type { NextConfig } from "next"
import csp from "./config/csp.config"

type NodeEnv = "development" | "production" | "test" | "staging"
const nodeEnv = process.env.NODE_ENV as NodeEnv
const isProduction = nodeEnv === "production"
const isStaging = nodeEnv === "staging"

const nextConfig: NextConfig = {
  turbopack: {},
  reactStrictMode: true,
  output: isProduction || isStaging ? "standalone" : undefined,
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          {
            key: "Content-Security-Policy",
            value: csp.replace(/\n/g, ""),
          },
          {
            key: "Accept-Language",
            value: "id",
          },
        ],
      },
    ]
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "external-content.duckduckgo.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "tse1.mm.bing.net",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "upload.wikimedia.org",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.goodfreephotos.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "via.placeholder.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "s3-ap-southeast-1.amazonaws.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "dummyimage.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "d5ibtax54de3q.cloudfront.net",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "kickavenue-assets.s3.amazonaws.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname:
          "kickavenue-media-assets-staging.s3.ap-southeast-1.amazonaws.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "d1ic3o7t1o73go.cloudfront.net",
        pathname: "/**",
      },
      {
        protocol: "http",
        hostname: "example.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "example.com",
        pathname: "/**",
      },
    ],
  },
  transpilePackages: ["@kickavenue/ui"],
  modularizeImports: {
    "@kickavenue/ui": {
      transform: "@kickavenue/ui/dist/{{member}}",
    },
  },
}

export default nextConfig
