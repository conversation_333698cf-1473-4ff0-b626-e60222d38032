const production = process.env.ENV === "production"

const connectSrc = [
  "'self'",
  "https://*.googletagmanager.com",
  "https://*.clarity.ms",
  "https://www.google-analytics.com",
  "https://*.browser-intake-datadoghq.com",
  "*.sendbird.com",
  "wss://*.sendbird.com",
  "*.googleapis.com",
  "*.google.com",
  "https://*.gstatic.com",
  "blob:",
  "data:",
  "https://*.cloudflare.com",
  "*.midtrans.com",
  "*.kickavenue.com",
  "https://*.amazonaws.com",
  "https://*.amazoncognito.com",
  "localhost:3001",
  production ? "" : "http://localhost:8081",
]

const imgSrc = [
  "'self'",
  "blob:",
  "data:",
  "*.googleapis.com",
  "*.google.com",
  "*.googleusercontent.com",
  "*.midtrans.com",
  "*.cloudfront.net",
  "*.unsplash.com",
  "images.unsplash.com",
]

const scriptSrcElem = [
  "'self'",
  "'unsafe-inline'",
  "https://*.googletagmanager.com",
  "https://*.clarity.ms",
  "*.googleapis.com",
  "https://*.gstatic.com",
  "*.google.com",
  "*.googleusercontent.com",
  "*.midtrans.com",
]

const scriptSrc = [
  "'self'",
  production ? "" : "'unsafe-eval'",
  "'unsafe-inline'",
  "https://www.google-analytics.com",
  "https://*.gstatic.com",
  "https://*.googletagmanager.com",
  "https://*.clarity.ms",
  "*.googleapis.com",
  "*.google.com",
  "https://*.ggpht.com",
  "*.googleusercontent.com",
  "blob:",
  "*.midtrans.com",
]

const styleSrc = ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"]
const fontSrc = ["'self'", "https://fonts.gstatic.com"]

const csp = `
  script-src-elem ${scriptSrcElem.join(" ")};
  script-src ${scriptSrc.join(" ")};
  style-src ${styleSrc.join(" ")};
  font-src ${fontSrc.join(" ")};
  connect-src ${connectSrc.join(" ")};
  img-src ${imgSrc.join(" ")};
  worker-src 'self' blob:;
`
  .replace(/\s{2,}/g, " ")
  .trim()

export default csp
