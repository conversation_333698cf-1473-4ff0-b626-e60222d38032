include:
  - project: "kick-avenue/ka-deployment-tool"
    file: ".gitlab/templates/constant.gitlab-ci.yml"
    ref: main
  - project: "kick-avenue/infrastructure"
    file: ".gitlab/templates/constant.gitlab-ci.yml"
    ref: main

variables:
  ECR_REPOSITORY: 074400870776.dkr.ecr.ap-southeast-3.amazonaws.com/$CI_PROJECT_NAME

stages:
  - test
  - build
  - deploy

.anchor:
  # ── Rules ─────────────────────────────────────────────────────────────
  .common-ifs:
    on-merge-requests: &on-mr
      rules:
        - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    on-staging: &on-staging
      variables: &env-var
        REGISTRY_URL: 074400870776.dkr.ecr.ap-southeast-1.amazonaws.com
        PROJECT_NAME: marketplace-web
      environment: &env-staging
        name: staging
        action: prepare
        url: https://marketplace-stag.kickavenue.com/
      rules:
        - if: $CI_PIPELINE_SOURCE == 'schedule'
          when: never
        - if: $CI_COMMIT_TAG =~ /^\d+\.\d+\.\d+\-beta$/
    on-prod: &on-prod
      variables:
        !!merge <<: *env-var
      environment: &env-prod
        name: prod
        action: prepare
        url: https://marketplace.kickavenue.com/
      rules:
        - if: $CI_PIPELINE_SOURCE == 'schedule'
          when: never
        - if: $CI_COMMIT_TAG =~ /^\d+\.\d+\.\d+\-stable$/
  .images:
    kaniko-image: &kaniko-img
      image:
        name: gcr.io/kaniko-project/executor:v1.23.0-debug
        entrypoint: [""]
    node-img: &node-img
      image: node:18.20.4-alpine
    alpine-img: &alpine-img
      image: alpine:3.20.2
  pnpm-cache: &pnpm-cache
    before_script:
      - corepack enable
      - corepack prepare pnpm@9.7.1 --activate
      - pnpm config set store-dir .pnpm-store-alpine
      - pnpm config set -- //$KICK_NPM_REGISTRY_URL:_authToken $KICK_NPM_REGISTRY_TOKEN

    cache:
      key:
        files:
          - pnpm-lock.yaml
          - package.json
        prefix: alpine
      paths:
        - .pnpm-store-alpine
      policy: pull-push

#          ╭──────────────────────────────────────────────────────────╮
#          │                           TEST                           │
#          ╰──────────────────────────────────────────────────────────╯
lint:
  !!merge <<: *on-mr
  !!merge <<: *node-img
  !!merge <<: *pnpm-cache
  stage: test
  script:
    - pnpm install
    - pnpm fix

#          ╭──────────────────────────────────────────────────────────╮
#          │                          BUILD                           │
#          ╰──────────────────────────────────────────────────────────╯

build:staging: &build-docker
  !!merge <<: *on-staging
  !!merge <<: *kaniko-img
  stage: build
  cache:
    key: "kaniko-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - .kaniko-cache/
  script:
    # Set Path
    - PATH=$PATH:/kaniko:/busybox
    # Setup ECR for kaniko
    - mkdir -p /root/.aws
    - echo $AWS_CREDENTIAL_BASE64 | base64 -d > /root/.aws/credentials
    - |
      cat << EOF > /kaniko/.docker/config.json      
      {
        "credHelpers": {
          "$REGISTRY_URL": "ecr-login"
        }
      }
      EOF
    # Debug DOCKER_BUILD_ARGS
    - echo "=== Debugging DOCKER_BUILD_ARGS ==="
    - |
      if [[ -n "$DOCKER_BUILD_ARGS" ]]; then
        echo "DOCKER_BUILD_ARGS variable exists: YES"
        echo "DOCKER_BUILD_ARGS length: ${#DOCKER_BUILD_ARGS}"
        echo "DOCKER_BUILD_ARGS content (first 100 chars): ${DOCKER_BUILD_ARGS:0:100}"
      else
        echo "DOCKER_BUILD_ARGS variable exists: NO"
        echo "DOCKER_BUILD_ARGS length: 0"
        echo "DOCKER_BUILD_ARGS content (first 100 chars): "
      fi
    - echo "All environment variables containing DOCKER:"
    - env | grep -i docker || echo "No DOCKER variables found"
    - echo "=== End Debug ==="
    - cp $DOCKER_BUILD_ARGS .env.BUILD_ARGS
    # Create kaniko cache directory
    - mkdir -p .kaniko-cache
    # Build and push
    - IMAGE_URI=$REGISTRY_URL/$PROJECT_NAME:$CI_COMMIT_TAG
    - IMAGE_CACHE=$REGISTRY_URL/$PROJECT_NAME
    - |
      executor --compression-level 3 --cache=true \
        --build-arg "KICK_NPM_REGISTRY_URL=$KICK_NPM_REGISTRY_URL" \
        --build-arg "KICK_NPM_REGISTRY_TOKEN=$KICK_NPM_REGISTRY_TOKEN" \
        --cache-repo $IMAGE_CACHE \
        --cache-dir .kaniko-cache \
        --destination $IMAGE_URI \
        --context .

#          ╭──────────────────────────────────────────────────────────╮
#          │                          DEPLOY                          │
#          ╰──────────────────────────────────────────────────────────╯
deploy:staging: &deploy
  !!merge <<: *alpine-img
  !!merge <<: *on-staging
  stage: deploy
  script:
    - apk add --no-cache git curl bash
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "ci-marketplace"

    - if [[ -z "$GIT_PAT" ]]; then echo "GIT_PAT is not set"; exit 1; fi
    - git config --global url."https://${GIT_USER}:${GIT_PAT}@gitlab.kickavenue.com/".insteadOf "https://gitlab.kickavenue.com/"

    - if [[ -z "$INFRA_REPO_TOKEN" ]]; then INFRA_REPO_TOKEN=$GIT_PAT; fi
    - git config --global url."https://${GIT_USER}:${INFRA_REPO_TOKEN}@gitlab.kickavenue.com/".insteadOf "https://gitlab.kickavenue.com/"
    - git clone https://gitlab.kickavenue.com/kick-avenue/infrastructure.git

    # Install kustomize
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
    - mv kustomize infrastructure/app_manifest/$CI_ENVIRONMENT_NAME
    - cd infrastructure/app_manifest/$CI_ENVIRONMENT_NAME
    - ./kustomize edit set image "$REGISTRY_URL/$PROJECT_NAME=:$CI_COMMIT_TAG"
    - git commit -a -m "Update $CI_ENVIRONMENT_NAME marketplace to $CI_COMMIT_TAG"
    - git push origin main
  environment:
    name: staging
    action: start
    url: https://marketplace-stag.kickavenue.com/

build:prod:
  stage: build
  extends:
    - .image-ka-deployment-tool # from ka-deployment-tool
    - .rules-prod # from infrastructure
  before_script:
    - docker buildx use multi-builder || docker buildx create --name multi-builder --driver docker-container --use
    - docker buildx inspect --bootstrap
  script:
    - source auth-aws.sh prod
    - auth-eks.sh ka-prod-eks ap-southeast-3
    - get-secret-as-env.sh marketplace-config kickavenue-app > .env.BUILD_ARGS
    - auth-docker-to-ecr.sh $ECR_REPOSITORY ap-southeast-3
    - >
      docker buildx build -t $ECR_REPOSITORY:$CI_COMMIT_TAG
      --build-arg "KICK_NPM_REGISTRY_URL=$KICK_NPM_REGISTRY_URL"
      --build-arg "KICK_NPM_REGISTRY_TOKEN=$KICK_NPM_REGISTRY_TOKEN"
      --cache-from type="registry,ref=$ECR_REPOSITORY:cache,mode=max"
      --cache-to type="registry,ref=$ECR_REPOSITORY:cache,mode=max"
      --platform linux/amd64
      --push
      --progress plain
      .

deploy:prod:
  stage: deploy
  extends:
    - .image-ka-deployment-tool # from ka-deployment-tool
    - .rules-prod # from infrastructure
  environment:
    name: production
    action: start
  script:
    - source auth-aws.sh prod
    - auth-eks.sh ka-prod-eks ap-southeast-3
    # update deployment tag
    - kubectl set image -n kickavenue-app deployment/marketplace-deployment marketplace-deployment=$ECR_REPOSITORY:$CI_COMMIT_TAG
