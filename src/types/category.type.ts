export interface TCategory {
  id: number
  name?: string
  parent?: TCategory
  complementaryId?: number[]
  sequence?: number
  isActive?: boolean
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
}

export interface TCategoryFilter {
  name?: string
  page?: number
  pageSize?: number
  type?: ECategoryType
  parentID?: number
}

export interface TGetAllCategoryResponse {
  content: TCategory[]
  page: number
  totalPages: number
}

export enum ECategoryType {
  Category = "CATEGORY",
  SubCategory = "SUBCATEGORY",
  All = "ALL",
}
