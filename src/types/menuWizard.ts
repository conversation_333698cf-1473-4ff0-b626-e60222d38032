import { IAuditable } from "./apiResponse.type"
import { TBrandItem } from "./brand.type"
import { ELocationPageType, IRedirectionRule } from "./misc.type"
import { TStripePrice } from "./stripe.type"

export enum EMenuWizardContentType {
  Banner = "BANNER",
  Collection = "COLLECTION",
  ShortcutMenu = "SHORTCUT_MENU",
  Raffle = "RAFFLE",
}

export interface IMenuWizard extends IAuditable {
  id: number
  title: string
  pageType: ELocationPageType
  menuDefault: boolean
  startTime: string
  endTime: string
  menuWizardSections: IMenuWizardSection[]
}

export interface IMenuWizardSection extends IAuditable {
  id: number
  menuWizardId: number
  contentType: EMenuWizardContentType
  sequence: number
  backgroundColor: string
  title: string
  description: string
  actionTitle: string
  contentId: number
  image: string
  keyword: string
  sectionContent: ISectionContent[] | null
}

export interface ISectionContent extends IAuditable {
  id: number
  name: string
  startTime: string
  endTime: string
  description: string
  isActive: boolean
  items: ISectionContentItem[]
  imagePortrait: string
  imageLandscape: string
  redirectionRule: IRedirectionRule
  type?: string
  value?: string
  defaultSort?: string

  title?: string
  subtitle?: string
  platform?: string
  locationPage?: string
  excludedItemId?: string
  slug?: string
  sellerId?: string

  redirectUrl?: string
  imageWeb?: string
  imageMobile?: string

  // banner and termsAndConditions need for remove
  banner: string
  termsAndConditions: string
}

export interface ISectionContentItem extends IAuditable {
  id: number
  name: string
  skuCode: string
  description: string
  colorway: string
  gender: string
  underRetail?: boolean
  retailPrice: TStripePrice
  lowestPrice: TStripePrice
  brand: TBrandItem
  countryId: number
  releaseDate: string
  tag: string
  weight: number
  dimension: string
  sizeChartId: number
  marketplaceLink: string
  isAutoScrape: boolean
  commissionFee: number
  isActive: boolean
  isAddOn: boolean
  isVoucherApplicable: boolean
  isReceiveSellRequest: boolean
  isCanMakeOffer: boolean
  isHotItem: boolean
  isNonPurchaseable: boolean
  images: string[]
  seoTitleIna: string
  seoKeywordIna: string[] | null
  seoDescriptionIna: string
  seoTitleEng: string
  seoKeywordEng: string[] | null
  seoDescriptionEng: string
  category?: TBrandItem
  subCategory?: TBrandItem
}
