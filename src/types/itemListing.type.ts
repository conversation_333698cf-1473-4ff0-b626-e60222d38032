import { TBrandItem } from "./brand.type"
import { TListing } from "./listing.type"
import { TSize } from "./size.type"

export interface TItemListing {
  id: number
  size: TSize
  brand: TBrandItem
  strikeThroughPrice?: number
  lowestAsk: number
  isConsignment?: boolean
  skuCode: string
  name: string
  listings: TListing

  // temporary fix because missing 'n' from API response
  isConsigment?: boolean
}

export enum EShippingType {
  Express = "Express",
  Standard = "Standard",
  PreOrder = "Pre-Order",
}
