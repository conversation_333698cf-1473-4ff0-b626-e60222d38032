import { TBrandItem } from "./brand.type"
import { TCategory } from "./category.type"
import { TCountry } from "./country.type"
import { TItemListing } from "./itemListing.type"
import { TItemSummary } from "./itemSummary.type"
import { TSize } from "./size.type"
import { TStripePrice } from "./stripe.type"

export interface TProduct {
  content: Product[]
}
export interface Product {
  id: number
  skuCode: string
  sku?: string
  condition?: "NEW" | "USED"
  name: string
  brands?: TBrandItem[]
  brandIds: number[]
  category?: TCategory
  country?: TCountry
  listings?: TItemListing[]
  itemSummary?: TItemSummary
  itemPriceSummary?: TItemListing[]
  itemListing?: TItemListing
  categoryId?: number
  subCategoryId?: number
  description?: string
  colorway?: string
  gender?: string
  retailPrice?: TStripePrice
  countryId?: number
  releaseDate?: string
  tag?: string
  weight?: number
  dimension?: string
  sizeChartId?: number
  size?: TSize
  marketplaceLink?: string
  isAutoScrape?: boolean
  commissionFee?: number
  isActive?: boolean
  isAddOn?: boolean
  isVoucherApplicable?: boolean
  isReceiveSellRequest?: boolean
  isCanMakeOffer?: boolean
  isHotItem?: boolean
  isNonPurchaseable?: boolean
  images?: string[]
  image?: string
  seoTitleIna?: string
  seoKeywordIna?: string | null
  seoDescriptionIna?: string
  seoTitleEng?: string
  seoKeywordEng?: string | null
  seoDescriptionEng?: string
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
  highestOffer?: THighestOffer[]
  lowestPrice?: TStripePrice | null
  lowestAsk?: TStripePrice | null
  wishlistId?: number
  sizeAvailability?: {
    sizes: TSize[]
  }
}

export interface TSimilarProduct extends TItemListing {
  product: Product
}

export interface TFilterProducts {
  name?: string
  search?: string
  brandName?: string | string[]
  page?: number
  pageSize?: number
  isAddOn?: boolean
  subcategoryID?: number[]
  brandID?: number[]
  categoryID?: number[]
  sizeID?: number[]
  gender?: string[]
  categoryName?: string[]
  subcategoryName?: string[]
  shippingMethod?: string[]
  condition?: string[]
  priceHigh?: number
  priceLow?: number
  sortBy?: string
  ids?: number[]
  collectionSlug?: string

  // handle camelCase filter key ID
  subcategoryId?: number[]
  brandId?: number[]
  categoryId?: number[]
  sizeId?: number[]
}

export type TProductCondition =
  | "express"
  | "express99"
  | "standard"
  | "standard99"
  | "preOrder"

export interface THighestOffer {
  size?: TSize
  highestOffer: TStripePrice | null
}

export type TProductWishlist = Record<number, number> | null | undefined
export type TProductWishlistCb = (
  wishlistIds: number[],
  product: Product,
) => void

export enum TProductFilterKey {
  Category = "category",
  CategoryId = "categoryID",
  Brand = "brand",
  BrandId = "brandID",
  SubCategory = "subcategory",
  SubCategoryId = "subcategoryID",
  SizeId = "sizeID",
  Size = "size",
  PriceRange = "priceRange",
  SortBy = "sortBy",
}

export enum ProductInfoType {
  Vintage = "vintage",
  UnderRetail = "under-retail",
  OnlyOneLeft = "only-one-left",
  None = "none",
}

export type PriceType = TStripePrice | number | null
