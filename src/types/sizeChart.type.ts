import { TListParams } from "./apiResponse.type"
import { TBrandItem } from "./brand.type"
import { TCategory } from "./category.type"

export interface TSizeChart {
  content: SizeChart[]
  first: boolean
  last: boolean
  page: number
  pageSize: number
  sort: any[]
  totalSize: number
  totalPages: number
}

export interface SizeChart {
  id: number
  name: string
  brandId: number
  categoryId: number
  gender: string
  size: Size[]
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
  brand?: TBrandItem
  category?: TCategory
}
export interface Isize {
  label: string
  quantity: number
  id: string
  price: number
}
export interface Size {
  id: number
  sizeChartId: number
  us: string
  eu: string
  uk: string
  cm: string
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
}

export type TTransformedSizes = Record<number, Size>

export interface TSizeChartFilter extends TListParams {
  brandId?: number
}

export interface TUniqueSize {
  label: string
  us: string
  eu?: string
  uk?: string
  cm?: string
  gender?: string[]
  id: number[]
  sizeChartId: number[]
  sizeIds: number[]
}

export enum ESizeRegion {
  US = "us",
  EU = "eu",
  UK = "uk",
  CM = "cm",
}

export enum ESizeGender {
  MEN = "MEN",
  WOMEN = "WOMEN",
  KID = "KID",
}

export interface TUniqueSizeFilter extends TListParams {
  id: string
  name: string
  category: string
  region: ESizeRegion
  gender: ESizeGender
}
