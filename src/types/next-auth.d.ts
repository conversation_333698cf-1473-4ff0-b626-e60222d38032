import "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user?: User | null
    error?: string | null
  }

  interface User {
    id: string
    email: string
    name?: string | null
    accessToken?: string
    refreshToken?: string
    accessTokenExpires?: number
    accessTokenExpiresIn?: number
    provider?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    email: string
    name?: string | null
    accessToken?: string
    refreshToken?: string
    accessTokenExpires?: number
    accessTokenExpiresIn?: number
  }
}
