export interface TMember {
  id: number
  recipient: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  gender: string
  birthOfDate: string
  countryId: number
  provinceId: number
  regionId: number
  sellerPoint: number
  referralCode: string
  countryName: string
  provinceName: string
  image: string
  isActive: boolean
  isSellerOnVacation: boolean
}

export enum TMemberAuthStatus {
  Authenticated = "authenticated",
  Unauthenticated = "unauthenticated",
  Loading = "loading",
}

export interface IMemberUpdateUserLocationPayload {
  countryId: number
  provinceId: number | null
}
