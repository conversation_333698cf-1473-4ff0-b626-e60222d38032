import { IAuditable } from "./apiResponse.type"
import { ELocationPageType, EPlatformType, IRedirectionRule } from "./misc.type"

export interface IPopupBannerParams {
  sortBy: string
  order: string
  platform: EPlatformType[]
  locationPage: ELocationPageType
}

export interface IPopupBannerData extends IAuditable {
  id: number
  title: string
  subtitle: string
  description: string
  startTime: string
  endTime: string
  isActive: boolean
  platform: string
  image: string
  redirectionRule: IRedirectionRule
  locationPage: string

  // for future use
  imagePortrait?: string
  imageLandscape?: string
}
