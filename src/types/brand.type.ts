import { IAuditable } from "./apiResponse.type"

export interface TBrand {
  content: TBrandItem[]
  first: boolean
  last: boolean
  page: number
  pageSize: number
  sort: any[]
  totalSize: number
  totalPages: number
}

export interface TBrandItem extends IAuditable {
  id: number
  name: string
  backgroundImage: string
  logoImage: string
  isActive: boolean
  sequence?: number
  parent?: any

  // description and isPartner need for remove
  description: string
  isPartner: boolean
}

export interface TGetAllBrandsResponse {
  content: TBrand[]
  page: number
  totalPages: number
}

export interface TBrandFilter {
  page?: number
  pageSize?: number
  name?: string
}
