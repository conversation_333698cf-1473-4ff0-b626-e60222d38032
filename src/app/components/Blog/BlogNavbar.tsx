"use client"

import React, { useRef, useState } from "react"
import Link from "next/link"
import { Logo } from "@kickavenue/ui/components/icons"
import { Text } from "@kickavenue/ui/components"
import SearchInput from "@components/shared/SearchInput"

const BlogNavbar = () => {
  const [searchKeyword, setSearchKeyword] = useState("")
  const ref = useRef<HTMLInputElement>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(e.target.value)
  }

  return (
    <nav>
      <div className="flex justify-center bg-white md:sticky md:top-0 md:z-30">
        <div className="w-full max-w-[1440px]">
          <div className="hidden grid-cols-4 items-center gap-lg p-sm md:grid md:grid-cols-12 md:px-xxl">
            <div className="col-span-6 flex items-center gap-3">
              <Link href="/" className="!w-2/5">
                <div className="h-auto w-full">
                  <Logo />
                </div>
              </Link>
              <Text size="base" state="primary" type="bold">
                Blog & News
              </Text>
            </div>
            <div className="col-span-6 flex justify-end md:col-span-6">
              <SearchInput
                size="sm"
                placeholder="Search"
                onChange={handleInputChange}
                onClearText={() => setSearchKeyword("")}
                value={searchKeyword}
                ref={ref}
                className="!w-1/2"
              />
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default BlogNavbar
