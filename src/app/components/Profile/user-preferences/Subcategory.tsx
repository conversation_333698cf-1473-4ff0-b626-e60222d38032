"use client"

import { useMutation } from "@tanstack/react-query"
import React, { useState } from "react"
import { Button, CheckBox } from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import { preferenceApi } from "@infrastructure/api/preference"
import useGetCategoryAndMember from "@app/hooks/useGetCategoryAndMember"

const Subcategory = ({ handleNext }: { handleNext: () => void }) => {
  const [subCatergory, setSubCategoty] = useState<number[]>([])
  const router = useRouter()
  const handleCheckboxChange = (id: number) => {
    setSubCategoty((prevState) =>
      prevState.includes(id)
        ? prevState.filter((itemId) => itemId !== id)
        : [...prevState, id],
    )
  }
  const { data, isSuccess } = useGetCategoryAndMember()

  const mutation = useMutation({
    mutationFn: () => {
      return preferenceApi.create({
        memberId: data?.member?.id ?? 0,
        preference: [{ value: subCatergory.join(",") }],
        type: "SUBCATEGORY",
      })
    },
    onSuccess: () => {
      handleNext()
      router.refresh()
    },
  })

  if (!isSuccess || !data) return null

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-col gap-y-base p-lg">
          {data?.categories?.map((item) => (
            <CheckBox
              onChange={() => handleCheckboxChange(Number(item.id))}
              key={item.id}
              label={item.name || ""}
            />
          ))}
        </div>
      </div>
      <div className="sticky bottom-0 bg-white p-lg shadow-base">
        <Button
          onClick={() => mutation.mutate()}
          size="lg"
          variant="primary"
          className="!w-full"
          disabled={subCatergory.length === 0 || mutation.isPending}
        >
          Next
        </Button>
      </div>
    </div>
  )
}

export default Subcategory
