"use client"

import { <PERSON><PERSON>, <PERSON> } from "@kickavenue/ui/components"
import React, { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { preferenceApi } from "@infrastructure/api/preference"
import useGetSneakersSizeAndMember from "@app/hooks/useGetSneakersSizeAndMember"
import { TSize } from "types/size.type"

const SneakersSize = ({ handleNext }: { handleNext: () => void }) => {
  const [sneakersSize, setSneakersSize] = useState<string[]>([])
  const router = useRouter()
  const handleCheckboxChange = (size: TSize) => {
    const sizeValue = size.us
    setSneakersSize((prevState) =>
      prevState.includes(sizeValue)
        ? prevState.filter((itemId) => itemId !== sizeValue)
        : [...prevState, sizeValue],
    )
  }
  const { data, isSuccess } = useGetSneakersSizeAndMember()
  const mutation = useMutation({
    mutationFn: () => {
      return preferenceApi.create({
        memberId: data?.member?.id ?? 0,
        preference: [{ title: "Sneakers", value: sneakersSize.join(",") }],
        type: "SIZE",
      })
    },
    onSuccess: () => {
      handleNext()
      router.refresh()
    },
  })

  if (!isSuccess || !data) return null

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-wrap justify-center gap-base p-lg">
          {data.sneakersSize.map((size) => (
            <Chip
              isSelected={sneakersSize.includes(size)}
              key={size}
              size="lg"
              onClick={() => handleCheckboxChange({ us: size } as TSize)}
              className="cursor-pointer"
            >
              US {size}
            </Chip>
          ))}
        </div>
      </div>
      <div className="sticky bottom-0 bg-white p-lg shadow-base">
        <Button
          onClick={() => mutation.mutate()}
          size="lg"
          variant="primary"
          className="!w-full"
          disabled={sneakersSize.length === 0 || mutation.isPending}
        >
          Next
        </Button>
      </div>
    </div>
  )
}

export default SneakersSize
