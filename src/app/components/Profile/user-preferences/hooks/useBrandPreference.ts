import { useMutation } from "@tanstack/react-query"
import { useCallback, useEffect, useState } from "react"
import useFetchBrands from "@app/hooks/useFetchBrands"
import { preferenceApi } from "@infrastructure/api/preference"
import { TBrandItem } from "types/brand.type"
import { PreferenceType } from "types/preference.type"
import { useMemberStore } from "stores/memberStore"

interface UseBrandPreferenceProps {
  preferenceId?: number
  initialBrands?: TBrandItem[]
  onSuccessSave?: () => void
}

export default function useBrandPreference({
  preferenceId,
  initialBrands,
  onSuccessSave,
}: UseBrandPreferenceProps) {
  const { member } = useMemberStore()
  const [selectedBrands, setSelectedBrands] = useState<TBrandItem[]>(
    initialBrands ?? [],
  )
  const [search, setSearch] = useState<string>("")
  const {
    brands: data,
    isLoading,
    handleBrandSearch,
    setFilter,
  } = useFetchBrands({
    filter: { name: search, pageSize: 100 },
  })

  useEffect(() => {
    if (initialBrands) {
      setSelectedBrands(initialBrands)
    }
    setSearch("")
    setFilter({
      name: "",
      pageSize: 100,
    })
  }, [initialBrands, setFilter])

  const handleSearch = (search: string) => {
    setSearch(search)
    handleBrandSearch(search)
  }

  const saveMutation = useMutation({
    mutationFn: () => {
      if (preferenceId) {
        return preferenceApi.update(Number(preferenceId), {
          memberId: member?.id ?? 0,
          preference: [
            { value: selectedBrands.map((item) => item.id).join(",") },
          ],
          type: PreferenceType.Brand,
        })
      }
      return preferenceApi.create({
        memberId: member?.id ?? 0,
        preference: [
          { value: selectedBrands.map((item) => item.id).join(",") },
        ],
        type: PreferenceType.Brand,
      })
    },
    onSuccess: () => {
      onSuccessSave?.()
    },
  })

  const handleCheckboxChange = useCallback((item: TBrandItem) => {
    setSelectedBrands((prevState) =>
      prevState.map((item) => item.id).includes(item.id)
        ? prevState.filter((e) => e.id !== item.id)
        : [...prevState, item],
    )
  }, [])

  return {
    data,
    saveMutation,
    isLoading,
    search,
    selectedBrands,
    handleSearch,
    handleCheckboxChange,
  }
}
