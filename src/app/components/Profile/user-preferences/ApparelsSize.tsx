"use client"

import { <PERSON><PERSON>, <PERSON> } from "@kickavenue/ui/components"
import React, { useState } from "react"
import { useMutation, useQuery } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { SizeChartApiRepository } from "@infrastructure/repositories/sizeChartApiRepository"
import { preferenceApi } from "@infrastructure/api/preference"
import { useModalStore } from "stores/modalStore"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"

const ApparelsSize = () => {
  const ApparelsSizes = new SizeChartApiRepository()
  const Member = new MemberApiRepository()

  const [apparelsSize, setApparelsSize] = useState<string[]>([])
  const { setOpen } = useModalStore()
  const router = useRouter()
  const { data, isSuccess } = useQuery({
    queryKey: ["getApparelsSizeAndMember"],
    queryFn: async () => {
      const [apparelsSize, member] = await Promise.all([
        ApparelsSizes.getByUnique(false),
        Member.getByMy(),
      ])
      return { apparelsSize, member }
    },
  })

  const handleCheckboxChange = (id: string) => {
    setApparelsSize((prevState) =>
      prevState.includes(id)
        ? prevState.filter((itemId) => itemId !== id)
        : [...prevState, id],
    )
  }
  const mutation = useMutation({
    mutationFn: () => {
      return preferenceApi.create({
        memberId: data?.member?.id ?? 0,
        preference: [{ title: "Apparel", value: apparelsSize.join(",") }],
        type: "SIZE",
      })
    },
    onSuccess: () => {
      setOpen(false)
      router.refresh()
    },
  })
  if (!isSuccess || !data) return
  return (
    <>
      <div className="flex flex-wrap justify-center gap-base p-lg">
        {data.apparelsSize.map((size: string) => (
          <Chip
            key={size}
            size="lg"
            isSelected={apparelsSize.includes(size)}
            onClick={() => handleCheckboxChange(size)}
            className="cursor-pointer"
          >
            {size}
          </Chip>
        ))}
      </div>
      <div className="p-lg shadow-base">
        <Button
          onClick={() => {
            mutation.mutate()
          }}
          size="lg"
          variant="primary"
          className="!w-full"
          disabled={apparelsSize.length === 0 || mutation.isPending}
        >
          Save
        </Button>
      </div>
    </>
  )
}

export default ApparelsSize
