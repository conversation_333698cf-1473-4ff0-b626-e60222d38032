"use client"

import { Divide<PERSON>, <PERSON><PERSON>, IconCloseOutline } from "@kickavenue/ui/components"
import React, { useState } from "react"
import { userPreferencesFlow } from "@app/components/Profile/user-preferences/user-preferences-utils"
import Modal from "@app/components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import Brand from "@app/components/Profile/user-preferences/Brand"
import Subcategory from "@app/components/Profile/user-preferences/Subcategory"
import SneakersSize from "@app/components/Profile/user-preferences/SneakersSize"
import ApparelsSize from "@components/Profile/user-preferences/ApparelsSize"
import { ModalConstant } from "@constants/modal"

const FormUserPrefernces = () => {
  const { setOpen } = useModalStore()
  const [currentStep, setCurrentStep] = useState("subcategory")

  const handleNext = () => {
    const nextStep = userPreferencesFlow[currentStep].next
    if (nextStep) {
      setCurrentStep(nextStep)
    }
  }

  const renderCategoryFlow = (categoryStep: string) => {
    if (currentStep === "subcategory") {
      return <Subcategory handleNext={handleNext} />
    }
    if (categoryStep === "brand") {
      return <Brand handleNext={handleNext} />
    }
    if (categoryStep === "sneakersSize") {
      return <SneakersSize handleNext={handleNext} />
    }
    if (categoryStep === "apparelsSize") {
      return <ApparelsSize />
    }
  }
  return (
    <Modal modalId={ModalConstant.MODAL_IDS.PREFERENCES_CREATE}>
      <div className="flex justify-between p-md">
        <div className="flex grow justify-center text-right">
          <Heading heading="5" textStyle="bold">
            {userPreferencesFlow[currentStep].name}
          </Heading>
        </div>
        <IconCloseOutline
          onClick={() => setOpen(false)}
          className="scale-150 cursor-pointer"
        />
      </div>
      <Divider orientation="horizontal" />
      <div className="max-h-[496px] overflow-scroll">
        {renderCategoryFlow(currentStep)}
      </div>
    </Modal>
  )
}
export default FormUserPrefernces
