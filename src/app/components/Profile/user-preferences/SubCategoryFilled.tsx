import React from "react"
import { But<PERSON> } from "@kickavenue/ui/dist/src/components"
import { TPreferences } from "types/preference.type"
import { TPaginatedData } from "types/apiResponse.type"
import { useModalStore } from "stores/modalStore"
import useGetCategoryAndMember from "@app/hooks/useGetCategoryAndMember"

import MultiSelect from "../../shared/Form/MultiSelect"

import { filterSubCategory } from "./user-preferences-utils"

const SubCategoryFilled = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  const { setOpen } = useModalStore()
  const { data } = useGetCategoryAndMember()

  const options = data?.categories
    ? filterSubCategory(preference, data.categories)
    : []
  return (
    <div className="flex items-start">
      <MultiSelect
        label="Subcategory"
        items={options}
        selected={options}
        setSelected={() => {}}
      />
      <Button
        size="sm"
        variant="link"
        onClick={() => setOpen(true, "edit-subCategory")}
      >
        Edit
      </Button>
    </div>
  )
}

export default SubCategoryFilled
