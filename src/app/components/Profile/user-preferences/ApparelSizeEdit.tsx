import {
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  Heading,
  IconCloseOutline,
} from "@kickavenue/ui/components"
import React, { useEffect, useState, useCallback, memo } from "react"
import { useMutation } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { preferenceApi } from "@infrastructure/api/preference"
import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { TPaginatedData } from "types/apiResponse.type"
import { TPreferences } from "types/preference.type"
import useGetApparelSizeAndMember from "@app/hooks/useGetApparelSizeAndMember"
import { ModalConstant } from "@constants/modal"

import { filterSneakersSize } from "./user-preferences-utils"

const ApparelSizeModalContent = ({
  formattedSizes,
  apparelsSize,
  handleChipClick,
  handleClose,
  mutation,
}: {
  formattedSizes: string[]
  apparelsSize: string[]
  handleChipClick: (size: string) => void
  handleClose: () => void
  mutation: any
}) => (
  <div className="flex h-full max-h-[calc(100vh-2rem)] flex-col">
    <div className="flex justify-between p-md">
      <div className="flex grow justify-center text-right">
        <Heading heading="5" textStyle="bold">
          Apparel Size
        </Heading>
      </div>
      <IconCloseOutline
        onClick={handleClose}
        className="scale-150 cursor-pointer"
      />
    </div>
    <Divider orientation="horizontal" />
    <div className="flex-1 overflow-y-auto">
      <div className="flex flex-wrap justify-center gap-base p-lg">
        {formattedSizes.map((size) => {
          const sizeKey = String(size)
          return (
            <Chip
              isSelected={apparelsSize.includes(sizeKey)}
              key={sizeKey}
              size="lg"
              onClick={() => handleChipClick(sizeKey)}
              className="cursor-pointer"
            >
              {sizeKey}
            </Chip>
          )
        })}
      </div>
    </div>
    <div className="flex justify-end gap-base p-lg">
      <Button
        variant="link"
        onClick={handleClose}
        disabled={mutation.isPending}
      >
        Cancel
      </Button>
      <Button
        variant="primary"
        onClick={() => mutation.mutate()}
        disabled={mutation.isPending || apparelsSize.length === 0}
      >
        Save
      </Button>
    </div>
  </div>
)

const ApparelSizeEdit = memo(
  ({ preference }: { preference: TPaginatedData<TPreferences> }) => {
    const { data, isSuccess } = useGetApparelSizeAndMember()
    const router = useRouter()
    const { setOpen } = useModalStore()

    const { options } = filterSneakersSize(preference, "Apparel")
    const [apparelsSize, setApparelsSize] = useState<string[]>(options)

    const handleChipClick = useCallback((size: string) => {
      setApparelsSize((prevState) =>
        prevState.includes(size)
          ? prevState.filter((itemId) => itemId !== size)
          : [...prevState, size],
      )
    }, [])

    const handleClose = useCallback(() => {
      setOpen(false)
    }, [setOpen])

    const id = preference.content
      .filter((item) => item.type === "SIZE")
      .filter((item) =>
        item.preference.some((pref) => pref.title === "Apparel"),
      )
      .map((item) => item.id)
      .join("")

    const mutation = useMutation({
      mutationFn: () => {
        if (id) {
          return preferenceApi.update(Number(id), {
            memberId: data?.member?.id ?? 0,
            preference: [{ title: "Apparel", value: apparelsSize.join(",") }],
            type: "SIZE",
          })
        }
        return preferenceApi.create({
          memberId: data?.member?.id ?? 0,
          preference: [{ title: "Apparel", value: apparelsSize.join(",") }],
          type: "SIZE",
        })
      },
      onSuccess: () => {
        setOpen(false)
        router.refresh()
      },
    })

    useEffect(() => {
      if (!mutation.isSuccess) return
      setOpen(false)
      router.refresh()
    }, [mutation.isSuccess, router, setOpen])

    if (!isSuccess || !data) return null

    const formattedSizes = Array.isArray(data.apparelsSize)
      ? data.apparelsSize.map((size) => String(size).trim()).filter(Boolean)
      : []

    return (
      <Modal modalId={ModalConstant.MODAL_IDS.PREFERENCES_APPAREL_SIZE_EDIT}>
        <ApparelSizeModalContent
          formattedSizes={formattedSizes}
          apparelsSize={apparelsSize}
          handleChipClick={handleChipClick}
          handleClose={handleClose}
          mutation={mutation}
        />
      </Modal>
    )
  },
)

ApparelSizeEdit.displayName = "ApparelSizeEdit"

export default ApparelSizeEdit
