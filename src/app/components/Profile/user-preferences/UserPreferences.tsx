"use client"

import React from "react"
import EmptyUserPreferences from "@app/components/Profile/user-preferences/EmptyUserPreferences"
import UserPreferencesFilled from "@app/components/Profile/user-preferences/UserPreferencesFilled"
import useGetPreferences from "@app/hooks/useGetPreferences"

import FormUserPrefernces from "./FormUserPreferences"

const UserPreferences = () => {
  const { data: preferences, isLoading, isSuccess } = useGetPreferences()

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (isSuccess && preferences) {
    if (preferences.totalSize > 0) {
      return (
        <>
          <UserPreferencesFilled preference={preferences} />
          <FormUserPrefernces />
        </>
      )
    }
    return (
      <>
        <EmptyUserPreferences />
        <FormUserPrefernces />
      </>
    )
  }

  return (
    <>
      <EmptyUserPreferences />
      <FormUserPrefernces />
    </>
  )
}

export default UserPreferences
