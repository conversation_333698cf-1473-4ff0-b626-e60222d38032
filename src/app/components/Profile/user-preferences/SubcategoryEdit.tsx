"use client"

import { useMutation } from "@tanstack/react-query"
import React, { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  CheckBox,
  Divider,
  Heading,
  IconCloseOutline,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import Modal from "@app/components/shared/Modal"
import { preferenceApi } from "@infrastructure/api/preference"
import { useModalStore } from "stores/modalStore"
import { TPaginatedData } from "types/apiResponse.type"
import { TPreferences } from "types/preference.type"
import useGetCategoryAndMember from "@app/hooks/useGetCategoryAndMember"

const SubcategoryEdit = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  const { data, isSuccess } = useGetCategoryAndMember()

  const router = useRouter()
  const { setOpen } = useModalStore()
  const [subcategory, setSubcategory] = useState<string[]>([])
  const handleCheckboxChange = (id: number) => {
    setSubcategory((prevState) =>
      prevState.includes(String(id))
        ? prevState.filter((itemId) => itemId !== String(id))
        : [...prevState, String(id)],
    )
  }
  const id = preference.content
    .filter((item) => item.type === "SUBCATEGORY")
    .map((item) => item.id)
    .join("")
  const mutation = useMutation({
    mutationFn: () => {
      if (id) {
        return preferenceApi.update(Number(id), {
          memberId: data?.member?.id ?? 0,
          preference: [{ value: subcategory.join(",") }],
          type: "SUBCATEGORY",
        })
      }
      return preferenceApi.create({
        memberId: data?.member?.id ?? 0,
        preference: [{ value: subcategory.join(",") }],
        type: "SUBCATEGORY",
      })
    },
    onSuccess: () => {
      setOpen(false)
      router.refresh()
    },
  })
  useEffect(() => {
    if (!mutation.isSuccess) return
    setOpen(false)
    router.refresh()
  }, [mutation.isSuccess, router, setOpen])

  if (!isSuccess || !data) return null

  return (
    <Modal modalId="edit-subCategory">
      <div className="flex h-full max-h-[calc(100vh-2rem)] flex-col">
        <div className="flex justify-between p-md">
          <div className="flex grow justify-center text-right">
            <Heading heading="5" textStyle="bold">
              Sub Category
            </Heading>
          </div>
          <IconCloseOutline
            onClick={() => setOpen(false)}
            className="scale-150 cursor-pointer"
          />
        </div>
        <Divider orientation="horizontal" />
        <div className="flex-1 overflow-y-auto">
          <div className="flex flex-col gap-y-base p-lg">
            {data?.categories?.map((item) => (
              <CheckBox
                onChange={() => handleCheckboxChange(Number(item.id))}
                key={item.id}
                defaultChecked={subcategory.includes(String(item.id))}
                label={item.name || ""}
              />
            ))}
          </div>
        </div>
        <div className="sticky bottom-0 bg-white p-lg shadow-base">
          <Button
            onClick={() => mutation.mutate()}
            size="lg"
            variant="primary"
            className="!w-full"
            disabled={subcategory.length === 0 || mutation.isPending}
          >
            Save Changes
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default SubcategoryEdit
