import { TPaginatedData } from "types/apiResponse.type"
import { TPreferences } from "types/preference.type"
import { TItemOption } from "@kickavenue/ui/dist/src/components/ComboBox/ComboBox.type"
import { TCategory } from "types/category.type"

export interface Step {
  name: string
  next: keyof UserPrefencesFlow | null
}

export type UserPrefencesFlow = Record<string, Step>

export const userPreferencesFlow: UserPrefencesFlow = {
  subcategory: { name: "Subcategory", next: "brand" },
  brand: { name: "Brand", next: "sneakersSize" },
  sneakersSize: { name: "Sneakers Size", next: "apparelsSize" },
  apparelsSize: { name: "Apparels Size", next: null },
}

export const filterSubCategory = (
  preference: TPaginatedData<TPreferences>,
  categories: TCategory[],
): TItemOption[] => {
  const subCategoryIds = preference.content
    .filter((item) => item.type === "SUBCATEGORY")
    .flatMap((item) => item.preference.flatMap((pref) => pref.value.split(",")))
  return categories
    .filter((item) => subCategoryIds.includes(item.id.toString()))
    .map((item) => ({
      label: String(item.name),
      value: String(item.id),
    }))
}

export const filterSneakersSize = (
  preference: TPaginatedData<TPreferences>,
  title: "Sneakers" | "Apparel",
) => {
  const data = preference.content.filter(
    (item) =>
      item.type === "SIZE" &&
      item.preference.some((pref) => pref.title === title),
  )
  const options = data
    .map((item) => {
      const foundPreference = item.preference.find(
        (pref) => pref.title === title,
      )
      return foundPreference
        ? foundPreference.value.split(",").map((size) => size.trim())
        : []
    })
    .flat()
    .filter(Boolean)
  return { data, options }
}
