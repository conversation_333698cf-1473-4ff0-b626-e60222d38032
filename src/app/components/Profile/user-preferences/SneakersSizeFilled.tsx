import React from "react"
import { But<PERSON> } from "@kickavenue/ui/dist/src/components"
import { TPreferences } from "types/preference.type"
import { TPaginatedData } from "types/apiResponse.type"
import MultiSelect from "@components/shared/Form/MultiSelect"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

import { filterSneakersSize } from "./user-preferences-utils"

const SneakersSizeFilled = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  const { setOpen } = useModalStore()

  const getSneakersSize = () => {
    const { options } = filterSneakersSize(preference, "Sneakers")
    return options.map((size) => ({
      value: size,
      label: `US ${size}`,
    }))
  }

  const sneakersSizes = getSneakersSize()
  const selectedSizes = sneakersSizes.filter((size) => size.value)

  return (
    <div className="flex items-start">
      <MultiSelect
        label="Size"
        items={sneakersSizes}
        selected={selectedSizes}
        setSelected={() => {}}
        renderLabel={(item: { label: string }) => item.label}
      />
      <Button
        size="sm"
        variant="link"
        onClick={() =>
          setOpen(true, ModalConstant.MODAL_IDS.PREFERENCES_SNEAKERS_SIZE_EDIT)
        }
      >
        Edit
      </Button>
    </div>
  )
}

export default SneakersSizeFilled
