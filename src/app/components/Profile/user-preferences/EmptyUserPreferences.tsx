"use client"

import Empty from "@kickavenue/ui/components/Empty"
import { But<PERSON> } from "@kickavenue/ui/components"
import React from "react"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

const EmptyUserPreferences = () => {
  const { setOpen } = useModalStore()
  return (
    <div className="mt-32 flex h-full flex-col items-center">
      <Empty
        title="No Preferences Set"
        subText="Personalize your experience by adding your preferences."
        className="[&>img]:h-[250px] [&>img]:w-[294px]"
        actionButton={
          <Button
            onClick={() =>
              setOpen(true, ModalConstant.MODAL_IDS.PREFERENCES_CREATE)
            }
            size="md"
            variant="primary"
          >
            Add Preferences
          </Button>
        }
      />
    </div>
  )
}
export default EmptyUserPreferences
