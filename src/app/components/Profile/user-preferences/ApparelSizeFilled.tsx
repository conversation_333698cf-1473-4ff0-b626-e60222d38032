import React from "react"
import { But<PERSON> } from "@kickavenue/ui/components"
import { TPreferences } from "types/preference.type"
import { TPaginatedData } from "types/apiResponse.type"
import MultiSelect from "@components/shared/Form/MultiSelect"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

import { filterSneakersSize } from "./user-preferences-utils"

const ApparelSizeFilled = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  const { setOpen } = useModalStore()
  const getApparelSize = () => {
    const filterApparelSizeOptions = filterSneakersSize(
      preference,
      "Apparel",
    ).options
    if (filterApparelSizeOptions) {
      return filterApparelSizeOptions.map((item) => ({
        value: item ?? "",
        label: item,
      }))
    }
    return []
  }

  const handleEditClick = () => {
    setOpen(true, ModalConstant.MODAL_IDS.PREFERENCES_APPAREL_SIZE_EDIT)
  }

  return (
    <div className="flex items-start">
      <MultiSelect
        label="Size"
        items={getApparelSize()}
        selected={getApparelSize()}
        setSelected={() => {}}
      />
      <Button size="sm" variant="link" onClick={handleEditClick}>
        Edit
      </Button>
    </div>
  )
}

export default ApparelSizeFilled
