"use client"

import React from "react"
import Header from "@app/components/Profile/user-preferences/Header"
import { TPreferences } from "types/preference.type"
import { TPaginatedData } from "types/apiResponse.type"

import SubCategoryFilled from "./SubCategoryFilled"
import BrandFilled from "./BrandFilled"
import SneakersSizeFilled from "./SneakersSizeFilled"
import ApparelSizeFilled from "./ApparelSizeFilled"
import SubcategoryEdit from "./SubcategoryEdit"
import BrandEdit from "./BrandEdit"
import SneakersSizeEdit from "./SneakersSizeEdit"
import ApparelSizeEdit from "./ApparelSizeEdit"

const UserPreferencesFilled = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  return (
    <>
      <div className="flex flex-col gap-y-base">
        <Header heading="General" />
        <SubCategoryFilled preference={preference} />
        <BrandFilled preference={preference} />
      </div>
      <div className="mt-lg flex flex-col gap-y-base">
        <Header heading="Sneakers" />
        <SneakersSizeFilled preference={preference} />
      </div>
      <div className="mt-lg flex flex-col gap-y-base">
        <Header heading="Apparels" />
        <ApparelSizeFilled preference={preference} />
      </div>
      <SubcategoryEdit preference={preference} />
      <BrandEdit preference={preference} />
      <SneakersSizeEdit preference={preference} />
      <ApparelSizeEdit preference={preference} />
    </>
  )
}

export default UserPreferencesFilled
