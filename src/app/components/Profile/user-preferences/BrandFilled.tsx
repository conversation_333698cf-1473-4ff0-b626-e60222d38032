import React from "react"
import { Button } from "@kickavenue/ui/dist/src/components"
import { TPreferences } from "types/preference.type"
import { TPaginatedData } from "types/apiResponse.type"
import MultiSelect from "@components/shared/Form/MultiSelect"
import { useModalStore } from "stores/modalStore"
import useGetBrandAndMember from "@app/hooks/useGetBrandAndMember"
import { convertToItemList, Item } from "@utils/misc"
import { ModalConstant } from "@constants/modal"
import { TItemOption } from "@kickavenue/ui/dist/src/components/ComboBox/ComboBox.type"

const BrandFilled = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  const { setOpen } = useModalStore()
  const { data } = useGetBrandAndMember()
  const filterBrand = preference.content
    .filter((item) => item.type === "BRAND")
    .flatMap((item) => item.preference.flatMap((pref) => pref.value.split(",")))
  const brandOptions = data?.brand?.content.filter((item) => {
    return filterBrand.includes(item?.id.toString())
  })
  const options = convertToItemList(
    (brandOptions ?? []) as unknown as Item[],
    "id",
    "name",
  ) as TItemOption[]

  return (
    <div className="flex items-start">
      <MultiSelect
        label="Brand"
        items={options}
        selected={options}
        setSelected={() => {}}
      />
      <Button
        onClick={() =>
          setOpen(
            true,
            ModalConstant.MODAL_IDS.PREFERENCES_BRAND_EDIT,
            brandOptions,
          )
        }
        size="sm"
        variant="link"
      >
        Edit
      </Button>
    </div>
  )
}

export default BrandFilled
