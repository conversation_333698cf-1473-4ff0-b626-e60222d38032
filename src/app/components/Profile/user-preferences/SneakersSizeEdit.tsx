"use client"

import { useMutation } from "@tanstack/react-query"
import React, { useEffect, useState, useCallback, memo } from "react"
import {
  But<PERSON>,
  Chip,
  Divider,
  Heading,
  IconCloseOutline,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import Modal from "@app/components/shared/Modal"
import { preferenceApi } from "@infrastructure/api/preference"
import { useModalStore } from "stores/modalStore"
import { TPaginatedData } from "types/apiResponse.type"
import { TPreferences } from "types/preference.type"
import useGetSneakersSizeAndMember from "@app/hooks/useGetSneakersSizeAndMember"
import { ModalConstant } from "@constants/modal"

import { filterSneakersSize } from "./user-preferences-utils"

const SneakersSizeModalContent = ({
  formattedSizes,
  sneakersSize,
  handleChipClick,
  handleClose,
  mutation,
}: {
  formattedSizes: string[]
  sneakersSize: string[]
  handleChipClick: (size: string) => void
  handleClose: () => void
  mutation: any
}) => (
  <div className="flex h-full max-h-[calc(100vh-2rem)] flex-col">
    <div className="flex justify-between p-md">
      <div className="flex grow justify-center text-right">
        <Heading heading="5" textStyle="bold">
          Sneakers Size
        </Heading>
      </div>
      <IconCloseOutline
        onClick={handleClose}
        className="scale-150 cursor-pointer"
      />
    </div>
    <Divider orientation="horizontal" />
    <div className="flex-1 overflow-y-auto">
      <div className="flex flex-wrap justify-center gap-base p-lg">
        {formattedSizes.map((size) => {
          const sizeKey = String(size)
          return (
            <Chip
              isSelected={sneakersSize.includes(sizeKey)}
              key={sizeKey}
              size="lg"
              onClick={() => handleChipClick(sizeKey)}
              className="cursor-pointer"
            >
              {`US ${sizeKey}`}
            </Chip>
          )
        })}
      </div>
    </div>
    <div className="flex justify-end gap-base p-lg">
      <Button
        variant="link"
        onClick={handleClose}
        disabled={mutation.isPending}
      >
        Cancel
      </Button>
      <Button
        variant="primary"
        onClick={() => mutation.mutate()}
        disabled={mutation.isPending || sneakersSize.length === 0}
      >
        Save
      </Button>
    </div>
  </div>
)

const SneakersSizeEdit = memo(
  ({ preference }: { preference: TPaginatedData<TPreferences> }) => {
    const { data, isSuccess } = useGetSneakersSizeAndMember()
    const router = useRouter()
    const { setOpen } = useModalStore()

    const { options } = filterSneakersSize(preference, "Sneakers")
    const [sneakersSize, setSneakersSize] = useState<string[]>(options)

    const handleChipClick = useCallback((size: string) => {
      setSneakersSize((prevState) =>
        prevState.includes(size)
          ? prevState.filter((itemId) => itemId !== size)
          : [...prevState, size],
      )
    }, [])

    const handleClose = useCallback(() => {
      setOpen(false)
    }, [setOpen])

    const id = preference.content
      .filter((item) => item.type === "SIZE")
      .filter((item) =>
        item.preference.some((pref) => pref.title === "Sneakers"),
      )
      .map((item) => item.id)
      .join("")

    const mutation = useMutation({
      mutationFn: () => {
        if (id) {
          return preferenceApi.update(Number(id), {
            memberId: data?.member?.id ?? 0,
            preference: [{ title: "Sneakers", value: sneakersSize.join(",") }],
            type: "SIZE",
          })
        }
        return preferenceApi.create({
          memberId: data?.member?.id ?? 0,
          preference: [{ title: "Sneakers", value: sneakersSize.join(",") }],
          type: "SIZE",
        })
      },
      onSuccess: () => {
        setOpen(false)
        router.refresh()
      },
    })

    useEffect(() => {
      if (!mutation.isSuccess) return
      setOpen(false)
      router.refresh()
    }, [mutation.isSuccess, router, setOpen])

    if (!isSuccess || !data) return null

    const formattedSizes = Array.isArray(data.sneakersSize)
      ? data.sneakersSize
          .map((size) => {
            const sizeStr = String(size).trim()
            return sizeStr === "null" || sizeStr === "undefined"
              ? null
              : sizeStr
          })
          .filter((size): size is string => size !== null)
      : []

    return (
      <Modal modalId={ModalConstant.MODAL_IDS.PREFERENCES_SNEAKERS_SIZE_EDIT}>
        <SneakersSizeModalContent
          formattedSizes={formattedSizes}
          sneakersSize={sneakersSize}
          handleChipClick={handleChipClick}
          handleClose={handleClose}
          mutation={mutation}
        />
      </Modal>
    )
  },
)

SneakersSizeEdit.displayName = "SneakersSizeEdit"

export default SneakersSizeEdit
