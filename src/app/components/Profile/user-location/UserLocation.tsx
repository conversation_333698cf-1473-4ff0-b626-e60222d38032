"use client"

import { useQuery } from "@tanstack/react-query"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

import FormUserLocation from "./FormUserLocation"
import LoadingPageSkeleton from "@components/shared/Skeleton/LoadingPageSkeleton"

const UserLocation = () => {
  const memberRepository = new MemberApiRepository()

  const { data: member, isLoading: isLoadingMember } = useQuery({
    queryKey: [QueryKeysConstant.GET_MY_MEMBER],
    queryFn: () => memberRepository.getByMy(),
  })

  if (isLoadingMember) {
    return (
      <div className="flex min-h-[650px] flex-col items-center justify-center">
        <LoadingPageSkeleton />
      </div>
    )
  }

  if (!member) {
    return (
      <div className="flex min-h-[650px] flex-col items-center justify-center">
        <p>Failed to load data</p>
      </div>
    )
  }

  return <FormUserLocation member={member} />
}

export default UserLocation
