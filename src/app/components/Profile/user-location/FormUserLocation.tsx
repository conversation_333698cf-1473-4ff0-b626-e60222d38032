"use client"

import ButtonSave from "@components/shared/Form/ButtonSave"
import { useUserLocationMutation } from "./hooks/useUserLocationMutation"

import { Controller, FieldErrors, FormProvider, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import z from "zod"
import SelectCountry from "@components/shared/Select/SelectCountry"
import SelectProvince from "@components/shared/Select/SelectProvince"
import Text from "@kickavenue/ui/dist/src/components/Text/Text"
import LabeledInput from "@components/shared/LabeledInput"
import { TMember } from "types/member.type"
import useFetchCountryById from "@app/hooks/useFetchCountryById"
import useFetchProvinceById from "@app/hooks/useFetchProvinceById"

const formSchema = z.object({
  country: z
    .object({
      label: z.string(),
      value: z.string(),
    })
    .nullable()
    .refine((val) => {
      return val !== null && val !== undefined
    }, "Country is required")
    .optional(),
  province: z
    .object({
      label: z.string(),
      value: z.string(),
    })
    .nullable()
    .optional(),
})

type TFormUserLocation = z.infer<typeof formSchema>

const FormUserLocation = ({ member }: { member: TMember }) => {
  const { data: country } = useFetchCountryById(member.countryId)
  const { data: province } = useFetchProvinceById(member.provinceId)

  const form = useForm({
    values: {
      country: country
        ? { label: country.name, value: country.country.toString() }
        : undefined,
      province: province
        ? { label: province.name, value: province.id.toString() }
        : null,
    },
    resolver: zodResolver(formSchema),
  })

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = form

  const { mutate: updateUserLocation } = useUserLocationMutation({
    onSuccess: () => {
      console.log("User location updated successfully")
    },
    onError: (error) => {
      console.error("Failed to update user location:", error)
    },
  })

  const watchCountry = watch("country")

  const onValidForm = (data: TFormUserLocation) => {
    updateUserLocation({
      countryId: Number(data.country?.value || 0),
      provinceId: data.province?.value ? Number(data.province?.value) : null,
    })
  }

  const onInvalidForm = (errors: FieldErrors<TFormUserLocation>) => {
    console.log("errors form", errors)
    console.log("data form", form.getValues())
  }

  return (
    <FormProvider {...form}>
      <form
        onSubmit={handleSubmit(onValidForm, onInvalidForm)}
        className="flex h-full flex-col justify-between gap-y-base"
      >
        <div className="flex flex-col gap-y-base sm:gap-y-lg">
          <Controller
            control={control}
            name="country"
            render={({ field }) => (
              <LabeledInput label="Country">
                <SelectCountry
                  {...field}
                  menuPlacement="bottom"
                  placeholder="Select your country"
                  onChange={(e) => {
                    field.onChange(e)
                    form.setValue("province", null)

                    if (e === null) {
                      form.setValue("province", null)
                    }
                  }}
                />
                {errors.country && (
                  <Text type={"regular"} size={"xs"} state={"danger"}>
                    {errors.country?.message}
                  </Text>
                )}
              </LabeledInput>
            )}
          />

          <Controller
            control={control}
            name="province"
            render={({ field }) => (
              <LabeledInput label="Province/State" state="default">
                <SelectProvince
                  {...field}
                  menuPlacement="bottom"
                  placeholder="Select your province/state"
                  disabled={!watchCountry}
                  countryName={watchCountry?.label}
                />
                {errors.province && (
                  <Text type={"regular"} size={"xs"} state={"danger"}>
                    {errors.province?.message}
                  </Text>
                )}
              </LabeledInput>
            )}
          />
        </div>

        <ButtonSave />
      </form>
    </FormProvider>
  )
}

export default FormUserLocation
