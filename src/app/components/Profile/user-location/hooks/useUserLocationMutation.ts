import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useMemo } from "react"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { IMemberUpdateUserLocationPayload } from "types/member.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useToast from "@app/hooks/useToast"

interface UseUserLocationMutationProps {
  onSuccess?: () => void
  onError?: (error: unknown) => void
}

export const useUserLocationMutation = ({
  onSuccess,
  onError,
}: UseUserLocationMutationProps = {}) => {
  const memberRepository = useMemo(() => new MemberApiRepository(), [])
  const { setShowToast } = useToast()
  const queryClient = useQueryClient()

  const handleSuccess = () => {
    // Invalidate related queries
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_MY_MEMBER],
    })

    setShowToast(true, "User location updated successfully")
    onSuccess?.()
  }

  const handleError = (error: unknown) => {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to update user location"
    setShowToast(true, errorMessage, "danger")
    onError?.(error)
  }

  return useMutation({
    mutationFn: async (payload: IMemberUpdateUserLocationPayload) => {
      return memberRepository.updateUserLocation(payload)
    },
    onSuccess: handleSuccess,
    onError: handleError,
  })
}
