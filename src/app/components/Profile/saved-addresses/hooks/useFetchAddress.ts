import { useInfiniteQuery } from "@tanstack/react-query"
import { useEffect, useRef } from "react"
import { AddressApiRepository } from "@infrastructure/repositories/addressApiRepository"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { useAddressStore } from "stores/addressStore"
import { TPaginatedData } from "types/apiResponse.type"
import { TAddress } from "types/address.type"

export const useFetchAddress = () => {
  const { setAddresses, searchQuery } = useAddressStore()

  const Address = new AddressApiRepository()
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  
  const query = useInfiniteQuery({
    queryKey: [QueryKeysConstant.GET_MY_ADDRESS, searchQuery],
    initialPageParam: 0,
    queryFn: async ({ pageParam }) => {
      try {
        const resp = await Address.getByMy({
          page: pageParam,
          pageSize: 5,
          search: searchQuery,
        })
        setAddresses(resp.content)
        return resp
      } catch (error) {
        // Handle 404 error gracefully - user has no addresses
        
        // Return empty paginated data structure
        const emptyData: TPaginatedData<TAddress> = {
          content: [],
          first: true,
          last: true,
          page: pageParam,
          pageSize: 0,
          totalPages: 0,
          totalSize: 0,
        }
        
        setAddresses([])
        return emptyData
      }
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.page < lastPage.totalPages - 1) {
        return lastPage.page + 1
      }
      return null
    },
    retry: false, // Don't retry on error to prevent endless loop
  })

  const mappedData = query.data?.pages.flatMap((page) => {
    // If page.content is an array, return it directly
    if (Array.isArray(page.content)) {
      return page.content
      // If page.content is an object with a content property, return page.content.content
    } else if (
      page.content &&
      typeof page.content === "object" &&
      "content" in page.content &&
      Array.isArray((page.content as any).content)
    ) {
      return (page.content as any).content || []
    }
    return []
  })

  useEffect(() => {
    const handleScroll = () => {
      const container = scrollContainerRef.current
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container
        if (
          scrollTop + clientHeight >= scrollHeight - 2 &&
          query.hasNextPage &&
          !query.isFetchingNextPage
        ) {
          query.fetchNextPage()
        }
      }
    }

    const container = scrollContainerRef.current
    container?.addEventListener("scroll", handleScroll)
    return () => container?.removeEventListener("scroll", handleScroll)
  }, [query, query.hasNextPage, query.isFetchingNextPage])
  
  return {
    isLoading: query.isLoading,
    addresses: mappedData,
    isSuccess: query.isSuccess,
    scrollContainerRef,
    searchQuery,
  }
}
