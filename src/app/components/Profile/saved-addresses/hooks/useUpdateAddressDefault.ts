import { useMutation, useQueryClient } from "@tanstack/react-query"
import { UpdateAddressPrimary } from "@application/usecases/updateAddressPrimary"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { AddressApiRepository } from "@infrastructure/repositories/addressApiRepository"
import { TAddress } from "types/address.type"

export const useUpdateAddressDefault = () => {
  const queryClient = useQueryClient()

  const updateAddressDefault = async (props: {
    address?: TAddress
    isPrimary?: boolean
  }) => {
    const { address, isPrimary } = props
    if (!address || !address.id) return
    const r = new AddressApiRepository()
    const u = new UpdateAddressPrimary(r)
    const data = await u.execute(address, isPrimary ?? false)
    return data
  }

  return useMutation({
    mutationFn: updateAddressDefault,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_MY_ADDRESS],
      })
    },
  })
}
