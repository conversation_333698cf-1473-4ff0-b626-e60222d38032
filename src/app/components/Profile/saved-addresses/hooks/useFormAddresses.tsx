/* eslint-disable max-lines-per-function */
import { useEffect, useState, useRef } from "react"
import { getDistrictsByCity } from "@infrastructure/actions/district"
import { getPostalCodesByDistrict } from "@infrastructure/actions/postalCode"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"

export interface TItemOption {
  label: string
  value: string
  icon?: React.ReactNode
}

export interface InitialValue {
  country?: TItemOption
  countryCode?: TItemOption
  city?: TItemOption
  province?: TItemOption
  district?: TItemOption
}

interface UseFormAddressesProps {
  initialValue: InitialValue
  provinces: TProvince[]
  cities: TCity[]
  address?: {
    zipCode?: string
    addressDetail?: string
  }
  isEditMode?: boolean
  state?: any
}

export const useFormAddresses = ({
  initialValue,
  provinces,
  cities,
  address,
  isEditMode = false,
  state,
}: UseFormAddressesProps) => {
  // Track if component is mounted to prevent state updates after unmount
  const isMounted = useRef(true)

  // State for form fields
  const [country, setCountryState] = useState<TItemOption | null>(
    initialValue?.country ?? null,
  )
  const [countryCode, setCountryCode] = useState<TItemOption | null>(
    initialValue?.countryCode ?? null,
  )
  const [province, setProvinceState] = useState<TItemOption | null>(
    initialValue?.province ?? null,
  )
  const [city, setCityState] = useState<TItemOption | null>(
    initialValue?.city ?? null,
  )
  const [district, setDistrictState] = useState<TItemOption | null>(
    initialValue?.district ?? null,
  )

  const [filteredProvinces, setFilteredProvinces] =
    useState<TProvince[]>(provinces)
  const [filteredCities, setFilteredCities] = useState<TCity[]>(cities)
  const [filteredDistricts, setFilteredDistricts] = useState<TItemOption[]>([])
  const [filteredPostalCodes, setFilteredPostalCodes] = useState<TItemOption[]>(
    [],
  )

  const [isProvinceEnabled, setIsProvinceEnabled] = useState(
    Boolean(initialValue?.country),
  )
  const [isCityEnabled, setIsCityEnabled] = useState(
    Boolean(initialValue?.province),
  )
  const [isDistrictEnabled, setIsDistrictEnabled] = useState(
    Boolean(initialValue?.city),
  )
  const [isZipCodeEnabled, setIsZipCodeEnabled] = useState(
    Boolean(initialValue?.district),
  )
  const [isPostalCodeFreeText, setIsPostalCodeFreeText] = useState(false)

  const [postalCode, setPostalCodeState] = useState<TItemOption | null>(
    address?.zipCode
      ? { label: address.zipCode, value: address.zipCode }
      : null,
  )
  const [postalCodeFreeText, setPostalCodeFreeTextState] = useState<string>(
    address?.zipCode || "",
  )
  const [addressDetailValue, setAddressDetailValue] = useState<string>(
    address?.addressDetail || "",
  )
  const [title, setTitle] = useState<string>("")
  const [phoneNumber, setPhoneNumber] = useState<string>("")
  const [isFormValid, setIsFormValid] = useState<boolean>(isEditMode)

  // Update form validation status whenever any field changes
  useEffect(() => {
    const isPostalCodeValid = isPostalCodeFreeText 
      ? postalCodeFreeText.length === 5 
      : Boolean(postalCode)

    const isValid =
      Boolean(country) &&
      Boolean(countryCode) &&
      Boolean(province) &&
      Boolean(city) &&
      Boolean(district) &&
      isPostalCodeValid &&
      Boolean(addressDetailValue?.trim()) &&
      Boolean(title?.trim()) &&
      Boolean(phoneNumber?.trim())

    setIsFormValid(isValid)
  }, [
    country,
    countryCode,
    province,
    city,
    district,
    postalCode,
    postalCodeFreeText,
    isPostalCodeFreeText,
    addressDetailValue,
    title,
    phoneNumber,
  ])

  const setCountry = (value: TItemOption | null) => {
    setCountryState(value)
    if (value) {
      setIsProvinceEnabled(true)
      const newFilteredProvinces = provinces.filter(
        (province) => province.countryId === Number(value.value),
      )
      setFilteredProvinces(newFilteredProvinces)
    } else {
      setIsProvinceEnabled(false)
      setFilteredProvinces([])
    }
    setProvinceState(null)
    setCityState(null)
    setDistrictState(null)
    setPostalCodeState(null)
    setPostalCodeFreeTextState("")
    setIsCityEnabled(false)
    setIsDistrictEnabled(false)
    setIsZipCodeEnabled(false)
    setIsPostalCodeFreeText(false)
  }

  const setProvince = (value: TItemOption | null) => {
    setProvinceState(value)
    if (value) {
      setIsCityEnabled(true)
      const newFilteredCities = cities.filter(
        (city) => city.provinceId === Number(value.value),
      )
      setFilteredCities(newFilteredCities)
    } else {
      setIsCityEnabled(false)
      setFilteredCities([])
    }
    setCityState(null)
    setDistrictState(null)
    setPostalCodeState(null)
    setPostalCodeFreeTextState("")
    setIsDistrictEnabled(false)
    setIsZipCodeEnabled(false)
    setIsPostalCodeFreeText(false)
  }

  const setCity = async (value: TItemOption | null) => {
    // Reset district and postal code state
    setDistrictState(null)
    setPostalCodeState(null)
    setPostalCodeFreeTextState("")

    // Set the city state immediately
    setCityState(value)

    // If no city selected, disable district dropdown
    if (!value) {
      setIsDistrictEnabled(false)
      setFilteredDistricts([])
      setIsZipCodeEnabled(false)
      setIsPostalCodeFreeText(false)
      return
    }

    // Enable district dropdown immediately
    setIsDistrictEnabled(true)

    // Fetch districts for the selected city
    try {
      // IMPORTANT: Wait for the API call to complete
      const districtsData = await getDistrictsByCity(value.label)

      // Process the districts data
      if (districtsData?.content?.length > 0) {
        // Map the districts to the format expected by the ComboBox
        const districtOptions = districtsData.content.map((district) => ({
          label: district.name,
          value: district.id.toString(),
        }))

        // Set the filtered districts
        setFilteredDistricts(districtOptions)
      } else {
        setFilteredDistricts([])
      }
    } catch (error) {
      setFilteredDistricts([])
    }

    // Disable postal code dropdown until a district is selected
    setIsZipCodeEnabled(false)
  }

  const setDistrict = async (value: TItemOption | null) => {
    console.log("🏘️ [useFormAddresses] setDistrict called with:", value)
    setDistrictState(value)
    setPostalCodeState(null)
    setPostalCodeFreeTextState("")

    if (!value) {
      console.log("🏘️ [useFormAddresses] No district selected, disabling postal code")
      setIsZipCodeEnabled(false)
      setIsPostalCodeFreeText(false)
      setFilteredPostalCodes([])
      return
    }

    console.log("🏘️ [useFormAddresses] District selected:", value.label, "fetching postal codes...")
    setIsZipCodeEnabled(true)

    try {
      const postalCodesData = await getPostalCodesByDistrict(value.label)
      console.log("🏘️ [useFormAddresses] Postal codes data received:", postalCodesData)

      if (
        postalCodesData &&
        postalCodesData.content &&
        postalCodesData.content.length > 0
      ) {
        const postalCodeOptions = postalCodesData.content.map((postalCode) => ({
          label: postalCode.code,
          value: postalCode.code,
        }))
        console.log("🏘️ [useFormAddresses] Postal code options:", postalCodeOptions)
        setFilteredPostalCodes(postalCodeOptions)
        setIsPostalCodeFreeText(false)
      } else {
        console.log("🏘️ [useFormAddresses] No postal codes found, enabling free text input")
        setFilteredPostalCodes([])
        setIsPostalCodeFreeText(true)
      }
    } catch (error) {
      console.error("❌ [useFormAddresses] Error fetching postal codes:", error)
      setFilteredPostalCodes([])
      setIsPostalCodeFreeText(true)
    }
  }

  // Clean up effect to prevent updates after unmount
  useEffect(() => {
    return () => {
      isMounted.current = false
    }
  }, [])

  useEffect(() => {
    if (initialValue?.country) {
      const newFilteredProvinces = provinces.filter(
        (province) =>
          province.countryId === Number(initialValue.country?.value),
      )
      setFilteredProvinces(newFilteredProvinces)
    }

    if (initialValue?.province) {
      const newFilteredCities = cities.filter(
        (city) => city.provinceId === Number(initialValue.province?.value),
      )
      setFilteredCities(newFilteredCities)
    }

    if (initialValue?.city) {
      const fetchDistricts = async () => {
        try {
          if (!initialValue.city?.label) return
          const districtsData = await getDistrictsByCity(
            initialValue.city.label,
          )

          if (
            districtsData &&
            districtsData.content &&
            districtsData.content.length > 0
          ) {
            const districtOptions = districtsData.content.map((district) => ({
              label: district.name,
              value: district.id.toString(),
            }))

            setFilteredDistricts(districtOptions)

            // If district is initially set, find the matching district option
            if (initialValue.district && initialValue.district.value) {
              const matchingDistrict = districtOptions.find(
                (option) => option.value === initialValue.district?.value,
              )

              // If we found the district, set it as selected
              if (matchingDistrict) {
                setDistrictState(matchingDistrict)
                setIsDistrictEnabled(true)

                // Now fetch postal codes for this district
                const postalCodesData = await getPostalCodesByDistrict(
                  matchingDistrict.label,
                )

                if (
                  postalCodesData &&
                  postalCodesData.content &&
                  postalCodesData.content.length > 0
                ) {
                  const postalCodeOptions = postalCodesData.content.map(
                    (postalCode) => ({
                      label: postalCode.code,
                      value: postalCode.code,
                    }),
                  )

                  setFilteredPostalCodes(postalCodeOptions)
                  setIsZipCodeEnabled(true)
                  setIsPostalCodeFreeText(false)
                } else {
                  setFilteredPostalCodes([])
                  setIsZipCodeEnabled(true)
                  setIsPostalCodeFreeText(true)
                }
              }
            }
          } else {
            setFilteredDistricts([])
          }
        } catch (error) {
          setFilteredDistricts([])
        }
      }
      fetchDistricts()
    }
  }, [initialValue, provinces, cities])

  // Add extra effect to monitor districts
  useEffect(() => {}, [filteredDistricts])

  // Add extra effect to monitor district state
  useEffect(() => {}, [district])

  const isDanger = (isDanger: boolean) => {
    return isDanger ? "danger" : undefined
  }

  const setPostalCode = (value: TItemOption | null) => {
    setPostalCodeState(value)
  }

  const setPostalCodeFreeText = (value: string) => {
    setPostalCodeFreeTextState(value)
  }

  const getPostalCodeError = () => {
    if (isPostalCodeFreeText && postalCodeFreeText.length > 0 && postalCodeFreeText.length !== 5) {
      return "Postal Code must be 5 numbers long"
    }
    return state?.errors?.zipCode
  }

  return {
    country,
    countryCode,
    province,
    city,
    district,
    postalCode,
    postalCodeFreeText,
    setCountry,
    setCountryCode,
    setProvince,
    setCity,
    setDistrict,
    setPostalCode,
    setPostalCodeFreeText,
    filteredProvinces,
    filteredCities,
    filteredDistricts,
    filteredPostalCodes,
    isProvinceEnabled,
    isCityEnabled,
    isDistrictEnabled,
    isZipCodeEnabled,
    isPostalCodeFreeText,
    isDanger,
    isFormValid,
    getPostalCodeError,
    setAddressDetailValue,
    setTitle,
    setPhoneNumber,
  }
}
