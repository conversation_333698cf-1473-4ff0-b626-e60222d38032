import { debounce } from "lodash"
import { useMemo, useState } from "react"
import { useAddressStore } from "stores/addressStore"

export const useSearchAddress = () => {
  const { searchQuery, setSearchQuery } = useAddressStore()
  const [searchValue, setSearchValue] = useState(searchQuery)

  const debouncedSetSearchQuery = useMemo(
    () => debounce(setSearchQuery, 500),
    [setSearchQuery],
  )

  const handleSearch = (value: string) => {
    setSearchValue(value)
    debouncedSetSearchQuery(value)
  }

  return {
    searchValue,
    handleSearch,
  }
}
