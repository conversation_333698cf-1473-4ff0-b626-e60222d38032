"use client"

import { Divide<PERSON>, <PERSON>ing, IconCloseOutline } from "@kickavenue/ui/components"
import React, { useState } from "react"
import Modal from "@app/components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { useAddressStore } from "stores/addressStore"
import { TCountry } from "types/country.type"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"
import ButtonSave from "@components/shared/Form/ButtonSave"
import { ModalConstant } from "@constants/modal"
import { useCreateAddress } from "@app/hooks/useAddressMutations"
import { convertFormDataToAddressData } from "@utils/addressFormConverter"

import FieldFormAddresses from "./FieldFormAddresses"

interface FormAddressesProps {
  countries: TCountry[]
  cities: TCity[]
  provinces: TProvince[]
}

const FormAddresses = (props: FormAddressesProps) => {
  const { countries, cities, provinces } = props
  const { setOpen } = useModalStore()
  const { addresses } = useAddressStore()
  const [isFormValid, setIsFormValid] = useState(false)
  const [formErrors, setFormErrors] = useState<Record<string, string[]>>({})

  const createAddress = useCreateAddress()

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const formData = new FormData(event.currentTarget)

    // If it's the first address or no default address exists, force set as default
    if (addresses.length === 0 || !addresses.some((addr) => addr.isPrimary)) {
      formData.set("is_primary", "on")
    }

    try {
      // Convert form data to API format
      const addressData = convertFormDataToAddressData(formData)

      // Create address using React Query mutation
      await createAddress.mutateAsync(addressData)

      // Close modal on success
      setOpen(false, "")
      setFormErrors({})
    } catch (error: any) {
      // Handle validation errors
      if (error.message?.includes("Validation failed:")) {
        const errorMessage = error.message.replace("Validation failed: ", "")
        const errors: Record<string, string[]> = {}

        // Parse error message (field: message; field2: message2)
        errorMessage.split("; ").forEach((fieldError: string) => {
          const [field, message] = fieldError.split(": ")
          if (field && message) {
            errors[field] = [message]
          }
        })

        setFormErrors(errors)
      } else {
        setFormErrors({
          submit: [
            error.message || "Failed to create address. Please try again.",
          ],
        })
      }
    }
  }

  const initialValue = { countryCode: { label: "+62", value: "+62" } }

  // Determine if we should show the "Set as Default" checkbox
  const showSetAsDefault =
    addresses.length === 0 || !addresses.some((addr) => addr.isPrimary)

  return (
    <>
      <Modal modalId={ModalConstant.MODAL_IDS.NEW_ADDRESS}>
        <div className="flex justify-between p-md">
          <div className="flex grow justify-center text-right">
            <Heading heading="5" textStyle="bold">
              Add New Address
            </Heading>
          </div>
          <IconCloseOutline
            onClick={() => setOpen(false, "")}
            className="scale-150 cursor-pointer"
          />
        </div>
        <Divider orientation="horizontal" />
        <form onSubmit={handleSubmit}>
          <div className="max-h-[496px] overflow-scroll">
            <FieldFormAddresses
              state={{ errors: formErrors }}
              countries={countries}
              cities={cities}
              provinces={provinces}
              initialValue={initialValue}
              showSetAsDefault={showSetAsDefault}
              onValidationChange={setIsFormValid}
            />
          </div>
          <div className="p-lg shadow-base">
            <ButtonSave
              disabled={!isFormValid || createAddress.isPending}
              loading={createAddress.isPending}
            />
          </div>
        </form>
      </Modal>
    </>
  )
}

export default FormAddresses
