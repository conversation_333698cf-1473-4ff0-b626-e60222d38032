import React, { useEffect, useMemo } from "react"
import Image from "next/image"
import {
  Label,
  ComboBox as ComboBoxCommonLib,
  Input as InputCommonLib,
  CheckBox,
} from "@kickavenue/ui/components"
import { TItemOption as TLocalItemOption } from "@kickavenue/ui/dist/src/components/ComboBox/ComboBox.type"
import {
  convertS3UrlToCloudFront,
  convertToItemList,
  Item,
  splitPhoneNumber,
} from "@utils/misc"
import Textarea from "@app/components/shared/Form/Textarea"
import Input from "@app/components/shared/Form/Input"
import ComboBox from "@app/components/shared/Form/ComboBox"
import { TCountry } from "types/country.type"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"
import { TAddress } from "types/address.type"
import { useMemberStore } from "stores/memberStore"

import {
  InitialValue,
  TItemOption,
  useFormAddresses,
} from "./hooks/useFormAddresses"

interface FieldFormAdressesProps {
  state: any
  countries: TCountry[]
  cities: TCity[]
  provinces: TProvince[]
  address?: TAddress | null
  initialValue: InitialValue
  showSetAsDefault?: boolean
  onValidationChange?: (isValid: boolean) => void
  isEditMode?: boolean
}

// eslint-disable-next-line max-lines-per-function
const FieldFormAddresses = (props: FieldFormAdressesProps) => {
  const { member } = useMemberStore()
  const {
    countries,
    cities,
    provinces,
    state,
    address,
    initialValue,
    showSetAsDefault = false,
    isEditMode = false,
  } = props
  const {
    country,
    countryCode,
    province,
    city,
    district,
    postalCode,
    postalCodeFreeText,
    setCountry,
    setCountryCode,
    setProvince,
    setCity,
    setDistrict,
    setPostalCode,
    setPostalCodeFreeText,
    filteredProvinces,
    filteredCities,
    filteredDistricts,
    filteredPostalCodes,
    isProvinceEnabled,
    isCityEnabled,
    isDistrictEnabled,
    isZipCodeEnabled,
    isPostalCodeFreeText,
    isDanger,
    isFormValid,
    getPostalCodeError,
    setAddressDetailValue,
    setTitle,
    setPhoneNumber,
  } = useFormAddresses({
    initialValue,
    provinces,
    cities,
    address: address
      ? {
        zipCode: address.zipCode,
        addressDetail: address.addressDetail,
      }
      : undefined,
    isEditMode,
    state,
  })

  // Destructure onValidationChange outside useEffect
  const { onValidationChange } = props

  useEffect(() => {
    if (!onValidationChange) return
    onValidationChange(isFormValid)
  }, [isFormValid, onValidationChange])

  // Initialize form validation immediately when in edit mode
  useEffect(() => {
    if (!isEditMode || !address) return

    // Set initial values from the address data directly when in edit mode
    if (address.title) {
      setTitle(address.title)
    }

    // Extract phone number without country code
    if (address.phoneNumber) {
      const phoneInfo = splitPhoneNumber(address.phoneNumber)
      if (phoneInfo && phoneInfo.number) {
        setPhoneNumber(phoneInfo.number)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditMode, address])

  const postalCodeState = postalCode
    ? {
      label: postalCode.label,
      value: postalCode.value,
    }
    : null

  const districtComboBoxProps = useMemo(
    () => ({
      state: "required" as const,
      placeholder: "District",
      label: "District",
      disabled: !isDistrictEnabled,
      items: filteredDistricts,
      selected: filteredDistricts.length > 0 ? district : null,
      setSelected: (value: TLocalItemOption | null) => {
        setDistrict(value)
        if (value) {
          const districtInput = document.querySelector(
            'input[name="district_id"]',
          ) as HTMLInputElement
          if (districtInput) {
            districtInput.value = value.value
          }
        }
      },
    }),
    [filteredDistricts, district, isDistrictEnabled, setDistrict],
  )

  return (
    <div className="flex flex-col gap-y-base p-lg">
      <input
        type="text"
        name="pinpoint"
        className="hidden"
        defaultValue={
          address?.geoReverseAddress || address?.addressDetail || ""
        }
      />
      <input
        type="text"
        name="member_id"
        className="hidden"
        defaultValue={member?.id}
      />
      {address?.id && (
        <input
          type="text"
          name="id"
          className="hidden"
          defaultValue={address.id}
        />
      )}
      <Input
        placeholder="House/Office/Apartment"
        state="required"
        label="Mark as"
        name="title"
        defaultValue={address?.title}
        helperText={state?.errors?.title}
        variant={isDanger(state?.errors?.title)}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          setTitle(e.target.value)
          // Update the hidden form field
          const titleInput = document.querySelector(
            'input[name="title"]',
          ) as HTMLInputElement
          if (titleInput) {
            titleInput.value = e.target.value
          }
        }}
      />
      <div className="flex justify-between gap-xs">
        <Input
          state="required"
          label="Recipient"
          placeholder="Enter your first name"
          name="recipient"
          defaultValue={member?.recipient}
          helperText={state?.errors?.recipient}
          variant={isDanger(state?.errors?.recipient)}
        />
        {/* <Input
          state="required"
          label="First Name"
          placeholder="Enter your first name"
          name="first_name"
          disabled
          defaultValue={member?.firstName}
          helperText={state?.errors?.firstName}
          variant={isDanger(state?.errors?.firstName)}
        />
        <Input
          label="Last Name"
          placeholder="Enter your last name"
          disabled
          defaultValue={member?.lastName}
          name="last_name"
        /> */}
      </div>
      <div className="flex gap-xs">
        <div className="flex w-1/2 flex-col gap-y-sm">
          <Label state="required" size="sm" type="default">
            Mobile Number
          </Label>
          <div className="flex justify-between gap-xs">
            <div className="w-[100px]">
              <input
                type="text"
                name="country_code"
                className="hidden"
                defaultValue={countryCode?.value ?? ""}
              />
              <ComboBoxCommonLib
                selected={countryCode}
                setSelected={setCountryCode}
                items={countries.map((country) => ({
                  label: country.prefix || "",
                  value: country.prefix || "",
                  icon: country.flag ? (
                    <>
                      <Image
                        src={convertS3UrlToCloudFront(country.flag)}
                        alt={country.name || ""}
                        width={0}
                        height={0}
                        className="h-4 w-6"
                      />
                    </>
                  ) : null,
                }))}
                placeholder="Code"
              />
            </div>
            <InputCommonLib
              placeholder="Mobile Number"
              name="phone_number"
              type="tel"
              pattern="[0-9]*"
              inputMode="numeric"
              helperText={state?.errors?.phoneNumber}
              variant={isDanger(state?.errors?.phoneNumber)}
              defaultValue={
                splitPhoneNumber(address?.phoneNumber ?? "")?.number
              }
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Remove any non-numeric characters
                e.target.value = e.target.value.replace(/[^0-9]/g, "")
                setPhoneNumber(e.target.value)
              }}
            />
          </div>
        </div>

        <div className="w-1/2">
          <input
            type="text"
            name="country_id"
            className="hidden"
            defaultValue={country?.value}
          />
          <ComboBox
            state="required"
            placeholder="Country"
            label="Country"
            helperText={state?.errors?.country}
            variant={isDanger(state?.errors?.country)}
            items={
              convertToItemList(
                countries as unknown as Item[],
                "country",
                "name",
              ) as unknown as TItemOption[]
            }
            selected={country}
            setSelected={(value) => {
              setCountry(value)
              if (value) {
                const countryInput = document.querySelector(
                  'input[name="country_id"]',
                ) as HTMLInputElement
                if (countryInput) {
                  countryInput.value = value.value
                }
              }
            }}
          />
        </div>
      </div>
      <div className="flex justify-between gap-base">
        <input
          type="text"
          name="region_id"
          className="hidden"
          defaultValue="1"
        />
        <input
          type="text"
          name="province_id"
          className="hidden"
          defaultValue={province?.value}
        />
        <ComboBox
          state="required"
          placeholder="Province"
          label="Province"
          helperText={state?.errors?.province}
          variant={isDanger(state?.errors?.province)}
          disabled={!isProvinceEnabled}
          items={
            convertToItemList(
              filteredProvinces as unknown as Item[],
              "id",
              "name",
            ) as unknown as TItemOption[]
          }
          selected={province}
          setSelected={(value) => {
            setProvince(value)
            if (value) {
              const provinceInput = document.querySelector(
                'input[name="province_id"]',
              ) as HTMLInputElement
              if (provinceInput) {
                provinceInput.value = value.value
              }
            }
          }}
        />
        <input
          type="text"
          name="city_id"
          className="hidden"
          defaultValue={city?.value}
        />
        <ComboBox
          helperText={state?.errors?.city}
          variant={isDanger(state?.errors?.city)}
          state="required"
          placeholder="City"
          label="City"
          disabled={!isCityEnabled}
          items={
            convertToItemList(
              filteredCities as unknown as Item[],
              "id",
              "name",
            ) as unknown as TItemOption[]
          }
          selected={city}
          setSelected={(value) => {
            setCity(value)
            if (value) {
              const cityInput = document.querySelector(
                'input[name="city_id"]',
              ) as HTMLInputElement
              if (cityInput) {
                cityInput.value = value.value
              }
            }
          }}
        />
      </div>
      <div className="flex justify-between gap-base">
        <input
          type="text"
          name="district_id"
          className="hidden"
          defaultValue={
            district?.value || address?.districtId?.toString() || ""
          }
        />
        <ComboBox
          {...districtComboBoxProps}
          key={`district-${filteredDistricts.length}`}
        />
        <input
          type="text"
          name="zip_code"
          className="hidden"
          defaultValue={isPostalCodeFreeText ? postalCodeFreeText : postalCode?.value}
        />
        {isPostalCodeFreeText ? (
          <Input
            placeholder="Postal Code"
            label="Postal Code"
            state="required"
            variant={postalCodeFreeText.length > 0 && postalCodeFreeText.length !== 5 ? "danger" : isDanger(state?.errors?.zipCode)}
            helperText={getPostalCodeError()}
            disabled={!isZipCodeEnabled}
            value={postalCodeFreeText}
            inputType="number"
            maxLength={5}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const value = e.target.value.replace(/\D/g, '').slice(0, 5)
              setPostalCodeFreeText(value)
              const postalCodeInput = document.querySelector(
                'input[name="zip_code"]',
              ) as HTMLInputElement
              if (postalCodeInput) {
                postalCodeInput.value = value
              }
            }}
            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
              // Allow digits and common navigation/control keys
              const allowedKeys = new Set([
                "Backspace",
                "Delete",
                "ArrowLeft",
                "ArrowRight",
                "ArrowUp",
                "ArrowDown",
                "Tab",
              ])
              if (!(/\d/.test(e.key) || allowedKeys.has(e.key))) {
                e.preventDefault()
              }
            }}
          />
        ) : (
          <ComboBox
            placeholder="Postal Code"
            label="Postal Code"
            state="required"
            variant={isDanger(state?.errors?.zipCode)}
            helperText={getPostalCodeError()}
            disabled={!isZipCodeEnabled}
            items={filteredPostalCodes.map((item) => ({
              label: item.label,
              value: item.value,
            }))}
            selected={postalCodeState}
            setSelected={(selected) => {
              setPostalCode(selected as TLocalItemOption | null)
              if (selected) {
                const postalCodeInput = document.querySelector(
                  'input[name="zip_code"]',
                ) as HTMLInputElement
                if (postalCodeInput) {
                  postalCodeInput.value = selected.value
                }
              }
            }}
            key={`postal-${filteredPostalCodes.length}-${isZipCodeEnabled}`}
            noResultsMessage="No Postal Codes found, please input manually"
          />
        )}
      </div>
      <Textarea
        state="required"
        placeholder="Enter your address detail"
        label="Address Detail"
        name="address_detail"
        defaultValue={address?.addressDetail}
        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
          setAddressDetailValue(e.target.value)
        }
      />
      <Textarea
        placeholder="Enter notes"
        label="Notes"
        name="notes"
        defaultValue={address?.notes || ""}
      />
      {showSetAsDefault && (
        <div className="mt-base">
          <CheckBox
            name="is_primary"
            label="Set as Default Address"
            defaultChecked={false}
          />
        </div>
      )}
      {/* Debug info - will be hidden in production */}
      <div className="text-gray-500 hidden text-xs">
        <>District enabled: {String(isDistrictEnabled)}</>
        {" | "}
        <>District count: {filteredDistricts.length}</>
        {" | "}
        <>ZipCode enabled: {String(isZipCodeEnabled)}</>
        {" | "}
        <>ZipCode count: {filteredPostalCodes.length}</>
      </div>
    </div>
  )
}

export default FieldFormAddresses
