"use client"

import { useEffect, useState } from "react"
import { CityApiRepository } from "@infrastructure/repositories/cityApiRepository"
import { CountryApiRepository } from "@infrastructure/repositories/countryApiRepository"
import { ProvinceApiRepository } from "@infrastructure/repositories/provinceApiRepository"
import { TPaginatedData } from "types/apiResponse.type"
import { TCountry } from "types/country.type"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"

import AddressFormModal from "./AddressFormModal"
import AddressList from "./list/AddressList"
import ModalDelete from "./ModalDelete"

const SavedAddresses = () => {
  const Country = new CountryApiRepository()
  const City = new CityApiRepository()
  const Province = new ProvinceApiRepository()

  const [region, setRegion] = useState<{
    countries: {
      content: TCountry[]
    }
    cities: { content: TCity[] }
    provinces: { content: TProvince[] }
  }>({
    countries: {
      content: [],
    },
    cities: {
      content: [],
    },
    provinces: {
      content: [],
    },
  })

  useEffect(() => {
    const fetchRegionData = async () => {
      try {
        const [countries, cities, provinces] = await Promise.all([
          Country.getAll(),
          City.getAll(),
          Province.getAll(),
        ])

        setRegion({
          countries: countries as TPaginatedData<TCountry>,
          cities: cities as TPaginatedData<TCity>,
          provinces: provinces as TPaginatedData<TProvince>,
        })
      } catch (error) {
        return { countries: [], cities: [], provinces: [] }
      }
    }

    fetchRegionData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div className="h-full">
      <AddressList />
      <AddressFormModal
        countries={region.countries.content}
        cities={region.cities.content}
        provinces={region.provinces.content}
      />
      <ModalDelete />
    </div>
  )
}

export default SavedAddresses
