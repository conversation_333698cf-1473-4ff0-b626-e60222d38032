"use client"

import { IconKebabMenuVertical, Text } from "@kickavenue/ui/components"
import React, { useState } from "react"
import useClickOutside from "@app/hooks/useClickOutside"
import { useModalStore } from "stores/modalStore"
import { useAddressStore } from "stores/addressStore"
import { TAddress } from "types/address.type"
import { ModalConstant } from "@constants/modal"

import { useUpdateAddressDefault } from "./hooks/useUpdateAddressDefault"

const { DELETE_ADDRESS, EDIT_ADDRESS } = ModalConstant.MODAL_IDS

const DropdownAddress = ({ address }: { address: TAddress }) => {
  const { setOpen } = useModalStore()
  const { setSelectedIdAddress } = useAddressStore()
  const [open, setIsopen] = useState(false)
  const dropdownRef = useClickOutside<HTMLDivElement>(open, () =>
    setIsopen(false),
  )

  const buttonClass = "w-full py-xs px-sm text-left hover:bg-gray-w-90"

  const { mutate } = useUpdateAddressDefault()

  return (
    <div className="relative" ref={dropdownRef}>
      <IconKebabMenuVertical
        onClick={() => setIsopen(!open)}
        className="size-5 scale-100 cursor-pointer"
      />
      {open && (
        <div className="absolute right-0 z-10 mt-2 min-w-[97px] rounded-sm bg-white shadow-base">
          <button
            onClick={() => {
              setSelectedIdAddress(address.id ?? 0)
              setOpen(true, EDIT_ADDRESS)
            }}
            type="button"
            className={buttonClass}
          >
            <Text type="regular" size="sm" state="primary">
              Edit Address
            </Text>
          </button>
          {!address.isPrimary && (
            <button
              type="button"
              className={buttonClass}
              onClick={() => {
                mutate({ address, isPrimary: true })
                setIsopen(false)
              }}
            >
              <Text type="regular" size="sm" state="primary">
                Set Default
              </Text>
            </button>
          )}
          <button
            onClick={() => {
              setSelectedIdAddress(address.id ?? 0)
              setOpen(true, DELETE_ADDRESS)
            }}
            className={buttonClass}
            type="button"
          >
            <Text type="regular" size="sm" state="danger">
              Delete
            </Text>
          </button>
        </div>
      )}
    </div>
  )
}

export default DropdownAddress
