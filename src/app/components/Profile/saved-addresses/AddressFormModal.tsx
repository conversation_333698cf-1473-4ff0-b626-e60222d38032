/* eslint-disable max-lines-per-function */
"use client"

import { Divide<PERSON>, <PERSON><PERSON>, IconCloseOutline } from "@kickavenue/ui/components"
import React, { useEffect, useMemo, useCallback, useActionState } from "react"
import { useQueryClient } from "@tanstack/react-query"
import { cx } from "class-variance-authority"
import Modal from "@app/components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { useAddressStore } from "stores/addressStore"
import { create, edit } from "@infrastructure/actions/address"
import { TCountry } from "types/country.type"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"
import ButtonSave from "@components/shared/Form/ButtonSave"
import useToast from "@app/hooks/useToast"
import Spinner from "@components/shared/Spinner/Spinner"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { splitPhoneNumber } from "@utils/misc"
import useFetchAddressById from "@app/hooks/useFetchAddressById"
import { ModalConstant } from "@constants/modal"

import FieldFormAddresses from "./FieldFormAddresses"
import { InitialValue } from "./hooks/useFormAddresses"

const { EDIT_ADDRESS, NEW_ADDRESS, CHANGE_ADDRESS } = ModalConstant.MODAL_IDS

interface AddressFormModalProps {
  countries: TCountry[]
  cities: TCity[]
  provinces: TProvince[]
}

const AddressFormModal = (props: AddressFormModalProps) => {
  const { countries, cities, provinces } = props
  const { setOpen, open, modalId, prevModal } = useModalStore()
  const { addresses, selectedIdAddress } = useAddressStore()
  const queryClient = useQueryClient()
  const { setShowToast } = useToast()

  const isEditMode = modalId === EDIT_ADDRESS
  const [isFormValid, setIsFormValid] = React.useState(false)

  // Only fetch address data if we're in edit mode
  const { data: address, isLoading } = useFetchAddressById(
    isEditMode ? Number(selectedIdAddress) : undefined,
  )

  // Memoize the handlers to prevent recreation on each render
  const handleCreateAddress = useCallback(
    async (prevState: any, formData: FormData) => {
      if (addresses.length === 0) {
        formData.set("is_primary", "on")
      }

      try {
        const resp = await create(prevState, formData)
        if ("errors" in resp) {
          return resp
        }

        // Use a single invalidation call with a predicate
        queryClient.invalidateQueries({
          predicate: (query) => {
            const queryKey = query.queryKey[0]
            return (
              queryKey === QueryKeysConstant.GET_MY_ADDRESS ||
              queryKey === QueryKeysConstant.GET_ALL_ADDRESS
            )
          },
        })

        setShowToast(true, "Address successfully added")
        setOpen(false, "")
        return resp
      } catch (error) {
        return {
          errors: {
            submit: "Failed to create address. Please try again.",
          },
        }
      }
    },
    [addresses.length, queryClient, setOpen, setShowToast],
  )

  const handleEditAddress = useCallback(
    async (prevState: any, formData: FormData) => {
      if (!address) return prevState

      formData.set("is_primary", address.isPrimary ? "on" : "off")
      const resp = await edit(prevState, formData)

      if (!("id" in resp)) {
        return resp
      }

      // Use a single invalidation call with a predicate
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey[0]
          return (
            queryKey === QueryKeysConstant.GET_MY_ADDRESS ||
            queryKey === QueryKeysConstant.GET_ALL_ADDRESS ||
            (Array.isArray(query.queryKey) &&
              query.queryKey[0] === QueryKeysConstant.GET_ADDRESS_BY_ID &&
              query.queryKey[1] === selectedIdAddress)
          )
        },
      })

      setShowToast(true, "Address successfully edited")

      if (prevModal?.modalId === CHANGE_ADDRESS) {
        setOpen(true, CHANGE_ADDRESS)
      } else {
        setOpen(false)
      }

      return resp
    },
    [
      address,
      queryClient,
      selectedIdAddress,
      setOpen,
      setShowToast,
      prevModal?.modalId,
    ],
  )

  const [state, formAction] = useActionState(
    isEditMode ? handleEditAddress : handleCreateAddress,
    { errors: {} },
  )

  // Memoize initialValue to prevent recreating it on every render
  const initialValue: InitialValue = useMemo(() => {
    if (!isEditMode || !address) {
      return { countryCode: undefined }
    }

    const value = {
      countryCode: splitPhoneNumber(address.phoneNumber ?? "").countryCode,
      city: {
        value: String(address.cityId),
        label: address.cityName ?? "",
      },
      province: {
        value: String(address.provinceId),
        label: address.provinceName ?? "",
      },
      country: {
        value: String(address.countryId),
        label: address.countryName ?? "",
      },
      district: address.districtId
        ? {
            value: String(address.districtId),
            label: "",
          }
        : undefined,
    }

    return value
  }, [isEditMode, address])

  // Only show Set as Default when adding a new address AND user already has at least one address
  const showSetAsDefault = !isEditMode && addresses.length > 0

  // Check for successful submission and close modal
  useEffect(() => {
    if (isEditMode) return
    if (!("id" in state)) return
    setOpen(false, "")
  }, [isEditMode, setOpen, state])

  if (!open || (modalId !== NEW_ADDRESS && modalId !== EDIT_ADDRESS)) {
    return null
  }

  const isEditText = isEditMode ? "Edit Address" : "Add New Address"
  const addressValue = isEditMode ? address : undefined

  const formContent =
    isEditMode && isLoading ? (
      <div className="flex h-[496px] w-full items-center justify-center">
        <Spinner />
      </div>
    ) : (
      <FieldFormAddresses
        state={state}
        countries={countries}
        cities={cities}
        provinces={provinces}
        initialValue={initialValue}
        address={addressValue}
        showSetAsDefault={showSetAsDefault}
        onValidationChange={setIsFormValid}
        isEditMode={isEditMode}
      />
    )

  const formContentClass = cx(
    "max-h-[496px] overflow-scroll",
    isEditMode && isLoading && "overflow-x-hidden overflow-y-hidden",
  )

  return (
    <Modal modalId={modalId} className="!max-w-[1036px]">
      <div className="flex justify-between p-md">
        <div className="flex grow justify-center text-right">
          <Heading heading="5" textStyle="bold">
            {isEditText}
          </Heading>
        </div>
        <IconCloseOutline
          onClick={() => setOpen(false, "")}
          className="scale-150 cursor-pointer"
        />
      </div>
      <Divider orientation="horizontal" />
      <form action={formAction}>
        <div className={formContentClass}>{formContent}</div>
        <div className="p-lg shadow-base">
          <ButtonSave disabled={!isFormValid} />
        </div>
      </form>
    </Modal>
  )
}

export default AddressFormModal
