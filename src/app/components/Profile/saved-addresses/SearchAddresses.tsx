"use client"

import {
  Button,
  ButtonIcon,
  IconAddOutline,
  IconSearchOutline,
  Input,
} from "@kickavenue/ui/components"
import React from "react"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"

import { useSearchAddress } from "./hooks/useSearchAddress"

const SearchAddresses = () => {
  const { setOpen } = useModalStore()
  const { handleSearch, searchValue } = useSearchAddress()
  return (
    <div className="flex items-center gap-x-sm">
      <Input
        leftIcon={<IconSearchOutline />}
        onChange={(e) => {
          handleSearch(e.currentTarget.value)
        }}
        placeholder="Search"
        size="sm"
        value={searchValue}
      />
      <Button
        onClick={() => setOpen(true, ModalConstant.MODAL_IDS.NEW_ADDRESS)}
        size="md"
        variant="primary"
        className="!hidden !w-[158px] lg:!block"
      >
        Add New Address
      </Button>
      <ButtonIcon
        onClick={() => setOpen(true, ModalConstant.MODAL_IDS.NEW_ADDRESS)}
        size="md"
        variant="primary"
        className="lg:!hidden"
      >
        <IconAddOutline />
      </ButtonIcon>
    </div>
  )
}

export default SearchAddresses
