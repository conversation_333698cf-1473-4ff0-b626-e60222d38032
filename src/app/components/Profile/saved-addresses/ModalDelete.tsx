"use client"

import React from "react"
import {
  InfiniteData,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query"
import { But<PERSON> } from "@kickavenue/ui/components"
import { AddressApiRepository } from "@infrastructure/repositories/addressApiRepository"
import { DeleteAddressById } from "@application/usecases/deleteAddressById"
import { useAddressStore } from "stores/addressStore"
import { useModalStore } from "stores/modalStore"
import useToast from "@app/hooks/useToast"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { TPaginatedData } from "types/apiResponse.type"
import { TAddress } from "types/address.type"
import { ModalConstant } from "@constants/modal"
import ModalConfirm from "@components/shared/ModalConfirm"

const { DELETE_ADDRESS } = ModalConstant.MODAL_IDS

const ModalDelete = () => {
  const addressApiRepository = new AddressApiRepository()
  const deleteAddressById = new DeleteAddressById(addressApiRepository)
  const { selectedIdAddress } = useAddressStore()
  const { setOpen, modalId, open } = useModalStore()
  const { setShowToast } = useToast()
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: async () => {
      const address = await addressApiRepository.getById(
        Number(selectedIdAddress),
      )
      if (address.isPrimary) {
        throw new Error("You cannot delete the primary address.")
      }
      return deleteAddressById.execute(Number(selectedIdAddress))
    },
    onSuccess: () => {
      handleSuccess()
    },
    onError: (error: Error) => {
      handleError(error)
      setOpen(false, DELETE_ADDRESS)
    },
  })

  const handleSuccess = () => {
    queryClient.setQueriesData(
      {
        predicate: (query) => {
          return query.queryKey[0] === QueryKeysConstant.GET_MY_ADDRESS
        },
      },
      (prev: InfiniteData<TPaginatedData<TAddress>> | undefined) => {
        if (!prev) return prev
        const newAddresses = prev.pages
          .map((page) => page.content)
          .flat()
          .filter((item: TAddress) => item.id !== selectedIdAddress)
        return {
          ...prev,
          pages: prev.pages.map((page) => ({
            ...page,
            content: newAddresses,
          })),
        }
      },
    )
    queryClient.invalidateQueries({
      predicate: (query) =>
        query.queryKey[0] === QueryKeysConstant.GET_MY_ADDRESS,
    })
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_ALL_ADDRESS],
    })
    setOpen(false, DELETE_ADDRESS)
    setShowToast(true, "An address has been deleted!")
  }

  const handleError = (error: Error) => {
    setShowToast(true, error.message, "danger")
  }

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, DELETE_ADDRESS)}
        size="md"
        variant="secondary"
        className="!w-full"
      >
        Cancel
      </Button>
      <Button
        onClick={() => mutation.mutate()}
        size="md"
        variant="danger"
        className="!w-full"
        disabled={mutation.isPending}
      >
        Delete Address
      </Button>
    </>
  )

  return (
    <ModalConfirm
      open={open && modalId === DELETE_ADDRESS}
      onClose={() => setOpen(false, DELETE_ADDRESS)}
      title="Are you sure you want to delete this address?"
      subtitle="Deleting this address will remove it from your saved locations. This action cannot be undone."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default ModalDelete
