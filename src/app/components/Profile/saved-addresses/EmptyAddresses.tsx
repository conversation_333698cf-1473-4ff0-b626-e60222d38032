"use client"

import Empty from "@kickavenue/ui/components/Empty"
import { Button } from "@kickavenue/ui/dist/src/components"
import React from "react"
import { ModalConstant } from "@constants/modal"
import { useAddressStore } from "stores/addressStore"
import { useModalStore } from "stores/modalStore"

const EmptyAddresses = () => {
  const { searchQuery } = useAddressStore()
  const { setOpen } = useModalStore()

  const title = searchQuery
    ? "Opps, We Couldn't Find Your Address!"
    : "No Saved Addresses"
  const subText = searchQuery
    ? "Try using another keyword"
    : "You haven’t saved any addresses yet."

  return (
    <div className="mt-32 flex h-full flex-col items-center">
      <Empty
        title={title}
        subText={subText}
        className="[&>img]:h-[250px] [&>img]:w-[294px]"
      />
      {!searchQuery && (
        <Button
          size="md"
          variant="primary"
          onClick={() => {
            setOpen(true, ModalConstant.MODAL_IDS.NEW_ADDRESS)
          }}
        >
          Add New Address
        </Button>
      )}
    </div>
  )
}
export default EmptyAddresses
