"use client"

import React, { ReactNode, useState, KeyboardEvent } from "react"
import {
  IconEyeOutline,
  IconEyeSlashOutline,
} from "@kickavenue/ui/components/icons"
import { Text } from "@kickavenue/ui/dist/src/components"
import { useRouter } from "next/navigation"

import styles from "./WalletPoint.module.scss"
import { useSidebarStore } from "stores/sidebarCollapseStore"

interface WalletPoint {
  title: string
  icon: ReactNode
  value: string
}

const WalletPoint = (props: WalletPoint) => {
  const { icon, title, value } = props
  const [show, setShow] = useState(false)
  const { isCollapse } = useSidebarStore()
  const router = useRouter()

  const handleClick = () => {
    const route = title.toLowerCase().replace(" ", "-")
    router.push(`/profile/${route}`)
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter" || e.key === " ") {
      handleClick()
    }
  }

  const toggleShow = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation()
    setShow((prev) => !prev)
  }

  const showAriaLabel = show ? "Hide value" : "Show value"

  const renderIcon = () => {
    const IconComponent = show ? IconEyeSlashOutline : IconEyeOutline
    return (
      <IconComponent
        onClick={toggleShow}
        onKeyDown={(e: KeyboardEvent) => {
          if (e.key === "Enter" || e.key === " ") {
            toggleShow(e)
          }
        }}
        tabIndex={0}
        role="button"
        aria-label={showAriaLabel}
        className="cursor-pointer text-gray-w-40"
      />
    )
  }

  const displayValue = show ? value : "••••••••"

  return (
    <div
      className={`${styles["wallet-point"]} cursor-pointer transition-colors duration-300 hover:bg-gray-w-90`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
    >
      {icon}
      {
        isCollapse ? null :
          <>
            <div className="flex grow flex-col gap-xxs">
              <Text size="sm" type="regular" state="secondary">
                {title}
              </Text>
              <Text size="base" type="bold" state="primary">
                {displayValue}
              </Text>
            </div>
            {renderIcon()}
          </>
      }
    </div>
  )
}

export default WalletPoint
