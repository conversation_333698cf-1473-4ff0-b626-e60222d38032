import { Controller, useFormContext } from "react-hook-form"
import { Input } from "@kickavenue/ui/components/Input"
import LabeledInput from "@components/shared/LabeledInput"
import SelectBank from "@components/shared/Select/SelectBank"

const BankAccountFormFields = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext()

  const accountNumberVariant = errors.accountNumber ? "danger" : undefined
  const accountHolderVariant = errors.accountHolder ? "danger" : undefined

  return (
    <div className="flex flex-col gap-y-base px-lg pb-lg pt-sm">
      <LabeledInput label="Bank Name">
        <Controller
          name="bankId"
          control={control}
          render={({ field }) => <SelectBank {...field} />}
        />
      </LabeledInput>

      <LabeledInput label="Account Number">
        <Controller
          name="accountNumber"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              placeholder="Enter Account Number"
              variant={accountNumberVariant}
              helperText={errors.accountNumber?.message as string}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const value = e.target.value.replace(/[^0-9]/g, "")
                field.onChange(value)
              }}
            />
          )}
        />
      </LabeledInput>

      <LabeledInput label="Account Holder's Name">
        <Controller
          name="accountHolder"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              placeholder="Enter Account Holder's Name"
              variant={accountHolderVariant}
              helperText={errors.accountHolder?.message as string}
            />
          )}
        />
      </LabeledInput>
    </div>
  )
}

export default BankAccountFormFields
