"use client"

import {
  Button,
  IconBca,
  IconBni,
  IconBRI,
  IconMandiri,
  IconPermataBank,
  Text,
} from "@kickavenue/ui/components"
import Image from "next/image"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { Bank } from "types/bank.type"
import { TBankInfo } from "types/bankInfo.type"

interface BankAccountCardProps {
  bank: Bank & Partial<Omit<TBankInfo, "id" | "createdAt" | "createdBy">>
}

const BankAccountCard = ({ bank }: BankAccountCardProps) => {
  const replaceQueryParams = useReplaceQueryParams()
  const { setOpen } = useModalStore()

  const onEditClick = (bank: Bank) => {
    replaceQueryParams({
      mode: "edit",
      bankAccountId: bank.id,
    })

    setOpen(true, ModalConstant.MODAL_IDS.BANK_ACCOUNT_FORM)
  }

  return (
    <div
      key={bank.id}
      className="flex items-center justify-center gap-x-md rounded-xl border border-solid border-gray-w-80 p-base"
    >
      {getBankIcon(bank.bankCode!)}
      <div className="grow">
        <div className="flex flex-col gap-y-xxs">
          <Text size="base" state="primary" type="bold">
            {bank.accountHolder}
          </Text>
          <Text size="sm" state="secondary" type="regular">
            {bank.bankName}
            <span className="mx-xs">·</span> {bank.accountNumber}
          </Text>
        </div>
      </div>
      <div className="flex-none">
        <Button onClick={() => onEditClick(bank)} size="sm" variant="secondary">
          Edit
        </Button>
      </div>
    </div>
  )
}

export const getBankIcon = (bankCode: string) => {
  if (bankCode === "bca") return <IconBca />
  if (bankCode === "bri") return <IconBRI />
  if (bankCode === "permata") return <IconPermataBank />
  if (bankCode === "mandiri") return <IconMandiri />
  if (bankCode === "bni") return <IconBni />
  if (bankCode === "btn") {
    return <Image src="/btn.png" alt="btn" width={32} height={32} />
  }
  if (bankCode === "bsi") {
    return <Image src="/bsi-icon.jpg" alt="btn" width={32} height={32} />
  }
  if (bankCode === "cimb") {
    return <Image src="/cimb-icon.jpg" alt="btn" width={32} height={32} />
  }
  if (bankCode === "ocbc") {
    return <Image src="/ocbc-icon.png" alt="btn" width={32} height={32} />
  }
  if (bankCode === "danamon") {
    return <Image src="/danamon-icon.png" alt="btn" width={32} height={32} />
  }
  if (bankCode === "permata") {
    return <Image src="/permata-icon.png" alt="btn" width={32} height={32} />
  }
  if (bankCode === "jago") {
    return <Image src="/jago-icon.png" alt="btn" width={32} height={32} />
  }
  return <div className="size-8 rounded-md bg-gray-w-40" />
}

export default BankAccountCard
