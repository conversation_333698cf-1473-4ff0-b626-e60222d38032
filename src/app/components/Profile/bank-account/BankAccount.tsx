"use client"

import useGetAllBankInfo from "@app/hooks/useGetAllBankInfo"
import useGetAllMyBankAccount from "@app/hooks/useGetAllMyBankAccount"
import Spinner from "@components/shared/Spinner"

import BankAccountCard from "./BankAccountCard"
import EmptyBankAccount from "./EmptyBankAccount"
import BankAccountFormModal from "./form/BankAccountFormModal"

const BankAccount = () => {
  const { bankData, isLoading: isLoadingBankAccount } = useGetAllMyBankAccount()

  const bankIds = bankData.map((bank) => bank.bankId)
  const { data: allBankInfo, isLoading: isLoadingBankInfo } = useGetAllBankInfo(
    {
      ids: bankIds,
      page: 0,
      pageSize: bankIds.length,
    },
  )

  const getBankInfo = (bankId: number) => {
    return allBankInfo?.content.find((bank) => bank.id === bankId)
  }

  const prettierBankData = bankData.map((bank) => {
    return {
      ...getBankInfo(bank.bankId),
      ...bank,
    }
  })

  if (isLoadingBankAccount || isLoadingBankInfo) {
    return (
      <div className="flex min-h-[650px] flex-col items-center justify-center">
        <Spinner />
      </div>
    )
  }

  if (prettierBankData?.length === 0) {
    return (
      <>
        <EmptyBankAccount />
        <BankAccountFormModal />
      </>
    )
  }

  return (
    <>
      <div className="flex flex-col gap-y-base sm:min-h-[650px]">
        {prettierBankData.map((bank) => (
          <BankAccountCard key={bank.id} bank={bank} />
        ))}
      </div>
      <BankAccountFormModal />
    </>
  )
}

export default BankAccount
