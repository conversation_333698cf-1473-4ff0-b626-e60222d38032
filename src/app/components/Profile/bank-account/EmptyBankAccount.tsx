"use client"

import Empty from "@kickavenue/ui/components/Empty"
import { Button } from "@kickavenue/ui/components"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"

const EmptyBankAccount = () => {
  const replaceQueryParams = useReplaceQueryParams()
  const { setOpen } = useModalStore()

  const handleOpenBankAccountModal = () => {
    replaceQueryParams(
      {
        mode: "add",
      },
      ["id"],
    )

    setOpen(true, ModalConstant.MODAL_IDS.BANK_ACCOUNT_FORM)
  }

  return (
    <div className="mt-32 flex h-full flex-col items-center">
      <Empty
        title="No Bank Account Added"
        subText="Save your bank account details for easy withdrawals."
        className="[&>img]:h-[250px] [&>img]:w-[294px]"
        actionButton={
          <Button
            onClick={handleOpenBankAccountModal}
            size="md"
            variant="primary"
          >
            Add a Bank Account
          </Button>
        }
      />
    </div>
  )
}
export default EmptyBankAccount
