"use client"

import React, { useMemo } from "react"
import Image from "next/image"
import { Label, Input, ComboBox } from "@kickavenue/ui/components"
import type { TItemOption } from "@kickavenue/ui/components/ComboBox/ComboBox.type"
import { useCountries } from "@application/hooks/useCountries"
import { splitPhoneNumber } from "@utils/misc"

interface PersonalPhoneNumberInputProps {
  phoneNumber: string
}

const PersonalPhoneNumberInput: React.FC<PersonalPhoneNumberInputProps> = ({
  phoneNumber,
}) => {
  const { data: countries } = useCountries()

  // Parse the phone number to separate country code and number
  const parsedPhone = splitPhoneNumber(phoneNumber)

  // Find the country that matches the country code
  const selectedCountry = useMemo(() => {
    if (!countries || !parsedPhone?.countryCode?.value) return null
    return countries.find((c) => c.prefix === parsedPhone.countryCode.value)
  }, [countries, parsedPhone])

  const comboBoxItems = useMemo(
    () =>
      (countries?.map((c) => ({
        label: c.prefix || "",
        value: c.country?.toString() || "",
        icon: c.flag ? (
          <Image height={16} width={16} src={c.flag} alt={c.name || ""} />
        ) : null,
      })) as TItemOption[]) || [],
    [countries],
  )

  const comboBoxSelected = useMemo(() => {
    if (!selectedCountry?.country) {
      // Use the parsed country code or default to Indonesia if not available
      return {
        label: parsedPhone?.countryCode?.label || "+62",
        value: parsedPhone?.countryCode?.value || "62",
        icon: (
          <Image
            height={16}
            width={16}
            src="https://storage.googleapis.com/static-images-kickavenue/flags/Indonesia.svg"
            alt="Indonesia"
          />
        ),
      } as TItemOption
    }

    return {
      label: selectedCountry.prefix || "",
      value: selectedCountry.country.toString(),
      icon: selectedCountry.flag ? (
        <Image
          height={16}
          width={16}
          src={selectedCountry.flag}
          alt={selectedCountry.name || ""}
        />
      ) : null,
    } as TItemOption
  }, [selectedCountry, parsedPhone])

  const items = comboBoxItems.length > 0 ? comboBoxItems : [comboBoxSelected]

  return (
    <>
      <Label state="required" size="sm" type="default">
        Mobile Number
      </Label>
      <div className="flex gap-xs">
        <ComboBox
          items={items}
          selected={comboBoxSelected}
          setSelected={() => {}}
          className="!w-[22%] [&_input:disabled]:!text-gray-w-60"
          placeholder="Code"
          disabled
        />
        <div className="w-full">
          <Input
            disabled
            placeholder="Mobile Number"
            value={parsedPhone?.number || ""}
          />
        </div>
      </div>
    </>
  )
}

export default PersonalPhoneNumberInput
