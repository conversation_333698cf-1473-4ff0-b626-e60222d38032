import React from "react"
import { Label } from "@kickavenue/ui/components"
import InputDatePicker from "@app/components/shared/DatePicker/InputDatePicker"

const BirthOfDate = ({ birthOfDate }: { birthOfDate: string }) => {
  return (
    <div className="flex w-full flex-col gap-y-sm">
      <Label state="required" size="sm" type="default">
        Date of Birth
      </Label>
      <InputDatePicker
        name="birth_of_date"
        value={birthOfDate}
        placeholder="Select date of birth"
        dateFormat="yyyy-MM-dd"
        displayFormat="dd MMM yyyy"
        size="sm"
      />
    </div>
  )
}

export default BirthOfDate
