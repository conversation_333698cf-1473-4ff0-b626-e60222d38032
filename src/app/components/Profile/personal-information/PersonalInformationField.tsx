"use client"

import Input from "@app/components/shared/Form/Input"
import { TMember } from "types/member.type"

import BirthOfDate from "./BirthOfDate"
import PersonalPhoneNumberInput from "./PersonalPhoneNumberInput"

const PersonalInformationField = ({
  firstNameError,
  member,
}: {
  firstNameError: string
  member: TMember | undefined
}) => {
  const isDanger = firstNameError ? "danger" : undefined

  return (
    <div className="flex flex-col gap-y-base">
      <div className="flex flex-col gap-xs md:flex-row md:justify-between">
        <Input
          state="required"
          label="First Name"
          placeholder="Enter your first name"
          defaultValue={member?.firstName}
          name="first_name"
          variant={isDanger}
          helperText={firstNameError}
        />
        <Input
          label="Last Name"
          placeholder="Enter your last name"
          name="last_name"
          defaultValue={member?.lastName}
        />
      </div>
      <div className="flex flex-col gap-xs md:flex-row md:justify-between">
        <div className="flex w-full flex-col gap-y-sm sm:w-1/2">
          <PersonalPhoneNumberInput phoneNumber={member?.phoneNumber ?? ""} />
        </div>
        <div className="flex w-full flex-col gap-y-sm sm:w-1/2">
          <BirthOfDate birthOfDate={member?.birthOfDate ?? ""} />
        </div>
      </div>

      <input name="province_id" className="hidden" value={member?.provinceId} />
      <input
        name="is_seller_on_vacation"
        className="hidden"
        value={String(member?.isSellerOnVacation)}
      />
      <input name="email" className="hidden" value={member?.email} />
      <input name="id" className="hidden" value={member?.id} />
      <input name="gender" className="hidden" value={member?.gender} />
      <input
        name="phone_number"
        className="hidden"
        value={member?.phoneNumber}
      />
      <input
        name="referral_code"
        className="hidden"
        value={member?.referralCode}
      />
      <input name="region_id" className="hidden" value={member?.regionId} />
      <input name="country_id" className="hidden" value={member?.countryId} />
      <input
        name="is_active"
        className="hidden"
        value={String(member?.isActive)}
      />
      <input
        name="seller_point"
        className="hidden"
        value={member?.sellerPoint}
      />
      <Input
        state="required"
        label="Email"
        placeholder="Enter your email address"
        value={member?.email}
        disabled
        name="email"
      />
    </div>
  )
}

export default PersonalInformationField
