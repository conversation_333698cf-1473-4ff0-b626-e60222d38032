"use client"

import React, { useEffect, useRef, useActionState, useState } from "react"
import Avatar from "@kickavenue/ui/components/Avatar"
import Text from "@kickavenue/ui/components/Text"
import Divider from "@kickavenue/ui/components/Divider"
import { updateById } from "@infrastructure/actions/member"
import ButtonSave from "@components/shared/Form/ButtonSave"
import useToast from "@app/hooks/useToast"

import PersonalInformationField from "./PersonalInformationField"
import { useMemberStore } from "stores/memberStore"

const PersonalInformation = () => {
  const { member } = useMemberStore()
  const ref = useRef<HTMLInputElement>(null)

  const [imagePreview, setImagePreview] = useState<string | null>(null)

  const fullName = `${member?.firstName} ${member?.lastName}`
  const [state, formAction] = useActionState(updateById, { errors: {} })

  const handleUpload = () => {
    if (ref.current) {
      ref.current.click()
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"]
    if (file && !allowedTypes.includes(file.type)) {
      setShowToast(true, "Only JPG, JPEG, PNG files are allowed", "danger")
      return
    }

    if (file && file.size > 20 * 1024 * 1024) {
      setShowToast(true, "Image size must be less than 20MB", "danger")
      return
    }

    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const { setShowToast } = useToast()
  const showErrors = () => {
    if ("errors" in state) {
      return state.errors
    }
  }

  useEffect(() => {
    if ("id" in state) {
      setShowToast(true, "Profile has been successfully updated.")
    }
  }, [setShowToast, state])

  useEffect(() => {
    setImagePreview(member?.image)
  }, [member])

  return (
    <>
      <form action={formAction} className="flex h-full flex-col">
        <div className="flex w-full flex-col items-center gap-y-base">
          <Avatar
            size="xxxxl"
            name={fullName}
            handleEdit={handleUpload}
            url={imagePreview ?? undefined}
            isEdit
          />
          <input
            ref={ref}
            id="image"
            type="file"
            className="hidden"
            name="member_image"
            onChange={handleFileChange}
          />
          <div className="text-center">
            <Text size="xs" type="regular" state="secondary">
              Only JPG, JPEG, PNG
            </Text>
            <Text size="xs" type="regular" state="secondary">
              Max 20mb
            </Text>
          </div>
        </div>
        <div className="my-lg">
          <Divider orientation="horizontal" state="default" />
        </div>
        <div className="mb-4 grow">
          <PersonalInformationField
            firstNameError={showErrors()?.firstName as unknown as string}
            member={member}
          />
        </div>
        <ButtonSave />
      </form>
    </>
  )
}

export default PersonalInformation
