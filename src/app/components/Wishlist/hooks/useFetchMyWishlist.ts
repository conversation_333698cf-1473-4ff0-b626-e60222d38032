import useAuthSession from "@app/hooks/useAuthSession"
import useQuery from "@app/hooks/useQuery"
import { GetMyWishlist } from "@application/usecases/getMyWishlist"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { WishlistApiRepository } from "@infrastructure/repositories/wishlistApiRepository"
import { TWishlistParams } from "types/wishlist.type"

const useFetchMyWishlist = (filter?: TWishlistParams) => {
  const { isAuthenticated } = useAuthSession()

  const fetchMyWishlist = async () => {
    const r = new WishlistApiRepository()
    const u = new GetMyWishlist(r)
    const res = await u.getMy(filter)
    return res
  }

  return useQuery({
    queryKey: [QueryKeysConstant.GET_MY_WISHLIST],
    queryFn: fetchMyWishlist,
    refetchOnWindowFocus: true,
    enabled: isAuthenticated,
  })
}

export default useFetchMyWishlist
