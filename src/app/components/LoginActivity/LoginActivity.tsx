/* eslint-disable no-warning-comments */
"use client"

import { Button, IconArrowLeftOutline, Text } from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import React, { useState } from "react"
import ListItemSetting from "@components/Settings/ListItemSetting"
import { useModalStore } from "stores/modalStore"

import ModalLogOutDevice from "./modal/ModalLogOutDevice"

export default function LoginActivity() {
  const router = useRouter()
  const { setOpen } = useModalStore()
  const [isModalLogoutAllDevicesOpen, setIsModalLogoutAllDevicesOpen] =
    useState(false)

  const handleModal = (isLogoutAllDevices: boolean) => {
    setOpen(true)
    setIsModalLogoutAllDevicesOpen(isLogoutAllDevices)
  }
  return (
    <>
      <ModalLogOutDevice isLogoutForAllDevice={isModalLogoutAllDevicesOpen} />
      <div className="flex flex-col gap-4 p-sm lg:p-lg">
        <Button
          variant="link"
          IconLeft={() => <IconArrowLeftOutline className="text-gray-b-65" />}
          onClick={() => router.back()}
        >
          <Text size="sm" type="regular" state="secondary">
            Back
          </Text>
        </Button>
        <div className="flex items-center justify-between">
          <h5 className="text-heading-5 font-bold">Login Activity</h5>
          <Button size="sm" variant="link" onClick={() => handleModal(true)}>
            Log Out from all devices
          </Button>
        </div>
        <ListItemSetting
          title="Log Out from all devices"
          // TODO: implement watermark component
          leadingIcon={<div className="size-16 rounded-md bg-gray-w-90" />}
          description="Chrome, ************"
        />
        <ListItemSetting
          title="Last active Friday, 5th July 2024, 09:31 WIB"
          // TODO: implement watermark component
          leadingIcon={<div className="size-16 rounded-md bg-gray-w-90" />}
          trailingIcon={
            <Button
              size="sm"
              variant="secondary"
              onClick={() => handleModal(false)}
            >
              Exit
            </Button>
          }
          description="Chrome, ************"
        />
        <ListItemSetting
          title="Last active Friday, 5th July 2024, 09:31 WIB"
          // TODO: implement watermark component
          leadingIcon={<div className="size-16 rounded-md bg-gray-w-90" />}
          description="Chrome, ************"
          trailingIcon={
            <Button
              size="sm"
              variant="secondary"
              onClick={() => handleModal(false)}
            >
              Exit
            </Button>
          }
        />
        <ListItemSetting
          title="Last active Friday, 5th July 2024, 09:31 WIB"
          // TODO: implement watermark component
          leadingIcon={<div className="size-16 rounded-md bg-gray-w-90" />}
          description="Chrome, ************"
          trailingIcon={
            <Button
              size="sm"
              variant="secondary"
              onClick={() => handleModal(false)}
            >
              Exit
            </Button>
          }
        />
      </div>
    </>
  )
}
