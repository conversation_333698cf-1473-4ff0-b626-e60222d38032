import React from "react"
import {
  But<PERSON>,
  <PERSON>dalConfirm as ModalContaine<PERSON>,
} from "@kickavenue/ui/components"
import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"

export interface ModalLogOutDeviceProps {
  isLogoutForAllDevice?: boolean
  onConfirm?: () => void
}

export default function ModalLogOutDevice(props: ModalLogOutDeviceProps) {
  const { isLogoutForAllDevice = false, onConfirm } = props
  const { setOpen } = useModalStore()

  const title = isLogoutForAllDevice
    ? "Are you sure you want to log out from all devices?"
    : "Are you sure you want to log out from this device?"
  const subtitle = isLogoutForAllDevice
    ? "Your account will be logged out from all devices except this devices you're using right now."
    : "Your account will be logged out from this device."

  return (
    <Modal>
      <ModalContainer
        title={title}
        subtitle={subtitle}
        open
        slotAction={
          <>
            <Button variant="secondary" onClick={() => setOpen(false)}>
              No, Cancel
            </Button>
            <Button onClick={onConfirm}>Yes, Log Out</Button>
          </>
        }
      />
    </Modal>
  )
}
