import React from "react"
import Empty from "@kickavenue/ui/components/Empty"
import { TMessage } from "types/message.type"

import styles from "../MessageCenter.module.scss"

import MessageItem from "./MessageItem"

export interface MessageCenterContainerProps {
  list: TMessage[]
}

export default function MessageContainer(props: MessageCenterContainerProps) {
  const { list } = props

  return (
    <div className={styles["all-message"]}>
      {(list || []).map((item) => {
        return (
          <div key={item.id}>
            <MessageItem item={item} />
          </div>
        )
      })}
      {list.length === 0 && (
        <div className="flex h-full items-center justify-center">
          <Empty
            title="No New Messages"
            subText="Stay tuned! Your messages and notifications will appear here."
            className="[&>img]:w-[200px]"
          />
        </div>
      )}
    </div>
  )
}
