import React from "react"
import { BadgeDot, Divider } from "@kickavenue/ui/components"
import Text from "@kickavenue/ui/components/Text"
import Image from "next/image"
import { TMessage } from "types/message.type"
import { convertS3UrlToCloudFront } from "@utils/misc"

import styles from "../MessageCenter.module.scss"

export interface MessageItemProps {
  item: TMessage
}

export default function MessageItem(props: MessageItemProps) {
  const { item } = props

  const diffTime = new Date().getTime() - new Date(item.createdAt).getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 3600 * 24))
  const diffHours = Math.ceil(diffTime / (1000 * 3600))
  const renderTime =
    diffHours > 24 ? `${diffDays} days ago` : `${diffHours} hours ago`
  const renderBadge = item.activeTime ? null : <BadgeDot type="positive" />

  return (
    <div className="">
      <div className="flex w-full items-center p-xs">
        <div className="w-4">{renderBadge}</div>
        <div className="flex pl-sm pr-xl">
          {item.image && (
            <Image
              src={convertS3UrlToCloudFront(item.image)}
              className={styles["message-center-image-item"]}
              width={64}
              height={64}
              alt="Image"
            />
          )}
          <div className="flex flex-col justify-center overflow-hidden pl-md max-sm:max-w-40">
            <Text
              size="base"
              type="bold"
              state="primary"
              className="truncate text-nowrap"
            >
              {item.title}
            </Text>
            <Text
              size="base"
              type="regular"
              state="secondary"
              className="truncate text-nowrap"
            >
              {item.subtitle}
            </Text>
          </div>
        </div>
        <Text
          size="base"
          type="regular"
          state="primary"
          className="grow overflow-hidden truncate text-nowrap text-right"
        >
          {renderTime}
        </Text>
      </div>
      <div className="py-sm">
        <Divider orientation="horizontal" />
      </div>
    </div>
  )
}
