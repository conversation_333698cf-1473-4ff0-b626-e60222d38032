"use client"

import Tab from "@kickavenue/ui/components/Tab/Tab"
import { cx } from "class-variance-authority"
import { useTranslations } from "@app/i18n"

import styles from "./MessageCenter.module.scss"
import { useMessageCenter } from "./hooks/useMessageCenter"
import { MessageCenterProps } from "./utils/messagecenter.utils"

export default function MessageCenter(props: MessageCenterProps) {
  const { allMessages, unreadMessages } = props
  const { tabItems, tab, setTab, renderTabComponent } = useMessageCenter({
    allMessages,
    unreadMessages,
  })

  const translations = useTranslations()

  return (
    <>
      <div
        className={cx(styles["message-center"], "overflow-y-auto p-sm lg:p-lg")}
      >
        <h4 className="text-heading-4 font-bold text-gray-b-75">
          {translations("messageCenter")}
        </h4>
        <div className="my-lg overflow-hidden overflow-x-auto">
          <Tab className="!gap-x-xl lg:!w-full lg:!justify-start">
            {tabItems.map((item) => {
              return (
                <button
                  data-active={item === tab}
                  onClick={() => setTab(item)}
                  type="button"
                  key={item.name}
                  className="whitespace-nowrap"
                >
                  {item.name}
                </button>
              )
            })}
          </Tab>
        </div>
        {renderTabComponent()}
      </div>
    </>
  )
}
