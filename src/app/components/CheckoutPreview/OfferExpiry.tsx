"use client"

import { Chip, Label } from "@kickavenue/ui/components"
import { addDays, format } from "date-fns"
import React, { useState } from "react"
import { MiscConstant } from "@constants/misc"
import { useCreateOfferStore } from "stores/createOfferStore"

const offerExpiry = [7, 14, 30]

const OfferExpiry = () => {
  const { setField } = useCreateOfferStore()
  const [date, setDate] = useState<null | number>(null)
  const today = new Date()
  const handleSetdate = (date: number) => {
    setField(
      "expiredAt",
      String(format(addDays(today, date), MiscConstant.DATE_FORMAT_FULL)),
    )
    setDate(date)
  }
  return (
    <div className="mt-lg flex flex-col gap-y-base">
      <Label state="default" type="default" size="sm">
        Offer Expiry
      </Label>
      <div className="flex gap-x-xs">
        {offerExpiry.map((item) => (
          <Chip
            isSelected={item === date}
            size="md"
            key={item}
            onClick={() => handleSetdate(item)}
            className="cursor-pointer !rounded-sm"
          >
            {item} Days
          </Chip>
        ))}
      </div>
    </div>
  )
}

export default OfferExpiry
