"use client"

import { useEffect } from "react"
import { <PERSON><PERSON>, Divider, <PERSON> } from "@kickavenue/ui/components"
import { useMiscStore } from "stores/miscStore"
import { useListingItemStore } from "stores/listingItemStore"
import { formatPrice, getStripAmount } from "@utils/misc"
import { getListingCondition } from "@utils/selling"
import Input from "@components/shared/Form/Input"
import { formatNumberWithSeparator } from "@utils/separator"
import { useBuyOrOfferStore } from "stores/buyOrOfferStore.ts"
import { TBuyingOfferText } from "types/buyingOffer.type"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { useCheckoutStore } from "stores/checkoutStore"

import ProductDetailPreview from "./ProductDetailPreview"
import PriceStatisticGrid from "./PriceStatisticGrid"

const { MAKE_OFFER } = TBuyingOfferText
const { CHECKOUT } = PageRouteConstant

const CheckoutPreviewDetailBuyNow = () => {
  const { setShowFooter } = useMiscStore()
  const { detail: listing } = useListingItemStore()
  const { setOption } = useBuyOrOfferStore()
  const { goToPage, isLoading } = useChangePage()
  const { setListing } = useCheckoutStore()

  useEffect(() => setShowFooter(true), [setShowFooter])

  const handleContinueToCheckout = () => {
    setListing(listing)
    goToPage(CHECKOUT)
  }

  return (
    <div className="bg-gray-w-95 p-[10px] pt-lg md:flex md:justify-center md:pb-[320px]">
      <div className="rounded-sm bg-white px-sm pt-sm md:p-0">
        <div className="flex flex-col gap-lg pb-lg md:p-lg">
          <ProductDetailPreview
            productData={listing?.item}
            size={listing?.size}
            listing={listing}
            listingCondition={getListingCondition(listing)}
            className="md:min-w-[500px]"
          />
          <Divider orientation="horizontal" state="default" />
          <PriceStatisticGrid
            lowestAsk={formatPrice(
              Number(listing?.sellingPrice?.amountText),
              null,
              "IDR",
            )}
            highestOffer={formatPrice(listing?.highestOffer, null, "IDR")}
          />
          <Input
            size="sm"
            state="default"
            label="Price"
            prefix="IDR"
            value={formatNumberWithSeparator(
              getStripAmount(listing?.sellingPrice),
              ",",
            )}
            onChange={() => setOption(MAKE_OFFER)}
          />
        </div>
        <Divider orientation="horizontal" state="default" />
        <div className="p-lg">
          <div className="mb-sm text-center">
            <Text size="sm" type="regular" state="secondary">
              The total payment amount is calculated on the next screen.
            </Text>
          </div>
          <Button
            size="lg"
            variant="primary"
            style={{ width: "100%" }}
            onClick={handleContinueToCheckout}
            disabled={isLoading?.[CHECKOUT]}
          >
            Continue to Checkout
          </Button>
        </div>
      </div>
    </div>
  )
}

export default CheckoutPreviewDetailBuyNow
