import Chip from "@kickavenue/ui/dist/src/components/Chip/Chip"
import Label from "@kickavenue/ui/dist/src/components/Label"
import { Controller, useFormContext } from "react-hook-form"
import { FormEvent, useCallback } from "react"
import Input from "@components/shared/Form/Input"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { FormFieldConstant } from "@constants/formField"
import { formatNumberWithSeparator } from "@utils/separator"

import useOfferPriceInputStatus from "../hooks/useOfferPriceInputStatus"

const { MAKE_OFFER_INFO_MODAL } = ModalConstant.MODAL_IDS

const { OFFER_PRICE, EXPIRED_AT } =
  FormFieldConstant.CHECKOUT_PREVIEW_MAKE_OFFER

const offerExpiry = [7, 14, 30]

interface TCheckoutPreviewMakeOfferFormProps {
  highestOffer: number
  lowestAsk: number
}

const CheckoutPreviewMakeOfferForm = ({
  highestOffer,
  lowestAsk,
}: TCheckoutPreviewMakeOfferFormProps) => {
  const { control, register, setValue, watch } = useFormContext()
  const { setOpen } = useModalStore()
  const expiredAt = watch(EXPIRED_AT.KEY)

  const handleQuestionMarkClick = () => {
    setOpen(true, MAKE_OFFER_INFO_MODAL)
  }

  const isChipSelected = useCallback(
    (value: number) => {
      return Number(expiredAt) === value
    },
    [expiredAt],
  )

  const { variant, rightIcon, helperText } = useOfferPriceInputStatus({
    highestOffer,
    lowestAsk,
  })

  const handleOnPriceChange = useCallback(
    (e: FormEvent<HTMLInputElement>, onChange: (value: string) => void) => {
      const val = (e.currentTarget as HTMLInputElement).value
      const num = Number(val.replace(/\D/g, ""))
      onChange(num.toString())
    },
    [],
  )

  const handleChipClick = useCallback(
    (value: number) => {
      setValue(EXPIRED_AT.KEY, value, { shouldValidate: true })
    },
    [setValue],
  )

  return (
    <>
      <Controller
        name={OFFER_PRICE.KEY}
        control={control}
        rules={{ required: true, validate: (value) => value > 0 }}
        render={({ field }) => (
          <Input
            size="sm"
            label={OFFER_PRICE.NAME}
            onClickQuestionMark={handleQuestionMarkClick}
            variant={variant}
            rightIcon={rightIcon}
            helperText={helperText}
            prefix="IDR"
            {...field}
            value={formatNumberWithSeparator(field.value || 0, ",")}
            onChange={(e) => handleOnPriceChange(e, field.onChange)}
          />
        )}
      />
      <input type="hidden" {...register(EXPIRED_AT.KEY, { required: true })} />
      <div className="flex flex-col gap-y-base">
        <Label state="default" type="default" size="sm">
          Offer Expiry
        </Label>
        <div className="flex gap-x-xs">
          {offerExpiry.map((item) => (
            <Chip
              size="md"
              key={item}
              isSelected={isChipSelected(item)}
              onClick={() => handleChipClick(item)}
              className="cursor-pointer !rounded-sm"
            >
              {item} Days
            </Chip>
          ))}
        </div>
      </div>
    </>
  )
}

export default CheckoutPreviewMakeOfferForm
