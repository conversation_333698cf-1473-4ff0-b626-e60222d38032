"use client"

import {
  IconArrowRightOutline,
  IconSuccessCircleBulk,
} from "@kickavenue/ui/dist/src/components/icons"
import {
  Button,
  Divider,
  Heading,
  Text,
} from "@kickavenue/ui/dist/src/components"
import Link from "next/link"
import { formatCurrency } from "@utils/separator"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"

const { PROFILE_BUYING } = PageRouteConstant

const CheckoutPreviewMakeOfferSuccess = ({
  children,
  offerPrice,
}: {
  children: React.ReactNode
  offerPrice: number
}) => {
  const { goToPage, isLoading } = useChangePage()

  return (
    <div className="bg-gray-w-95 p-[10px] pt-lg md:flex md:justify-center md:pb-[320px]">
      <div className="flex flex-col gap-lg rounded-sm bg-white pt-lg">
        <div className="flex flex-col items-center gap-lg">
          <IconSuccessCircleBulk className="size-16 text-success" />
          <Heading heading="4" textStyle="bold">
            Success!
          </Heading>
          <div className="flex flex-col items-center gap-xs">
            <Text size="base" state="secondary" type="regular">
              Your offer has been successfully placed!
            </Text>
            <Text size="base" state="primary" type="bold">
              {formatCurrency(offerPrice || 0, ",", "IDR")}
            </Text>
          </div>
        </div>
        {children}
        <Divider orientation="horizontal" />
        <div className="flex justify-between gap-x-xs px-lg pb-lg">
          <Link href="/" className="w-full">
            <Button variant="secondary" className="!w-full">
              Back to Home Page
            </Button>
          </Link>
          <Button
            IconRight={IconArrowRightOutline}
            className="!w-full"
            onClick={() => goToPage(PROFILE_BUYING)}
            disabled={isLoading?.[PROFILE_BUYING]}
          >
            See My Offers
          </Button>
        </div>
      </div>
    </div>
  )
}

export default CheckoutPreviewMakeOfferSuccess
