import {
  Divider,
  But<PERSON>,
  Heading,
  Text,
} from "@kickavenue/ui/dist/src/components"
import {
  IconArrowRightOutline,
  IconDangerCircleBulk,
} from "@kickavenue/ui/dist/src/components/icons"
import Link from "next/link"
import { formatCurrency } from "@utils/separator"

const CheckoutPreviewMakeOfferError = ({
  offerPrice,
  children,
}: {
  offerPrice: number
  children: React.ReactNode
}) => {
  return (
    <div className="bg-gray-w-95 p-[10px] pt-lg md:flex md:justify-center md:pb-[320px]">
      <div className="flex flex-col gap-lg rounded-sm bg-white pt-lg">
        <div className="flex flex-col items-center gap-lg">
          <IconDangerCircleBulk className="size-16 text-danger" />
          <Heading heading="4" textStyle="bold">
            Failed!
          </Heading>
          <div className="flex flex-col items-center gap-xs">
            <Text size="base" state="secondary" type="regular">
              We&apos;re sorry, but your offer could not be placed.
            </Text>
            <Text size="base" state="primary" type="bold">
              {formatCurrency(offerPrice || 0, ",", "IDR")}
            </Text>
          </div>
        </div>
        {children}
        <Divider orientation="horizontal" />
        <div className="flex justify-between gap-x-xs px-lg pb-lg">
          <Link href="/" className="w-full">
            <Button variant="secondary" className="!w-full">
              Back to Home Page
            </Button>
          </Link>
          <Link href="/profile/buying" className="w-full">
            <Button IconRight={IconArrowRightOutline} className="!w-full">
              See My Offers
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default CheckoutPreviewMakeOfferError
