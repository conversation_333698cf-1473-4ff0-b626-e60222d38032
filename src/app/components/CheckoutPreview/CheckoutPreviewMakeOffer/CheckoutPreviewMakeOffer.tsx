"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconKickCreditBulkColor,
  Text,
} from "@kickavenue/ui/components"
import { useParams, useSearchParams } from "next/navigation"
import { useEffect, useMemo } from "react"
import { FormProvider } from "react-hook-form"
import Link from "next/link"
import { formatCurrency } from "@utils/separator"
import { getBooleanValue } from "@utils/query.utils"
import { ECheckoutPreviewSearchKey } from "types/misc.type"
import useGetProductById from "@app/hooks/useGetProductById"
import { useListingItemStore } from "stores/listingItemStore"
import useGetSizeById from "@app/hooks/useGetSizeById"
import { formatPrice, getStripAmount } from "@utils/misc"
import useFetchMyBalance from "@app/hooks/useFetchMyBalance"
import { OfferConstant } from "@constants/offer.constant"
import Loading from "@components/shared/Loading"
import { FormFieldConstant } from "@constants/formField"
import { TOfferPayload } from "types/offer.type"
import { QueryStatus } from "types/network.type"
import { useBuyOrOfferStore } from "stores/buyOrOfferStore.ts"

import ProductDetailPreview from "../ProductDetailPreview"
import PriceStatisticGrid from "../PriceStatisticGrid"
import MakeOfferInformationModal from "../MakeOfferInformationModal"
import { getCheckoutData } from "../utils/getCheckoutData"
import useCheckoutPreviewProduct from "../hooks/useCheckoutPreviewProduct"
import usePriceStatistic from "../hooks/usePriceStatistic"
import useMakeOfferForm from "../hooks/useMakeOfferForm"

import CheckoutPreviewMakeOfferForm from "./CheckoutPreviewMakeOfferForm"
import CheckoutPreviewMakeOfferSuccess from "./CheckoutPreviewMakeOfferSuccess"
import CheckoutPreviewMakeOfferError from "./CheckoutPreviewMakeOfferError"

const { Key } = ECheckoutPreviewSearchKey
const { MIN_USER_BALANCE } = OfferConstant

const { OFFER_PRICE } = FormFieldConstant.CHECKOUT_PREVIEW_MAKE_OFFER
const { Success, Error } = QueryStatus

// eslint-disable-next-line max-lines-per-function
const CheckoutPreviewMakeOffer = () => {
  const params = useParams<{ listingid: string }>()
  const listingid = params?.listingid
  const { detail: listing } = useListingItemStore()
  const { setIsShowSegmentedBuyOffer } = useBuyOrOfferStore()

  const searchParams = useSearchParams()
  const key = getBooleanValue(searchParams?.get(Key))

  const itemId = useMemo(() => {
    if (key || listing?.id) return 0
    return listingid ? parseInt(listingid, 10) : 0
  }, [key, listingid, listing?.id])

  const sizeId = useMemo(() => {
    if (key || listing?.id) return 0
    return getCheckoutData()?.sizeId ?? 0
  }, [key, listing?.id])

  const { data: product } = useGetProductById({ id: itemId })
  const { data: size } = useGetSizeById({ id: sizeId })
  const { data: balance } = useFetchMyBalance()

  const isCreditLower = useMemo(() => {
    return Number(balance?.kickCredit.amount) < MIN_USER_BALANCE
  }, [balance])

  const {
    listingCondition,
    listingForPreview,
    sizeForPreview,
    productForPreview,
  } = useCheckoutPreviewProduct(size, product)

  const { lowestAsk, highestOffer } = usePriceStatistic()

  const { form, onFormValid, disabled, isPending, status } = useMakeOfferForm({
    lowestAsk,
    userBalance: getStripAmount(balance?.kickCredit),
  })

  const offerPrice = Number(form.watch(OFFER_PRICE.KEY as keyof TOfferPayload))
  const sellingPrice = Number(listing?.sellingPrice?.amountText)

  useEffect(() => {
    if ([Success, Error].includes(status as QueryStatus)) {
      setIsShowSegmentedBuyOffer(false)
    }
  }, [status, setIsShowSegmentedBuyOffer])

  if (isPending) return <Loading />

  if (status === Success) {
    return (
      <CheckoutPreviewMakeOfferSuccess offerPrice={offerPrice}>
        <div className="mx-lg rounded-xl border border-solid border-gray-w-80 p-sm">
          <ProductDetailPreview
            productData={productForPreview}
            size={sizeForPreview}
            listing={listingForPreview}
            listingCondition={listingCondition}
          />
        </div>
      </CheckoutPreviewMakeOfferSuccess>
    )
  }

  if (status === Error) {
    return (
      <CheckoutPreviewMakeOfferError offerPrice={offerPrice}>
        <div className="mx-lg rounded-xl border border-solid border-gray-w-80 p-sm">
          <ProductDetailPreview
            productData={productForPreview}
            size={sizeForPreview}
            listing={listingForPreview}
            listingCondition={listingCondition}
          />
        </div>
      </CheckoutPreviewMakeOfferError>
    )
  }

  return (
    <div className="bg-gray-w-95 p-[10px] pt-lg md:flex md:justify-center md:pb-[320px]">
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onFormValid)}>
          <div className="rounded-sm bg-white">
            <div className="flex flex-col gap-lg md:p-lg">
              <ProductDetailPreview
                productData={productForPreview}
                size={sizeForPreview}
                listing={listingForPreview}
                listingCondition={listingCondition}
              />
              <Divider orientation="horizontal" state="default" />
              <PriceStatisticGrid
                lowestAsk={formatPrice(sellingPrice, null, "IDR")}
                highestOffer={formatPrice(highestOffer, null, "IDR")}
              />
              <CheckoutPreviewMakeOfferForm
                highestOffer={highestOffer}
                lowestAsk={sellingPrice}
              />
              <Divider orientation="horizontal" state="default" />
              <div className="flex flex-col gap-lg">
                <Alert
                  isIcon
                  subTitle="We'll hold IDR 25,000 from your Kick Credit Balance as a deposit for this offer."
                  variant="information"
                  className="!w-full"
                />
                <div className="flex w-full items-center gap-sm rounded-xl border border-solid border-gray-w-80 p-sm">
                  <IconKickCreditBulkColor className="size-6" />
                  <div className="flex flex-col gap-xxs">
                    <Text size="sm" state="primary" type="bold">
                      Kick Credit
                    </Text>
                    <Text size="sm" state="secondary" type="regular">
                      {formatCurrency(
                        Number(balance?.kickCredit.amount) || 0,
                        ",",
                        "IDR",
                      )}
                    </Text>
                    {isCreditLower && (
                      <div className="flex items-center gap-xxs">
                        <Text size="sm" state="danger" type="regular">
                          Insufficient Balance
                        </Text>
                        <Link href="/topup/confirmation">
                          <Button variant="link" className="!p-0">
                            Top Up Balance
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <Divider orientation="horizontal" />
            <div className="p-lg">
              <Button
                disabled={disabled}
                size="lg"
                variant="primary"
                style={{ width: "100%" }}
                type="submit"
              >
                Make Offer
              </Button>
            </div>
          </div>
        </form>
      </FormProvider>
      <MakeOfferInformationModal />
    </div>
  )
}

export default CheckoutPreviewMakeOffer
