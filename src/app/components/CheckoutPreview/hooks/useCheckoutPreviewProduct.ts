import { useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { generateListingData } from "@components/Checkout/utils/checkout.utils"
import { getListingCondition } from "@utils/selling"
import { getBooleanValue } from "@utils/query.utils"
import { useListingItemStore } from "stores/listingItemStore"
import { TSize } from "types/size.type"
import { ECheckoutPreviewSearchKey } from "types/misc.type"
import { Product } from "types/product.type"

import { getCheckoutData } from "../utils/getCheckoutData"

const { Key } = ECheckoutPreviewSearchKey

const useCheckoutPreviewProduct = (size?: TSize, product?: Product) => {
  const { detail: listing } = useListingItemStore()
  const searchParams = useSearchParams()
  const key = getBooleanValue(searchParams?.get(Key))

  const listingCondition = useMemo(() => {
    if (!key || !listing?.id) {
      return "Brand New, Perfect Box"
    }
    return getListingCondition(listing)
  }, [key, listing])

  const listingForPreview = useMemo(() => {
    if (!key || !listing?.id) {
      return generateListingData({
        shippingText: getCheckoutData()?.shippingText,
      })
    }
    return listing
  }, [key, listing])

  const sizeForPreview = useMemo(() => {
    if (!key || !listing?.id) {
      return size
    }
    return listing?.size
  }, [key, listing, size])

  const productForPreview = useMemo(() => {
    if (!key || !listing?.id) {
      return product
    }
    return listing?.item
  }, [key, listing, product])

  return {
    listingCondition,
    listingForPreview,
    sizeForPreview,
    productForPreview,
  }
}

export default useCheckoutPreviewProduct
