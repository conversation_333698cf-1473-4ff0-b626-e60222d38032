import { useRouter, useSearchParams } from "next/navigation"
import { useEffect } from "react"
import useCheckoutPreviewStore from "stores/useCheckoutPreviewStore"

const useLoadCheckoutData = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const key = searchParams?.get("search")
  const setCheckoutData = useCheckoutPreviewStore(
    (state) => state.setCheckoutData,
  )
  const clearCheckoutData = useCheckoutPreviewStore(
    (state) => state.clearCheckoutData,
  )

  useEffect(() => {
    const storedData = localStorage.getItem("checkoutData")

    if (storedData && key === "false") {
      const { expiryTime } = JSON.parse(storedData)

      if (Date.now() > expiryTime) {
        clearCheckoutData()
        router.push("/product-detail")
      }
    }
  }, [clearCheckoutData, setCheckoutData, router, key])
}

export default useLoadCheckoutData
