import { useMemo } from "react"
import { useFormContext } from "react-hook-form"
import {
  IconSuccessCircleBulk,
  IconWarningCircleBulk,
} from "@kickavenue/ui/dist/src/components/icons"
import { TInputProps } from "@kickavenue/ui/dist/src/components"
import { FormFieldConstant } from "@constants/formField"
import { formatNumberWithSeparator } from "@utils/separator"
import { OfferConstant } from "@constants/offer.constant"

const { OFFER_PRICE } = FormFieldConstant.CHECKOUT_PREVIEW_MAKE_OFFER

const offerPriceMultiplier = OfferConstant.OFFER_PRICE_MULTIPLIER

const useOfferPriceInputStatus = ({
  highestOffer,
  lowestAsk,
}: {
  highestOffer: number
  lowestAsk: number
}) => {
  const { watch } = useFormContext()
  const offerPrice = Number(watch(OFFER_PRICE.KEY))

  const variant: TInputProps["variant"] = useMemo(() => {
    if (!offerPrice || offerPrice === 0) {
      return undefined
    }
    if (offerPrice % offerPriceMultiplier !== 0) {
      return "danger"
    }
    if (lowestAsk > 0 && offerPrice > lowestAsk) {
      return "danger"
    }
    if (highestOffer > 0 && offerPrice < highestOffer) {
      return "warning"
    }
    if (!highestOffer || highestOffer === 0 || offerPrice > highestOffer) {
      return "success"
    }
    return undefined
  }, [highestOffer, lowestAsk, offerPrice])

  const rightIcon = useMemo(() => {
    if (variant === "success") {
      return <IconSuccessCircleBulk />
    }
    if (variant === "warning") {
      return <IconWarningCircleBulk />
    }
    return null
  }, [variant])

  const helperText = useMemo(() => {
    if (variant === "success") {
      return "You have the highest offer"
    }
    if (variant === "warning") {
      return "Your offer is lower than the highest offer"
    }
    if (variant === "danger" && offerPrice % offerPriceMultiplier !== 0) {
      return `The amount entered must be a multiple of ${formatNumberWithSeparator(
        offerPriceMultiplier,
        ",",
      )}`
    }
    if (variant === "danger" && offerPrice > lowestAsk) {
      return "You cannot put an offer higher than the lowest ask"
    }
    return undefined
  }, [variant, offerPrice, lowestAsk])

  return {
    variant,
    rightIcon,
    helperText,
  }
}

export default useOfferPriceInputStatus
