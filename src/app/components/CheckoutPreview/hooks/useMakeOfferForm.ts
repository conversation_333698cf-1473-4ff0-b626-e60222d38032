/* eslint-disable @typescript-eslint/naming-convention */

import { useMutation } from "@tanstack/react-query"
import { useSearchParams } from "next/navigation"
import { useCallback, useMemo } from "react"
import { useForm } from "react-hook-form"
import { CreateOffer } from "@application/usecases/createOffer"
import { FormFieldConstant } from "@constants/formField"
import { OfferConstant } from "@constants/offer.constant"
import { OfferApiRepository } from "@infrastructure/repositories/offerApiRepository"
import { getBooleanValue } from "@utils/query.utils"
import { useListingItemStore } from "stores/listingItemStore"
import { EShippingType } from "types/itemListing.type"
import { ECheckoutPreviewSearchKey } from "types/misc.type"
import {
  EDurationOffer,
  TCreateOfferPayload,
  TOfferPayload,
} from "types/offer.type"

import { getCheckoutData } from "../utils/getCheckoutData"

const { OFFER_PRICE, EXPIRED_AT } =
  FormFieldConstant.CHECKOUT_PREVIEW_MAKE_OFFER
const { OFFER_PRICE_MULTIPLIER, MIN_USER_BALANCE } = OfferConstant

const OFFER_DURATION_MAP = {
  7: EDurationOffer.SevenDay,
  14: EDurationOffer.FourteenDay,
  30: EDurationOffer.ThirtyDay,
}

const { Key } = ECheckoutPreviewSearchKey

const useMakeOfferForm = ({
  lowestAsk,
  userBalance,
}: {
  lowestAsk: number
  userBalance?: number
}) => {
  const form = useForm<TOfferPayload>({
    defaultValues: { [OFFER_PRICE.KEY]: "0", [EXPIRED_AT.KEY]: "" },
  })
  const { watch, formState } = form
  const offerPrice = Number(watch(OFFER_PRICE.KEY as keyof TOfferPayload))
  const searchParams = useSearchParams()
  const key = getBooleanValue(searchParams?.get(Key))
  const { detail: listing } = useListingItemStore()

  const isConsignment = useMemo(() => {
    if (key && listing?.id) return listing.isConsignment || listing.isConsigment
    if (getCheckoutData()?.shippingText === EShippingType.Express) return true
    return false
  }, [key, listing])

  const isPreOrder = useMemo(() => {
    if (key && listing?.id) return listing.isPreOrder
    if (getCheckoutData()?.shippingText === EShippingType.PreOrder) return true
    return false
  }, [key, listing])

  const isNewNoDefect = useMemo(() => {
    if (key && listing?.id) return listing.isNewNoDefect
    return true
  }, [key, listing])

  const itemId = useMemo(() => {
    if (key && listing?.id) return listing.item?.id
    return getCheckoutData()?.itemId
  }, [key, listing])

  const sizeId = useMemo(() => {
    if (key && listing?.id) return listing.size?.id
    return getCheckoutData()?.sizeId
  }, [key, listing])

  const submitCreateOffer = async (payload: TCreateOfferPayload) => {
    const r = new OfferApiRepository()
    const u = new CreateOffer(r)
    const resp = await u.execute(payload)
    return resp
  }

  const {
    mutate: createOffer,
    isPending,
    status,
  } = useMutation({
    mutationFn: submitCreateOffer,
  })

  const onFormValid = useCallback(
    (data: TOfferPayload) => {
      const commonFields = {
        amount: Number(data.offerPrice),
        isNewNoDefect,
        expiredAt:
          OFFER_DURATION_MAP[
            Number(data.expiredAt) as keyof typeof OFFER_DURATION_MAP
          ],
      }

      const createOfferPayload: TCreateOfferPayload = isNewNoDefect
        ? {
            ...commonFields,
            itemId: itemId as number,
            sizeId: sizeId as number,
            isConsigment: isConsignment,
            isConsignment,
            isPreOrder,
          }
        : {
            ...commonFields,
            sellerListingId: listing?.id as number,
          }

      createOffer(createOfferPayload)
    },
    [
      itemId,
      sizeId,
      listing,
      isConsignment,
      isPreOrder,
      isNewNoDefect,
      createOffer,
    ],
  )

  const disabled = useMemo(() => {
    if (isPending) return true
    if (!formState.isValid) return true
    if (lowestAsk > 0 && offerPrice > lowestAsk) return true
    if (offerPrice % OFFER_PRICE_MULTIPLIER !== 0) return true
    if (!userBalance) return true
    if (userBalance < MIN_USER_BALANCE) return true
    return false
  }, [offerPrice, lowestAsk, formState.isValid, userBalance, isPending])

  return { form, onFormValid, disabled, isPending, status }
}

export default useMakeOfferForm
