import { useParams, useSearchParams } from "next/navigation"
import { useMemo } from "react"
import useFetchItemSummary from "@app/hooks/useFetchItemSummary"
import { ECheckoutPreviewSearchKey } from "types/misc.type"
import { getBooleanValue } from "@utils/query.utils"
import { useListingItemStore } from "stores/listingItemStore"
import {
  getItemSizeHighestOffer,
  getItemSizeLowestAsk,
  mapItemSummaryToRawType,
} from "@utils/product.utils"
import { getShippingType } from "@utils/listingItem"
import { getStripAmount } from "@utils/misc"

import { getCheckoutData } from "../utils/getCheckoutData"

const { Key } = ECheckoutPreviewSearchKey

const usePriceStatistic = () => {
  const searchParams = useSearchParams()
  const key = getBooleanValue(searchParams?.get(Key))
  const params = useParams<{ itemid: string }>()
  const itemid = params?.itemid
  const { detail: listing } = useListingItemStore()

  const itemId = useMemo(() => {
    if (!key || !listing?.id) return itemid ? parseInt(itemid, 10) : 0
    // no need to fetch item summary if listing exists
    return 0
  }, [key, listing, itemid])

  const sizeId = useMemo(() => {
    if (!key || !listing?.id) return getCheckoutData()?.sizeId as number
    return listing?.size?.id
  }, [listing, key])

  const { data } = useFetchItemSummary(itemId || 0)

  const { lowestAsk, highestOffer } = useMemo(() => {
    if (listing?.id) {
      return {
        lowestAsk: listing?.lowestAsk,
        highestOffer: listing?.highestOffer,
      }
    }

    const itemSummary = mapItemSummaryToRawType(data)
    const shippingType = getShippingType(listing)
    const lowestAsk = getItemSizeLowestAsk({
      itemSummary,
      size: sizeId,
      shippingType,
    })
    const highestOffer = getItemSizeHighestOffer({
      itemSummary,
      size: sizeId,
      shippingType,
    })

    return {
      highestOffer: getStripAmount(highestOffer),
      lowestAsk: getStripAmount(lowestAsk),
    }
  }, [data, sizeId, listing])

  return { highestOffer, lowestAsk }
}

export default usePriceStatistic
