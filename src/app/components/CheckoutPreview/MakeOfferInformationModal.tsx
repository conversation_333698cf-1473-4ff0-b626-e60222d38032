import React from "react"
import {
  Modal as <PERSON>dal<PERSON><PERSON>r,
  IconCloseOutline,
  IconQuestionmarkCircleBulk,
  Text,
  Divider,
  Space,
} from "@kickavenue/ui/components"
import Modal from "@shared/Modal"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

import {
  makeOfferInformationHeader,
  makeOfferInformationBody,
} from "./makeOfferInformationModal.utils"

export default function MakeOfferInformationModal() {
  const { setOpen } = useModalStore()

  return (
    <Modal modalId={ModalConstant.MODAL_IDS.MAKE_OFFER_INFO_MODAL}>
      <ModalContainer
        title={makeOfferInformationHeader.title}
        trailing={
          <IconCloseOutline
            className="cursor-pointer"
            onClick={() => setOpen(false)}
          />
        }
        open
      >
        <div className="flex flex-col items-center justify-center gap-y-md">
          <IconQuestionmarkCircleBulk className="size-[60px] text-gray-w-40" />
          <Text state="secondary" size="base" type="medium">
            {makeOfferInformationHeader.body}
          </Text>
          <Divider orientation="horizontal" />
          <div className="flex flex-col gap-y-xs text-start">
            {makeOfferInformationBody.map(({ title, body }) => (
              <div key={title}>
                <h6 className="text-base font-bold">{title}</h6>
                <Text state="secondary" size="base" type="medium">
                  {body}
                </Text>
                <Space size="xs" direction="x" type="margin" />
              </div>
            ))}
          </div>
        </div>
      </ModalContainer>
    </Modal>
  )
}
