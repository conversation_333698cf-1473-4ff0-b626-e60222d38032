"use client"

import { Divider, Text, Watermark } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { Product } from "types/product.type"
import { getProductImageUrl } from "@utils/misc"
import { TSize } from "types/size.type"
import { SellerListing } from "types/sellerListing"
import { TListingItem } from "types/listingItem.type"
import ProductImage from "@components/shared/ProductImage"

import RenderListingBadge from "./RenderListingBadge"

interface ProductDetailPreviewProps {
  itemName?: string
  size?: TSize
  className?: string
  listing?: SellerListing | TListingItem | null
  productData?: Product
  listingCondition?: string
}

const ProductDetailPreview: React.FC<ProductDetailPreviewProps> = ({
  itemName,
  size,
  className,
  listing,
  productData,
  listingCondition,
}) => {
  const product = (listing?.item as Product) || productData
  const us = size?.us ?? listing?.size?.us
  const usSize = us ? `US ${us}` : "-"

  const image = getProductImageUrl(product)
  const displayName = itemName ?? product?.name

  return (
    <div className={cx("flex gap-xs md:w-full", className)}>
      <div className="shrink-0">
        {product && (
          <ProductImage
            containerProps={{
              className: "!size-[82px] !bg-transparent border border-gray-w-90",
            }}
            imageProps={{
              width: 82,
              height: 82,
              src: image,
              alt: "product",
            }}
          />
        )}
        {!product && (
          <div className="h-full w-fit rounded-base bg-gray-w-95">
            <Watermark
              className="size-full"
              style={{
                width: 82,
                height: 82,
              }}
              logoClassName="!bg-gray-w-95"
            />
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col justify-center gap-xs">
        <Text
          size="base"
          type="medium"
          state="primary"
          className="line-clamp-1 text-ellipsis"
        >
          {displayName}
        </Text>
        <Text size="sm" type="regular" state="secondary">
          {listingCondition}
        </Text>
        <div className="flex w-full flex-col gap-xs md:h-md md:flex-row md:items-center md:justify-between">
          <div className="flex h-[17px] items-center gap-xs">
            <Text size="sm" state="secondary" type="regular">
              SKU: {product?.skuCode}
            </Text>
            <Divider orientation="vertical" type="solid" />
            <Text size="sm" state="secondary" type="regular">
              Size: {usSize}
            </Text>
          </div>
          <RenderListingBadge listing={listing as SellerListing} />
        </div>
      </div>
    </div>
  )
}

export default ProductDetailPreview
