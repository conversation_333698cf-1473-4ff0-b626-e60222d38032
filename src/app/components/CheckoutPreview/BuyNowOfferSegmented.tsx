"use client"

import { Divider, Heading, Segmented } from "@kickavenue/ui/components"
import { ChangeEvent, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import CenterWrapper from "@shared/CenterWrapper"
import { TBuyingOfferText } from "types/buyingOffer.type"
import { useBuyOrOfferStore } from "stores/buyOrOfferStore.ts"
import { ECheckoutPreviewSearchKey } from "types/misc.type"
import { getBooleanValue } from "@utils/query.utils"

const { BUY_NOW, MAKE_OFFER } = TBuyingOfferText
const { Key } = ECheckoutPreviewSearchKey

const BuyNowOfferSegmented = () => {
  const {
    option,
    setOption,
    isShowSegmentedBuyOffer,
    setIsShowSegmentedBuyOffer,
  } = useBuyOrOfferStore()
  const active = option === BUY_NOW ? 0 : 1
  const searchParams = useSearchParams()
  const key = getBooleanValue(searchParams?.get(Key))

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setOption(value)
  }

  useEffect(() => {
    return () => {
      setIsShowSegmentedBuyOffer(true)
    }
  }, [setIsShowSegmentedBuyOffer])

  if (!key) {
    return (
      <>
        {isShowSegmentedBuyOffer && (
          <div className="z-10 w-full bg-white shadow-[0px_4px_8px_-2px_#24242414] md:sticky md:top-[112px]">
            <Divider orientation="horizontal" type="solid" />
            <CenterWrapper className="!py-base">
              <div className="col-span-12 flex justify-center">
                <Heading heading="4" textStyle="medium">
                  Make Offer
                </Heading>
              </div>
            </CenterWrapper>
          </div>
        )}
      </>
    )
  }

  return (
    <>
      {isShowSegmentedBuyOffer && (
        <div className="z-10 w-full bg-white shadow-[0px_4px_8px_-2px_#24242414] md:sticky md:top-[112px]">
          <Divider orientation="horizontal" type="solid" />
          <CenterWrapper className="!py-base">
            <div className="col-span-12 flex justify-center">
              <Segmented
                size="md"
                variant="buy"
                segments={[BUY_NOW, MAKE_OFFER]}
                onChange={handleChange}
                defaultActive={active}
              />
            </div>
          </CenterWrapper>
        </div>
      )}
    </>
  )
}

export default BuyNowOfferSegmented
