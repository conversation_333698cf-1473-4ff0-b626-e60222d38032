import Watermark from '@kickavenue/ui/components/Watermark'
import { getProductImageUrl } from '@utils/misc'
import { cx } from 'class-variance-authority'
import Image from 'next/image'
import React, { useState } from 'react'

type Props = { src: string }

const ProductImageWithFallback = (props: Props) => {
    const [imageError, setImageError] = useState(false)
    return (
        <>
            {
                imageError ?
                    <div
                        className={cx(
                            "min-h-[400px] !size-full items-center justify-center",
                            "rounded-lg bg-gray-w-95 py-20",
                        )}
                    >
                        <Watermark
                            className="size-full py-10 sm:py-20"
                            logoClassName="!bg-gray-w-95"
                        />
                    </div> :
                    <Image
                        alt="Product"
                        src={getProductImageUrl(props.src)}
                        sizes="100vw"
                        style={{
                            width: "100%",
                            height: "auto",
                        }}
                        width={500}
                        height={300}
                        placeholder="blur"
                        blurDataURL="https://placehold.co/500x300"
                        onError={() => {
                            setImageError(true)
                        }}
                    />
            }
        </>
    )
}

export default ProductImageWithFallback