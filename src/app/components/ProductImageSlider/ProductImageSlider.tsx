"use client"

import { useRef, useState } from "react"
import { TCarouselRef } from "@kickavenue/ui/components/Carousel/Carousel.type"
import { useProductStore } from "stores/productStore"
import Carousel from "@components/shared/Carousel"
import { Product } from "types/product.type"

import ProductImageSliderBottom from "./ProductImageSliderBottom"
import ProductImageWithFallback from "./ProductImageWithFallback"

interface ProductImageSliderProps {
  images?: string[]
  product?: Product
}

const ProductImageSlider = ({
  images,
  product: productProp,
}: ProductImageSliderProps) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const { detail: productFromStore } = useProductStore()
  const ref = useRef<TCarouselRef>(null)

  // Use product prop if provided, otherwise use from store
  const product = productProp || productFromStore
  return (
    <div className="md:sticky md:top-0 md:z-10">
      <Carousel
        className="mb-lg"
        size="md"
        variant="line"
        withBackground
        afterChange={setSelectedIndex}
        ref={ref}
      >
        {(images || product?.images || []).map((image) => (
          <div key={image} className="flex min-h-full items-center bg-gray-w-95 p-2">
            <ProductImageWithFallback src={image} />
          </div>
        ))}
      </Carousel>
      <ProductImageSliderBottom
        selectedIndex={selectedIndex}
        setSelectedIndex={(idx) => ref.current?.goTo(idx)}
        product={product}
        images={images}
      />
    </div>
  )
}

export default ProductImageSlider
