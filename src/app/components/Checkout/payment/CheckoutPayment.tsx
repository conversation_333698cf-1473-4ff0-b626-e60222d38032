import {
  Button,
  IconArrowRightOutline,
  IconKickCreditBulkColor,
  IconKickPointsBulkColor,
  IconPaymentOutline,
  Text,
} from "@kickavenue/ui/components"
import NavigationOption from "@kickavenue/ui/components/NavigationOption"
import useChangePage from "@app/hooks/useChangePage"
import PaymentCardTypeIcon from "@components/Payment/credit-debit/PaymentCardTypeIcon"
import { MiscConstant } from "@constants/misc"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { capitalizeWords } from "@utils/misc"
import { usePaymentCardFormStore } from "stores/paymentCardFormStore"
import { usePaymentStore } from "stores/paymentStore"

const { PAYMENT } = PageRouteConstant
const { CHECKOUT_URL_SEARCH_PARAM } = MiscConstant

export default function CheckoutPayment({
  checkoutUrl,
}: {
  checkoutUrl: string
}) {
  const { selectedPayment } = usePaymentStore()
  const { goToPage, isLoading } = useChangePage()
  const { binData } = usePaymentCardFormStore()

  const { isKickPoints, isCreditBalance, paymentType } = selectedPayment || {}

  const renderContent = () => {
    switch (
      paymentType === null &&
      isKickPoints === false &&
      isCreditBalance === false
    ) {
      case true:
        return (
          <NavigationOption
            title="Select Payment"
            weight="medium"
            withArrowRight
            iconLeading={<IconPaymentOutline />}
            className="cursor-pointer rounded-base border border-gray-w-80"
            disableHover
            onClick={() =>
              goToPage(PAYMENT, { [CHECKOUT_URL_SEARCH_PARAM]: checkoutUrl })
            }
            disabled={isLoading[PAYMENT]}
          />
        )
      default:
        return (
          <div className="flex flex-col items-center justify-center rounded-base border border-gray-w-80 p-sm">
            <Button
              variant="link"
              onClick={() =>
                goToPage(PAYMENT, { [CHECKOUT_URL_SEARCH_PARAM]: checkoutUrl })
              }
              disabled={isLoading[PAYMENT]}
              size="lg"
              className="!h-full !w-full !p-1"
            >
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center gap-xs">
                  {isKickPoints && (
                    <div className="flex items-center gap-xxs">
                      <IconKickPointsBulkColor className="size-lg" />
                      <Text size="sm" type="bold" state="primary">
                        Kick Points
                      </Text>
                    </div>
                  )}
                  {isKickPoints && (isCreditBalance || paymentType) && (
                    <Text size="base" type="medium" state="secondary">
                      +
                    </Text>
                  )}
                  {isCreditBalance && (
                    <div className="flex items-center gap-xxs">
                      <IconKickCreditBulkColor className="size-lg" />
                      <Text size="sm" type="bold" state="primary">
                        Credit Balance
                      </Text>
                    </div>
                  )}
                  {isCreditBalance && paymentType && (
                    <Text size="base" type="medium" state="secondary">
                      +
                    </Text>
                  )}
                  {paymentType && binData && (
                    <div className="flex items-center gap-xs">
                      <PaymentCardTypeIcon binBrand={binData?.brand} />

                      <Text size="sm" type="bold" state="primary">
                        {capitalizeWords(binData?.binType)} Card
                      </Text>
                    </div>
                  )}
                  {paymentType && !binData && (
                    <Text size="sm" type="bold" state="primary">
                      {capitalizeWords(paymentType?.name)}
                    </Text>
                  )}
                </div>
                <div className="text-gray-b-75">
                  <IconArrowRightOutline className="size-lg" />
                </div>
              </div>
            </Button>
          </div>
        )
    }
  }

  return renderContent()
}
