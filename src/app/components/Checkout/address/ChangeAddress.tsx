import {
  But<PERSON>,
  IconCloseOutline,
  <PERSON><PERSON> as <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@kickavenue/ui/components"
import { useState } from "react"
import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { TAddress } from "types/address.type"
import CardAddresses from "@components/Profile/saved-addresses/CardAddresses"
import { useAddressStore } from "stores/addressStore"
import { ModalConstant } from "@constants/modal"

export interface ChangeAddressProps {
  defaultAddress?: TAddress
}

const { CHANGE_ADDRESS } = ModalConstant.MODAL_IDS

export const ChangeAddress = (props: ChangeAddressProps) => {
  const { defaultAddress } = props
  const { setOpen } = useModalStore()
  const { setSelectedAddress } = useAddressStore()
  const [selectedItemAddress, setSelectedItemAddress] = useState<TAddress>()

  return (
    <Modal modalId={CHANGE_ADDRESS}>
      <ModalContainer
        open
        title="Change Address"
        trailing={
          <IconCloseOutline
            className="cursor-pointer"
            onClick={() => setOpen(false)}
          />
        }
      >
        <div className="flex flex-col gap-sm text-start">
          <CardAddresses
            isClickable
            defaultAddress={defaultAddress}
            onItemClick={(item) => {
              setSelectedItemAddress(item)
            }}
          />
        </div>
        <Space size="sm" direction="y" type="margin" />
        <Button
          size="lg"
          variant="primary"
          style={{ width: "100%" }}
          onClick={() => {
            setOpen(false)
            setSelectedAddress(selectedItemAddress)
          }}
        >
          Save Changes
        </Button>
      </ModalContainer>
    </Modal>
  )
}
