import NavigationOption from "@kickavenue/ui/components/NavigationOption"
import { useMemo } from "react"
import { ShippingMethodType } from "types/shippingMethod.type"
import { TListingItem } from "types/listingItem.type"

import { shippingMethodMap } from "./checkoutform.utils"

export default function ShippingMethod({
  listing,
}: {
  listing?: TListingItem | null
}) {
  const { PreOrder, Express, Standard } = ShippingMethodType

  const optionOnListing = useMemo(() => {
    if (listing?.isConsignment || listing?.isConsigment) {
      return shippingMethodMap[Express]
    }
    if (listing?.isPreOrder) return shippingMethodMap[PreOrder]
    return shippingMethodMap[Standard]
  }, [
    PreOrder,
    Standard,
    Express,
    listing?.isConsignment,
    listing?.isConsigment,
    listing?.isPreOrder,
  ])

  if (!listing) return null

  return (
    <div className="flex flex-col gap-xs">
      <div className="border-primary cursor-pointer rounded-base border border-gray-w-80">
        <NavigationOption
          title={optionOnListing.title}
          subTitle={optionOnListing.subTitle}
          subTitleProps={{ className: "text-gray" }}
          iconLeading={optionOnListing.icon}
          disableHover
        />
      </div>
    </div>
  )
}
