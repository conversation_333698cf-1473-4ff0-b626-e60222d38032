"use client"

import React, { useEffect } from "react"
import { <PERSON><PERSON>, Divider, Space, Text } from "@kickavenue/ui/components"
import { snakeCase } from "lodash"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import CheckoutVoucherField from "@components/Checkout/voucher/CheckoutVoucherField"
import CheckoutPayment from "@components/Checkout/payment/CheckoutPayment"
import PaymentSummary from "@components/Payment/payment-summary/PaymentSummary"
import { useCheckoutForm } from "@components/Checkout/hooks/useCheckoutForm"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { getListingCondition } from "@utils/selling"
import { useCheckoutStore } from "stores/checkoutStore"
import { usePaymentStore } from "stores/paymentStore"
import { usePaymentSummary } from "@components/Payment/hooks/usePaymentSummary"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"

import CardAddress from "../address/CardAddress"
import AddonModal from "../addon/AddonModal"
import AddonSelectedList from "../addon/AddonSelectedList"
import { useCheckoutAddress } from "../hooks/useCheckoutAddress"

import ShippingMethod from "./ShippingMethod"
import CheckoutForm3DSAuth from "./CheckoutForm3DSAuth"

const { HOME, CHECKOUT } = PageRouteConstant

// eslint-disable-next-line max-lines-per-function
export default function CheckoutForm() {
  const { goToPage } = useChangePage()
  const { listing, voucher } = useCheckoutStore()
  const { selectedPayment, isEmptySelectedPayment, resetPaymentState } =
    usePaymentStore()
  const { totalPaymentText, itemPrices } = usePaymentSummary()
  const { member } = useMemberStore()
  const { selectedAddress } = useCheckoutAddress()

  const {
    handleProceed,
    handle3DSAuthModalClose,
    isProceedDisabled,
    redirectUrl,
  } = useCheckoutForm({
    itemPrices,
    listing,
  })

  useEffect(() => {
    const listingId = listing?.id ?? 0
    if (!listingId) {
      goToPage(HOME)
    }
  }, [listing?.id, goToPage])

  useEffect(() => {
    if (listing?.id !== selectedPayment?.listingId) {
      resetPaymentState()
    }
  }, [listing?.id, selectedPayment?.listingId, resetPaymentState])

  useEffect(() => {
    if (isEmptySelectedPayment()) {
      event({
        action: "product_checkout_started",
        params: {
          [snakeCase("member_id")]: String(member.id),
          email: member.email,
          brand: listing?.item.brands?.map((brand) => brand.name).join(" | "),
          [snakeCase("display_name")]: listing?.item.name || "",
          size: `${listing?.size.us} us`,
          [snakeCase("product_category")]: listing?.item.category?.name || "",
          sku: listing?.item.skuCode,
          [snakeCase("listing_id")]: String(listing?.id || ""),
          quantity: String(listing?.quantity || ""),
          voucher: voucher?.code,
          address: selectedAddress?.title,
        },
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div className="bg-gray-w-95 p-xxl pt-lg md:flex md:justify-center">
      <div className="rounded-base bg-white md:w-[580px] md:p-lg">
        <div className="flex flex-col gap-base p-sm">
          <ProductDetailPreview
            productData={listing?.item}
            size={listing?.size}
            listing={listing}
            listingCondition={getListingCondition(listing)}
          />
          <Divider orientation="horizontal" state="default" />
          <Text size="sm" type="bold" state="primary">
            Shipping Method
          </Text>
          <ShippingMethod listing={listing} />
          <Divider orientation="horizontal" state="default" />
          <Text size="sm" type="bold" state="primary">
            Shipping Address
          </Text>
          <CardAddress />
          <Divider orientation="horizontal" state="default" />
          <Text size="sm" type="bold" state="primary">
            Add On
          </Text>
          <AddonSelectedList />
          <AddonModal />
          <Divider orientation="horizontal" state="default" />
          <Text size="sm" type="bold" state="primary">
            Voucher
          </Text>
          <CheckoutVoucherField />
          <Divider orientation="horizontal" state="default" />
          <Text size="sm" type="bold" state="primary">
            Payment Method
          </Text>
          <CheckoutPayment checkoutUrl={CHECKOUT} />
          <Divider orientation="horizontal" state="default" />
          <Text size="sm" type="bold" state="primary">
            Payment Summary
          </Text>
          <PaymentSummary
            totalPaymentText={totalPaymentText}
            itemPrices={itemPrices}
          />
        </div>
        <Space size="base" direction="y" type="margin" />
        <Divider orientation="horizontal" state="default" />
        <Space size="base" direction="y" type="margin" />
        <div className="flex justify-center">
          <Text size="sm" type="regular" state="secondary">
            By clicking proceed, you agree to our
          </Text>
          <Button size="md" variant="link" style={{ padding: "0" }}>
            &nbsp;Terms & Conditions
          </Button>
        </div>
        <Space size="base" direction="y" type="margin" />
        <div className="flex w-full justify-center">
          <Button
            size="lg"
            variant="primary"
            style={{ width: "100%" }}
            onClick={handleProceed}
            disabled={isProceedDisabled}
          >
            Proceed
          </Button>
        </div>
      </div>
      <CheckoutForm3DSAuth
        redirectUrl={redirectUrl}
        onModalClose={handle3DSAuthModalClose}
      />
    </div>
  )
}
