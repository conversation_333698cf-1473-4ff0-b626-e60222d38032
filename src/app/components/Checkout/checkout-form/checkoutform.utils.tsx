import {
  Badge,
  IconConsignmentOutline,
  IconExpressBold,
  IconPreOrderBold,
  IconStandardBold,
} from "@kickavenue/ui/components"
import { ShippingMethodType } from "types/shippingMethod.type"

export interface ShippingMethodOption {
  id: number
  type: ShippingMethodType
  title: string
  subTitle: string
  icon?: React.ReactNode
}

export const shippingMethodOptions: ShippingMethodOption[] = [
  {
    id: 1,
    type: ShippingMethodType.Express,
    title: "Express",
    subTitle: "Ships TODAY within Business Hours",
    icon: <Badge iconLeft={IconExpressBold} />,
  },
  {
    id: 2,
    title: "Standard",
    type: ShippingMethodType.Standard,
    subTitle: "ETA  within 3 - 10 Business Days",
    icon: <Badge type="neutral" iconLeft={IconStandardBold} />,
  },
  {
    id: 3,
    type: ShippingMethodType.PreOrder,
    title: "Pre-Order",
    subTitle: "Ships TODAY within Business Hours",
    icon: (
      <Badge
        type="information"
        iconLeft={IconPreOrderBold}
        className="bg-accent [&>svg]:text-accent"
      />
    ),
  },
  {
    id: 4,
    type: ShippingMethodType.ConsignToWarehouse,
    title: "Consign to Warehouse",
    subTitle: "Make extra income reselling with us (Free Shipping)",
    icon: <Badge type="neutral" iconLeft={IconConsignmentOutline} />,
  },
]

export const shippingMethodMap: Record<
  ShippingMethodType,
  ShippingMethodOption
> = {
  [ShippingMethodType.Express]: {
    id: 1,
    type: ShippingMethodType.Express,
    title: "Express",
    subTitle: "Ships TODAY within Business Hours",
    icon: <Badge iconLeft={IconExpressBold} />,
  },
  [ShippingMethodType.Standard]: {
    id: 2,
    title: "Standard",
    type: ShippingMethodType.Standard,
    subTitle: "ETA  within 3 - 10 Business Days",
    icon: <Badge type="neutral" iconLeft={IconStandardBold} />,
  },
  [ShippingMethodType.PreOrder]: {
    id: 3,
    type: ShippingMethodType.PreOrder,
    title: "Pre-Order",
    subTitle: "Ships TODAY within Business Hours",
    icon: (
      <Badge
        type="information"
        iconLeft={IconPreOrderBold}
        className="bg-accent [&>svg]:text-accent"
      />
    ),
  },
  [ShippingMethodType.ConsignToWarehouse]: {
    id: 4,
    type: ShippingMethodType.ConsignToWarehouse,
    title: "Consign to Warehouse",
    subTitle: "Make extra income reselling with us (Free Shipping)",
    icon: <Badge type="neutral" iconLeft={IconConsignmentOutline} />,
  },
}

export const dummySummary = [
  {
    title: "Payment Method",
    value: 4000000,
    subitems: [
      {
        title: "Additional Fee",
        value: 700000,
      },
    ],
  },
  {
    title: "Shipping Fee",
    value: 80000,
  },
]
