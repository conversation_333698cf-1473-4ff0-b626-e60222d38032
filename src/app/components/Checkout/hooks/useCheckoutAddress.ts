import { useQuery } from "@tanstack/react-query"
import { useEffect, useMemo, useState } from "react"
import { AddressApiRepository } from "@infrastructure/repositories/addressApiRepository"
import { TAddress } from "types/address.type"
import { useAddressStore } from "stores/addressStore"
import { CountryApiRepository } from "@infrastructure/repositories/countryApiRepository"
import { CityApiRepository } from "@infrastructure/repositories/cityApiRepository"
import { ProvinceApiRepository } from "@infrastructure/repositories/provinceApiRepository"
import { GetCheckoutAddress } from "@application/usecases/getCheckoutAddress"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { useLocationStore } from "stores/locationStore"
import { getLocation } from "@utils/location.utils"

export const useCheckoutAddress = () => {
  const addressRepository = new AddressApiRepository()
  const countryRepository = new CountryApiRepository()
  const cityRepository = new CityApiRepository()
  const provinceRepository = new ProvinceApiRepository()

  const { selectedAddress, setSelectedAddress } = useAddressStore()
  const [addresses, setAddresses] = useState<TAddress[]>([])
  const {
    setCities,
    setProvinces,
    setCountries,
    cities,
    provinces,
    countries,
  } = useLocationStore()

  useQuery({
    queryKey: [QueryKeysConstant.GET_ALL_ADDRESS],
    queryFn: async () => {
      try {
        
        const usecase = new GetCheckoutAddress(
          addressRepository,
          cityRepository,
          provinceRepository,
          countryRepository,
        )

        const { addresses, cities, provinces, countries } = await usecase.getAddress()

        // Normalize addresses in case a paginated object slips through
        const normalizedAddresses = Array.isArray(addresses)
          ? addresses
          : (addresses as unknown as { content?: TAddress[] })?.content || []

        
        setAddresses(normalizedAddresses)
        setCities(cities || [])
        setProvinces(provinces || [])
        setCountries(countries || [])


        // update selected address if it exists
        if (selectedAddress?.id) {
          const address = normalizedAddresses.find(
            (address) => address.id === selectedAddress?.id,
          )
          setSelectedAddress(address)
        }

        return { addresses, cities, provinces, countries }
      } catch (error) {
        console.error("❌ [useCheckoutAddress] Failed to fetch checkout address data:", error)
        return { addresses: [], cities: [], provinces: [], countries: [] }
      }
    },
  })

  // Log the current state of the stores
  useEffect(() => {
  }, [addresses, countries, cities, provinces])

  useEffect(() => {
    if (selectedAddress?.id) return

    if (addresses.length > 0) {
      const primary = addresses.find((item) => item.isPrimary)
      setSelectedAddress(primary || addresses[0])
    }
  }, [addresses, selectedAddress, setSelectedAddress])

  const location = useMemo(() => {
    return getLocation({
      address: selectedAddress,
      cities,
      provinces,
      countries,
    })
  }, [cities, provinces, countries, selectedAddress])

  return {
    addresses,
    provinces,
    cities,
    countries,
    selectedAddress,
    location,
  }
}
