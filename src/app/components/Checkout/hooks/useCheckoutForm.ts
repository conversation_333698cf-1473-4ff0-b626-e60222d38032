import { useMemo, useState } from "react"
import { useAddressStore } from "stores/addressStore"
import { usePaymentStore } from "stores/paymentStore"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"
import { TOffer } from "types/offer.type"
import { TListingItem } from "types/listingItem.type"

import {
  getPaymentStatusPageLoadingKey,
  PaymentSummaryItem,
} from "../utils/checkout.utils"

import useAuthenticate3Ds from "./useAuthenticate3Ds"
import useSubmitCreateTx from "./useSubmitCreateTx"
import usePrepareCheckoutPayload from "./usePrepareCheckoutPayload"

const { CHECKOUT_3DS_AUTH } = ModalConstant.MODAL_IDS
const { PAYMENT_STATUS } = PageRouteConstant

export const useCheckoutForm = ({
  offer,
  listing,
  itemPrices,
}: {
  offer?: TOffer | null
  listing?: TListingItem | null
  itemPrices: PaymentSummaryItem[]
}) => {
  const { selectedAddress, addresses } = useAddressStore()
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null)
  const { setOpen } = useModalStore()
  const { goToPage, isLoading } = useChangePage()
  const [txId, setTxId] = useState<number | null | undefined>(null)

  const { setShowToast } = useToast()

  const { paymentType, isCreditBalance, isKickPoints } = usePaymentStore()

  const address =
    selectedAddress ??
    (Array.isArray(addresses)
      ? addresses.find((item) => item.isPrimary)
      : undefined)

  const { submitAuthenticate3Ds, isPending: isPending3Ds } = useAuthenticate3Ds(
    {
      onPerformAuth: (url) => {
        setRedirectUrl(url)
      },
      onFailure: (_: any, txId: number) => {
        setOpen(false, CHECKOUT_3DS_AUTH)
        goToPage(`${PAYMENT_STATUS}/${txId}`)
      },
      onSuccess: (txId: number) => {
        setOpen(false, CHECKOUT_3DS_AUTH)
        goToPage(`${PAYMENT_STATUS}/${txId}`)
      },
      onPending: (txId: number) => {
        setOpen(false, CHECKOUT_3DS_AUTH)
        goToPage(`${PAYMENT_STATUS}/${txId}`)
      },
    },
  )

  const { mutate, isPending } = useSubmitCreateTx({
    onSuccess: (data) => {
      const rurl = data?.metadata?.redirectUrl
      setTxId(data?.id)

      if (!rurl) {
        goToPage(`${PAYMENT_STATUS}/${data?.id}`)
        return
      }

      submitAuthenticate3Ds(rurl, data?.id)
      setOpen(true, CHECKOUT_3DS_AUTH)
    },
    onError: (error) => {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })

  const payload = usePrepareCheckoutPayload({ offer, listing, itemPrices })

  const handleProceed = () => {
    if (!address) {
      return
    }
    mutate(payload)
  }

  const handle3DSAuthModalClose = () => {
    goToPage(`${PAYMENT_STATUS}/${txId}`)
  }

  const isProceedDisabled = useMemo(() => {
    if (
      selectedAddress &&
      (paymentType || isCreditBalance || isKickPoints) &&
      !isPending &&
      !isPending3Ds &&
      !isLoading[getPaymentStatusPageLoadingKey(isLoading) as string]
    ) {
      return false
    }
    return true
  }, [
    selectedAddress,
    paymentType,
    isCreditBalance,
    isKickPoints,
    isPending,
    isPending3Ds,
    isLoading,
  ])

  return {
    handleProceed,
    handle3DSAuthModalClose,
    isProceedDisabled,
    redirectUrl,
  }
}
