import { useMemo } from "react"
import { TListingItem } from "types/listingItem.type"
import useFetchFeeById from "@app/hooks/useFetchFeeById"
import { getFeeAmount } from "@utils/fee.utils"
import { formatPrice, getStripAmount } from "@utils/misc"
import useFetchFeeRates from "@app/hooks/useFetchFeeRates"
import { TOffer } from "types/offer.type"

import {
  calculateTotalPayment,
  getFeeRateItems,
  PaymentSummaryItem,
} from "../utils/checkout.utils"

import { useCheckoutAddress } from "./useCheckoutAddress"

const useCheckoutOfferSummary = ({
  listing,
  offer,
}: {
  listing?: TListingItem | null
  offer?: TOffer | null
}) => {
  const { selectedAddress } = useCheckoutAddress()
  const { data: feeData } = useFetchFeeById({
    id: 1,
  })

  const { rate } = useFetchFeeRates({
    body: {
      items: getFeeRateItems({
        listings: listing ? [listing] : [],
      }),
      postalCode: selectedAddress?.zipCode
        ? Number(selectedAddress?.zipCode)
        : 0,
    },
    enabled: Boolean(listing?.id) && Boolean(selectedAddress?.zipCode),
  })

  const itemPrices: PaymentSummaryItem[] = useMemo(() => {
    const offerPrice = getStripAmount(offer?.amount)
    const processingFee = getFeeAmount(feeData, offerPrice)
    const shippingFee = rate || 0

    return [
      {
        title: "Offer Price",
        type: "addition",
        value: offerPrice,
      },
      {
        title: "Processing Fee",
        type: "addition",
        value: processingFee,
        tooltip:
          "Ensuring your order arrives safely, covering bubble wrap for weather protection, double-boxing for shipment security, and insurance for added peace of mind.",
      },
      {
        title: "Shipping Fee",
        type: "addition",
        value: shippingFee,
      },
    ] as unknown as PaymentSummaryItem[]
  }, [offer?.amount, feeData, rate])

  const totalPayment = calculateTotalPayment(itemPrices)
  const totalPaymentText = formatPrice(totalPayment, null, "IDR", "IDR 0")

  return {
    itemPrices,
    totalPayment,
    totalPaymentText,
  }
}

export default useCheckoutOfferSummary
