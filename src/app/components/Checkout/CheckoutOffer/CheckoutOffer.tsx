"use client"

import { useParams } from "next/navigation"
import { Divider, Text } from "@kickavenue/ui/components/index"
import { Button } from "@kickavenue/ui/components/Button"
import useGetOfferById from "@app/hooks/useGetOfferById"
import Spinner from "@components/shared/Spinner"
import useFetchListingItemById from "@app/hooks/useFetchListingItemById"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import { getListingCondition } from "@utils/selling"
import PaymentSummary from "@components/Payment/payment-summary/PaymentSummary"
import { PageRouteConstant } from "@constants/pageRoute.constant"

import ShippingMethod from "../checkout-form/ShippingMethod"
import CardAddress from "../address/CardAddress"
import CheckoutPayment from "../payment/CheckoutPayment"
import useCheckoutOfferSummary from "../hooks/useCheckoutOfferSummary"
import { useCheckoutForm } from "../hooks/useCheckoutForm"
import CheckoutForm3DSAuth from "../checkout-form/CheckoutForm3DSAuth"
import { generateListing } from "../utils/checkout.utils"

const { CHECKOUT_OFFER } = PageRouteConstant

const CheckoutOffer = () => {
  const params = useParams<{ id: string }>()
  const offerId = params?.id

  const { data: offer, isLoading: isOfferLoading } = useGetOfferById({
    id: Number(offerId),
  })

  const { data: listing, isLoading: isListingLoading } =
    useFetchListingItemById(offer?.sellerListingId)

  const { itemPrices, totalPaymentText } = useCheckoutOfferSummary({
    listing,
    offer,
  })

  const { handleProceed, isProceedDisabled, redirectUrl } = useCheckoutForm({
    offer,
    listing: generateListing({ id: offer?.sellerListingId }),
    itemPrices,
  })

  const isLoading = isOfferLoading || isListingLoading

  return (
    <>
      <h4 className="w-full p-base text-center text-heading-4 font-bold">
        Make Offer
      </h4>
      {isLoading && (
        <div className="flex min-h-[300px] items-center justify-center bg-gray-w-95 md:min-h-[500px]">
          <Spinner />
        </div>
      )}
      {!isLoading && offer && (
        <div className="bg-gray-w-95 p-xxl pt-lg md:flex md:justify-center">
          <div className="rounded-base bg-white md:w-[564px]">
            <div className="flex flex-col gap-lg md:p-lg">
              <ProductDetailPreview
                productData={listing?.item}
                size={listing?.size}
                listing={listing}
                listingCondition={getListingCondition(listing)}
              />
              <Divider orientation="horizontal" state="default" />
              <div className="">
                <Text size="sm" type="bold" state="primary" className="mb-sm">
                  Shipping Method
                </Text>
                <ShippingMethod listing={listing} />
              </div>
              <Divider orientation="horizontal" state="default" />
              <div className="">
                <Text size="sm" type="bold" state="primary" className="mb-sm">
                  Shipping Address
                </Text>
                <CardAddress />
              </div>
              <Divider orientation="horizontal" state="default" />
              <div className="">
                <Text size="sm" type="bold" state="primary" className="mb-sm">
                  Payment Method
                </Text>
                <CheckoutPayment checkoutUrl={`${CHECKOUT_OFFER}/${offerId}`} />
              </div>
              <Divider orientation="horizontal" state="default" />
              <div className="">
                <Text size="sm" type="bold" state="primary" className="mb-sm">
                  Payment Summary
                </Text>
                <PaymentSummary
                  totalPaymentText={totalPaymentText}
                  itemPrices={itemPrices}
                />
              </div>
            </div>
            <Divider orientation="horizontal" state="default" />
            <div className="flex flex-col gap-sm p-lg">
              <div className="flex justify-center">
                <Text size="sm" type="regular" state="secondary">
                  By clicking proceed, you agree to our
                </Text>
                <Button size="md" variant="link" style={{ padding: "0" }}>
                  &nbsp;Terms & Conditions
                </Button>
              </div>
              <div className="flex w-full justify-center">
                <Button
                  size="lg"
                  variant="primary"
                  style={{ width: "100%" }}
                  onClick={handleProceed}
                  disabled={isProceedDisabled}
                >
                  Proceed
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
      <CheckoutForm3DSAuth redirectUrl={redirectUrl} />
    </>
  )
}

export default CheckoutOffer
