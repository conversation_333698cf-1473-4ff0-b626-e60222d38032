import React, { useState } from "react"
import MyVoucherCard from "@components/MyVoucher/voucher-card/MyVoucherCard"
import SearchVoucher from "@components/MyVoucher/list/SearchVoucher"
import { useCheckoutStore } from "stores/checkoutStore"
import { TVoucher } from "types/voucher.type"
import { formatStripePrice } from "@utils/misc"

export interface CheckoutListVoucherProps {
  onItemClick: (item: TVoucher) => void
  isSuccess: boolean
}

export default function CheckoutListVoucher(props: CheckoutListVoucherProps) {
  const { onItemClick, isSuccess } = props
  const { listVoucher } = useCheckoutStore()

  const [selectedVoucher, setSelectedVoucher] = useState<TVoucher | undefined>()

  const handleSelectVoucher = (item: TVoucher) => {
    setSelectedVoucher(item)
    onItemClick(item)
  }

  return (
    <div className="flex flex-col gap-y-md">
      <SearchVoucher onChange={() => {}} />
      <div className="grid grid-cols-1 gap-md overflow-y-scroll md:grid-cols-1">
        {isSuccess &&
          listVoucher?.map((item) => {
            const type = item.id === selectedVoucher?.id ? "pressed" : "active"
            const { id, title, code, minimumPurchase, endDate } = item
            return (
              <div className="h-fit" key={id}>
                <MyVoucherCard
                  key={id}
                  title={title}
                  validUntil={endDate}
                  minPurchaseAmount={formatStripePrice(minimumPurchase)}
                  code={code}
                  type={type}
                  item={item}
                  onClick={() => handleSelectVoucher(item)}
                  onHint={() => {}}
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}
