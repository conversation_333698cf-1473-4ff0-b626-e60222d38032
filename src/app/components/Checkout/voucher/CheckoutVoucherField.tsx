import { IconSuccessCircleBulk, IconTicket } from "@kickavenue/ui/components"
import NavigationOption from "@kickavenue/ui/components/NavigationOption"
import React from "react"
import { useCheckoutStore } from "stores/checkoutStore"
import { useModalStore } from "stores/modalStore"
import { formatPrice, getStripAmount } from "@utils/misc"
import { ModalConstant } from "@constants/modal"
import VoucherModal from "@components/MyVoucher/modal/VoucherModal"

import {
  calculateAddOnTotalPrice,
  getVoucherAmount,
} from "../utils/checkout.utils"

import CheckoutVoucherModal from "./CheckoutVoucherModal"

const { CHECKOUT_VOUCHER } = ModalConstant.MODAL_IDS

export default function CheckoutVoucherField() {
  const { setOpen } = useModalStore()
  const { voucher, listing, shippingFee, addon, processingFee } =
    useCheckoutStore()

  const renderVoucher = () => {
    if (voucher == null) {
      return (
        <NavigationOption
          title="Use Voucher"
          weight="medium"
          withArrowRight
          iconLeading={<IconTicket />}
          className="cursor-pointer rounded-base border border-gray-w-80"
          onClick={() => setOpen(true, CHECKOUT_VOUCHER)}
          disableHover
        />
      )
    }
    const { title } = voucher!
    const voucherAmount = getVoucherAmount({
      voucher,
      sellingPrice: getStripAmount(listing?.sellingPrice),
      shippingFee: shippingFee || 0,
      processingFee: processingFee || 0,
      totalAddOnPrice: calculateAddOnTotalPrice(addon),
    })
    const amountText = formatPrice(voucherAmount, null, "IDR")
    return (
      <div className="cursor-pointer rounded-base bg-success text-gray-w-95">
        <NavigationOption
          title={title}
          subTitle={`You can save ${amountText}`}
          weight="bold"
          withArrowRight
          iconLeading={<IconSuccessCircleBulk />}
          disableHover
          onClick={() => setOpen(true, CHECKOUT_VOUCHER)}
        />
      </div>
    )
  }

  return (
    <>
      {renderVoucher()}
      <VoucherModal />
      <CheckoutVoucherModal />
    </>
  )
}
