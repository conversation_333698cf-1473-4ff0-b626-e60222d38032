import { useInView } from "react-intersection-observer"
import { useEffect, useState } from "react"
import { Text } from "@kickavenue/ui/dist/src/components"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import useFetchInfiniteMyVoucher from "@app/hooks/useFetchMyVoucher"
import MyVoucherCard from "@components/MyVoucher/voucher-card/MyVoucherCard"
import { formatStripePrice, isEmptyArray } from "@utils/misc"
import { TVoucher } from "types/voucher.type"
import Spinner from "@components/shared/Spinner"
import SearchInput from "@components/shared/SearchInput"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { useCheckoutStore } from "stores/checkoutStore"

import { buildVoucherFilters } from "../utils/checkout.utils"

const { CHECKOUT_VOUCHER } = ModalConstant.MODAL_IDS

const CheckoutVoucherModalContent = ({
  selectedVoucher,
  onSelectVoucher,
}: {
  selectedVoucher: TVoucher | undefined
  onSelectVoucher: (item?: TVoucher) => void
}) => {
  const { listing, addon } = useCheckoutStore()
  const { open, modalId } = useModalStore()
  const [search, setSearch] = useState("")

  const isVoucherModalOpen = open && modalId === CHECKOUT_VOUCHER

  const filters = buildVoucherFilters({
    listing,
    addon,
  })

  const {
    data: vouchers,
    isLoading,
    isSuccess,
    hasNextPage,
    fetchNextPage,
  } = useFetchInfiniteMyVoucher(isVoucherModalOpen, { search, ...filters })

  const { ref, inView } = useInView()

  const handleSelectVoucher = (item: TVoucher) => {
    if (selectedVoucher?.id === item.id) {
      onSelectVoucher(undefined)
      return
    }
    onSelectVoucher(item)
  }

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  const vouchersExist = isSuccess && !isEmptyArray(vouchers)
  const noVouchersFound = isSuccess && isEmptyArray(vouchers)

  return (
    <div className="flex h-[331px] flex-col gap-base overflow-y-auto p-lg">
      <SearchInput
        debounce
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setSearch(e.target.value)
        }
        onClearText={() => {}}
        placeholder="Enter Promo or Voucher Code here"
      />
      {vouchersExist &&
        vouchers?.map((item) => {
          const type = item.id === selectedVoucher?.id ? "pressed" : "active"
          const { id, title, code, minimumPurchase, endDate } = item
          return (
            <div className="h-fit" key={id}>
              <MyVoucherCard
                key={id}
                title={title}
                validUntil={endDate}
                code={code}
                type={type}
                item={item}
                onHint={() => {}}
                onClick={() => handleSelectVoucher(item)}
                minPurchaseAmount={formatStripePrice(minimumPurchase)}
              />
            </div>
          )
        })}
      {noVouchersFound && (
        <div className="flex h-[331px] min-w-[350px] items-center justify-center">
          <Text size="sm" state="secondary" type="regular">
            No vouchers found
          </Text>
        </div>
      )}
      {isLoading && <SpinnerLoading className="h-[331px] min-w-[350px]" />}
      {hasNextPage && (
        <div ref={ref} className="flex justify-center">
          <Spinner />
        </div>
      )}
    </div>
  )
}

export default CheckoutVoucherModalContent
