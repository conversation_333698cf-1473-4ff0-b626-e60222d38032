import { IconTrashOutline, QuantityCard } from "@kickavenue/ui/components"
import React, { useCallback, useState } from "react"
import { TAddon } from "types/addonn.type"
import { formatCurrencyStripe, getProductImageUrl } from "@utils/misc"

export interface AddonQuantityCardProps {
  item: TAddon
  onChange: (val: TAddon) => void
  onDelete?: (id: number) => void
}

export default function AddonQuantityCard(props: AddonQuantityCardProps) {
  const { item, onChange, onDelete } = props
  const [isActive, setIsActive] = useState(false)

  const handleAddonChange = useCallback(
    (val: number) => {
      setIsActive(val > 0)
      onChange({ ...item, selectedQuantity: val })
    },
    [item, onChange],
  )

  const { id, sellingPrice } = item
  const priceText = formatCurrencyStripe({ price: sellingPrice })

  return (
    <QuantityCard
      key={id}
      title={item?.item?.name}
      isActive={isActive}
      itemPrice={priceText}
      value={item?.selectedQuantity}
      minValue={0}
      maxValue={item?.quantity}
      imageProps={{
        src: getProductImageUrl(item.item),
        alt: "Product Image",
        width: 50,
        height: 50,
      }}
      onChange={handleAddonChange}
      trailingIcon={
        onDelete && (
          <IconTrashOutline
            className="cursor-pointer"
            onClick={() => onDelete(id)}
          />
        )
      }
    />
  )
}
