import React, { useCallback, useState } from "react"
import { <PERSON><PERSON> } from "@kickavenue/ui/components"
import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { TAddon } from "types/addonn.type"
import { useCheckoutStore } from "stores/checkoutStore"
import { ModalConstant } from "@constants/modal"
import HeaderModal from "@components/shared/HeaderModal"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"

import AddonModalContent from "./AddonModalContent"

const { SELECT_ADDON } = ModalConstant.MODAL_IDS

const AddonModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setAddon } = useCheckoutStore()
  const [selectedItems, setSelectedItems] = useState<TAddon[]>([])

  const isAddOnModalOpen = open && modalId === SELECT_ADDON

  const handleAddonChange = useCallback(
    (val: TAddon) => {
      const quantity = val.selectedQuantity ?? 0

      const exist =
        selectedItems.find((item) => item.id === val.id) !== undefined

      if (!exist && quantity > 0) {
        setSelectedItems([...selectedItems, val])
        return
      }

      if (exist && quantity > 0) {
        setSelectedItems([
          ...selectedItems.filter((item) => item.id !== val.id),
          val,
        ])
        return
      }

      if (exist && quantity === 0) {
        setSelectedItems(selectedItems.filter((item) => item.id !== val.id))
      }
    },
    [selectedItems],
  )

  const handleAddonProduct = useCallback(() => {
    setOpen(false)
    setAddon(selectedItems)
    setSelectedItems([])
  }, [selectedItems, setAddon, setOpen])

  if (!isAddOnModalOpen) return null

  return (
    <Modal modalId={SELECT_ADDON}>
      <HeaderModal onClose={() => setOpen(false)} title="Add On Products" />
      <AddonModalContent onChange={handleAddonChange} />
      <ModalFooter>
        <div className="col-span-12">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            disabled={!selectedItems?.length}
            onClick={handleAddonProduct}
          >
            Add Product
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}

export default AddonModal
