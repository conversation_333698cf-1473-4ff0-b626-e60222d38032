import NavigationOption from "@kickavenue/ui/components/NavigationOption"
import React, { useCallback, useEffect, useState } from "react"
import { useCheckoutStore } from "stores/checkoutStore"
import { useModalStore } from "stores/modalStore"
import { TAddon } from "types/addonn.type"
import { ModalConstant } from "@constants/modal"

import AddonQuantityCard from "./AddonQuantityCard"

const { SELECT_ADDON } = ModalConstant.MODAL_IDS

export default function AddonSelectedList() {
  const { setOpen } = useModalStore()
  const { addon, updateAddonQuantity, setAddon, removeAddon } =
    useCheckoutStore()
  const [items, setItems] = useState<TAddon[]>([])

  useEffect(() => {
    setItems([])
    setTimeout(() => {
      setItems(addon)
    }, 100)
  }, [addon])

  const handleAddonChange = useCallback(
    (val: TAddon) => {
      updateAddonQuantity(val)
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [addon],
  )

  const handleOpenModal = () => {
    setAddon([...addon])
    setOpen(true, SELECT_ADDON)
  }

  if (items.length === 0) {
    return (
      <NavigationOption
        title="Select Product"
        weight="medium"
        withArrowRight
        className="cursor-pointer rounded-base border border-gray-w-80"
        onClick={handleOpenModal}
        disableHover
      />
    )
  }

  return (
    <>
      <NavigationOption
        title={`Selected Product (${items.length})`}
        weight="bold"
        withArrowRight
        className="cursor-pointer rounded-base border border-gray-w-80"
        onClick={handleOpenModal}
        disableHover
      />
      {items.map((item) => (
        <AddonQuantityCard
          key={item.id}
          item={item}
          onChange={handleAddonChange}
          onDelete={(id) => removeAddon(id)}
        />
      ))}
    </>
  )
}
