import { useInView } from "react-intersection-observer"
import { useEffect } from "react"
import useFetchInfiniteAddon from "@app/hooks/useFetchInfiniteAddon"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { TAddon } from "types/addonn.type"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import Spinner from "@components/shared/Spinner"

import AddonQuantityCard from "./AddonQuantityCard"

const { SELECT_ADDON } = ModalConstant.MODAL_IDS

const AddonModalContent = ({
  onChange,
}: {
  onChange: (val: TAddon) => void
}) => {
  const { open, modalId } = useModalStore()

  const isAddOnModalOpen = open && modalId === SELECT_ADDON
  const {
    data: addonProducts,
    isLoading,
    hasNextPage,
    fetchNextPage,
  } = useFetchInfiniteAddon(isAddOnModalOpen)

  const { ref, inView } = useInView()

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  if (isLoading) {
    return <SpinnerLoading className="max-h-[331px] min-w-[350px]" />
  }

  return (
    <div className="flex max-h-[331px] flex-col gap-base overflow-y-auto p-lg">
      {addonProducts?.map((addon) => (
        <AddonQuantityCard key={addon.id} item={addon} onChange={onChange} />
      ))}
      {hasNextPage && (
        <div ref={ref} className="flex justify-center">
          <Spinner />
        </div>
      )}
    </div>
  )
}

export default AddonModalContent
