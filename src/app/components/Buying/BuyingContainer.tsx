"use client"

import { useEffect } from "react"
import { Product } from "types/product.type"
import { useProductStore } from "@stores/productStore"
import { TItemCondition } from "types/listing.type"

import ProductConditionSelection from "./ProductConditionSelection"
import NineNinePercentsModal from "./NineNinePercentsModal"

export interface BuyingContainerProps {
  product: Product
  sizeId: number
  productType: TItemCondition
  restUrlSegments?: string[]
}

const BuyingContainer = ({
  product,
  sizeId,
  productType,
  restUrlSegments,
}: BuyingContainerProps) => {
  const { setProductDetail, setSelectedProductType, setRestUrlSegments } =
    useProductStore()

  useEffect(() => {
    const p = {
      ...product,
      itemListing: product.itemPriceSummary?.find(
        (item) => item?.size?.id === sizeId,
      ),
    }
    setProductDetail(p)
    setSelectedProductType(
      productType?.toUpperCase().replace(/-/g, "_") as TItemCondition,
    )
    setRestUrlSegments(restUrlSegments as string[])
  }, [
    product,
    sizeId,
    productType,
    restUrlSegments,
    setRestUrlSegments,
    setProductDetail,
    setSelectedProductType,
  ])

  return (
    <>
      <ProductConditionSelection />
      <NineNinePercentsModal />
    </>
  )
}

export default BuyingContainer
