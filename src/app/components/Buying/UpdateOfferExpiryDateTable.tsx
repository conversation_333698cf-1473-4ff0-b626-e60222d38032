import moment from "moment"
import { formatCurrency } from "@utils/separator"
import { TTableColumn } from "types/table.type"
import { useSellingCurrentStore } from "stores/sellingCurrentStore"
import { MiscConstant } from "@constants/misc"
import { TListingItem } from "types/listingItem.type"
import BulkActionTable from "@shared/BulkActionTable"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { formatPriceMinUnitVal } from "@utils/misc"

const width = {
  productDetail: 374,
  SKU: 108,
  size: 64,
  listingPrice: 148,
  currentExpiryDate: 154,
  newExpiryDate: 140,
}

const UpdateExpiryDateTable = () => {
  const { updateExpiryDate, sellingCurrentData, selectedRowKeys } =
    useSellingCurrentStore()

  const columns = [
    {
      key: "productDetails",
      width: width.productDetail,
      title: "Product Name",
      headerClassName: styles["sticky-header-1"],
      contentClassName: styles["sticky-content-1"],
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.item?.name?.localeCompare(b?.item?.name),
      defaultSortOrder: "ascend",
      render: (record: TListingItem) => <>{record?.item?.name}</>,
    },
    {
      key: "SKU",
      width: width.SKU,
      title: "SKU",
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.item?.skuCode.localeCompare(b?.item?.skuCode),
      render: (record: TListingItem) => <>{record?.item?.skuCode}</>,
    },
    {
      key: "size",
      width: width.size,
      title: "Size",
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.size?.us?.localeCompare(b?.size?.us),
      render: (record: TListingItem) => <>{`US ${record?.size?.us}`}</>,
    },
    {
      key: "listingPrice",
      width: width.listingPrice,
      title: "Listing Price",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TListingItem) =>
        formatCurrency(
          formatPriceMinUnitVal(record?.sellingPrice?.minUnitVal || 0) || 0,
          ",",
          "IDR",
        ),
      sorter: (a: TListingItem, b: TListingItem) =>
        formatPriceMinUnitVal(a?.sellingPrice?.minUnitVal || 0) -
        formatPriceMinUnitVal(b?.sellingPrice?.minUnitVal || 0),
    },
    {
      key: "expiryDate",
      dataIndex: "expiryDate",
      width: width.currentExpiryDate,
      title: "Current Expiry Date",
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.expiryDate?.localeCompare(b?.expiryDate),
    },
    {
      key: "newExpiryDate",
      width: width.newExpiryDate,
      title: "New Expiry Date",
      render: () => {
        if (!updateExpiryDate) {
          return "-"
        }
        return moment(updateExpiryDate).format(MiscConstant.DATE_FORMAT)
      },
    },
  ] as TTableColumn[]

  return (
    <BulkActionTable
      columns={columns}
      data={sellingCurrentData}
      selectedRowKeys={selectedRowKeys}
    />
  )
}

export default UpdateExpiryDateTable
