"use client"

import { useEffect } from "react"
import { compact } from "lodash"
import useProductConditionSelection from "@hooks/useProductConditionSelection"
import ProductSummary from "@components/Buying/ProductSummary"
import ProductImageSlider from "@components/ProductImageSlider"
import CenterWrapper from "@shared/CenterWrapper"
import FixedBottomButton from "@shared/FixedBottomButton"
import { useMiscStore } from "stores/miscStore"
import useFetchSellerListingByIds from "@app/hooks/useFetchSellerListingByIds"

import ProductConditionList from "./ProductConditionList"
import { mapListingsToCards } from "./buying.utils"

const ProductConditionSelection = () => {
  const {
    list,
    selectedListingCard,
    loadCheckoutPreview,
    handleClickListingCard,
    handleFixedButtonClick,
    checkSelectedListingCard,
  } = useProductConditionSelection()

  const { setShowFooter } = useMiscStore()

  const listingIds = list?.map((item) => item.sellerListingId)
  const { data: listings } = useFetchSellerListingByIds(
    compact(listingIds as number[]),
  )
  const newListCards = mapListingsToCards(listings, list)

  useEffect(() => {
    setShowFooter(false)
    return () => {
      setShowFooter(true)
    }
  }, [setShowFooter])

  return (
    <>
      <ProductSummary />
      <CenterWrapper className="!pb-[150px] !pt-xl">
        <div className="col-span-4 md:col-span-6">
          <ProductImageSlider />
        </div>
        <div className="col-span-4 md:col-span-6">
          <ProductConditionList
            list={newListCards}
            onClickListingCard={handleClickListingCard}
            checkSelectedListingCard={checkSelectedListingCard}
          />
        </div>
      </CenterWrapper>
      <FixedBottomButton
        onClick={handleFixedButtonClick}
        disabled={!selectedListingCard || loadCheckoutPreview}
        text="Continue to Buy / Offer"
      />
    </>
  )
}

export default ProductConditionSelection
