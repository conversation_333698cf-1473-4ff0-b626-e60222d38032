import { Space } from "@kickavenue/ui/components"
import ListingCard from "@components/shared/ListingCard"
import { TListingCard } from "types/listingCard.type"

export interface ProductConditionListProps {
  list: TListingCard[] | undefined
  onClickListingCard: (item: TListingCard) => void
  checkSelectedListingCard: (item: TListingCard) => boolean
}

const ProductConditionList = ({
  list,
  onClickListingCard,
  checkSelectedListingCard,
}: ProductConditionListProps) => {
  return (
    <>
      {list?.map((item) => (
        <div key={item.sellerListingId}>
          <ListingCard
            key={item.price}
            badgeType={item.badgeType}
            is99Percents={item.is99Percents}
            price={item.price}
            shippingTime={item.shippingTime}
            onClick={() => onClickListingCard(item)}
            selected={checkSelectedListingCard(item)}
            appearanceImg={item.appearanceImg}
          />
          <Space size="sm" direction="y" type="margin" />
        </div>
      ))}
    </>
  )
}

export default ProductConditionList
