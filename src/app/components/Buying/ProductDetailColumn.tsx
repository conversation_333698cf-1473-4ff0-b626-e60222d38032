import {
  IconExpressBold,
  IconPreOrderBold,
  IconStandardBold,
  Space,
  Text,
} from "@kickavenue/ui/components"
import ProductImage from "@kickavenue/ui/components/ProductImage"
import ListingBadge from "@components/shared/ListingBadge"
import {
  getBadgeTextByListingItem,
  getBadgeTypeByListingItem,
} from "@utils/listingItem"
import { TListingItem } from "types/listingItem.type"
import { TBadgeType } from "types/misc.type"
import { getProductImageUrl } from "@utils/misc"

interface ProductDetailColumnProps {
  listingItem: TListingItem
}

const ProductDetailColumn = ({ listingItem }: ProductDetailColumnProps) => {
  const getBadgeIconLeft = () => {
    if (listingItem.consignmentId) {
      return IconExpressBold
    }
    if (listingItem.isPreOrder) {
      return IconPreOrderBold
    }
    return IconStandardBold
  }

  const { item: product } = listingItem

  return (
    <div className="flex items-center gap-sm">
      <div className="">
        <ProductImage
          imageProps={{
            width: 40,
            height: 40,
            src: getProductImageUrl(product),
            alt: listingItem.item.name,
          }}
          containerProps={{ className: "pb-xs" }}
        />
      </div>
      <div className="">
        <Text size="sm" state="primary" type="regular">
          {listingItem.item.name}
        </Text>
        <Space size="xs" type="margin" direction="y" />
        <div className="flex items-center gap-xs">
          <ListingBadge
            isUsed={false}
            is99Percents={false}
            badgeType={getBadgeTypeByListingItem(listingItem) as TBadgeType}
            type={getBadgeTypeByListingItem(listingItem)}
            iconLeft={getBadgeIconLeft()}
            text={getBadgeTextByListingItem(listingItem)}
          />
          <Text size="sm" state="secondary" type="regular">
            SKU: {product?.skuCode}
          </Text>
        </div>
      </div>
    </div>
  )
}

export default ProductDetailColumn
