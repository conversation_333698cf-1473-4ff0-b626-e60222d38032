"use client"

import { Divider, Heading, Text } from "@kickavenue/ui/components"
import CenterWrapper from "@shared/CenterWrapper"
import { useProductStore } from "stores/productStore"

const ProductSummary = () => {
  const { detail: product } = useProductStore()
  return (
    <div className="static top-0 z-20 w-full bg-white shadow-[0px_4px_8px_-2px_#24242414] md:sticky md:top-[110px]">
      <Divider orientation="horizontal" type="solid" />
      <CenterWrapper className="items-center">
        <div className="col-span-4 md:col-span-6">
          <Heading heading="5" textStyle="bold" className="mb-xs">
            {product?.name || ""}
          </Heading>
          <div className="flex h-md items-center gap-xs">
            <Text size="base" state="secondary" type="regular">
              SKU: {product?.skuCode || ""}
            </Text>
            <Divider orientation="vertical" type="solid" />
            <Text size="base" state="secondary" type="regular">
              Size: US {product?.itemListing?.size?.us || ""}
            </Text>
          </div>
        </div>
      </CenterWrapper>
    </div>
  )
}

export default ProductSummary
