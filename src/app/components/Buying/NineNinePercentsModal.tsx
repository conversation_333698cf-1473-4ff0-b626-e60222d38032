import {
  But<PERSON>,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  IconCloseOutline,
  IconPerfectBulk,
  Space,
  Text,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import { TProductConditionKey } from "types/listing.type"
import Modal from "@shared/Modal"
import { useModalStore } from "stores/modalStore"
import { useMiscStore } from "stores/miscStore"
import { getProductCondtionUrlSegment } from "@utils/itemListing"

import styles from "./NineNinePercentsModal.module.scss"

const NineNinePercentsModal = () => {
  const { setOpen } = useModalStore()
  const { selectedListingCard } = useMiscStore()
  const router = useRouter()
  return (
    <Modal>
      <div className="flex justify-between p-md">
        <div className="flex grow justify-center text-right">
          <Heading heading="5" textStyle="bold">
            It&rsquo;s 99% Perfect!
          </Heading>
        </div>
        <IconCloseOutline
          onClick={() => setOpen(false)}
          className="scale-150 cursor-pointer"
        />
      </div>
      <Divider orientation="horizontal" />
      <div className="max-h-[496px] overflow-y-auto p-lg">
        <div className="flex flex-col items-center justify-center gap-lg text-center">
          <IconPerfectBulk width={64} height={64} color="#FA8C15" />
          <Text size="base" type="regular" state="secondary">
            This product passed our authentication standard with some minor
            notes. Please pay more attention to its overall appearances and
            details before proceeding to payment.
          </Text>
        </div>
        <Space size="lg" direction="y" type="margin" />
        <Divider orientation="horizontal" state="default" />
        <Space size="lg" direction="y" type="margin" />
        <div className={styles["text-content"]}>
          <Text size="base" type="bold" state="primary">
            Brand New Authentic
          </Text>
          <Space size="xs" direction="y" type="margin" />
          <Text size="base" type="regular" state="secondary">
            This product has gone through our authentication process to ensure
            its authenticity and unworn condition.
          </Text>
          <Space size="base" direction="y" type="margin" />

          <Text size="base" type="bold" state="primary">
            Product Photo Inspection
          </Text>
          <Space size="xs" direction="y" type="margin" />
          <Text size="base" type="regular" state="secondary">
            You can check the product’s actual photos along with its detailed
            condition and appearance.
          </Text>
          <Space size="base" direction="y" type="margin" />

          <Text size="base" type="bold" state="primary">
            Refund Policy
          </Text>
          <Space size="xs" direction="y" type="margin" />
          <Text size="base" type="regular" state="secondary">
            Kick Avenue does not accept returns or refunds on all items with
            this condition.
          </Text>
          <Space size="base" direction="y" type="margin" />
        </div>
      </div>
      <div className="p-lg shadow-base">
        <Button
          size="lg"
          variant="primary"
          className="!w-full"
          onClick={() => {
            setOpen(false)
            const segment = getProductCondtionUrlSegment(
              selectedListingCard?.productConditionKey as TProductConditionKey,
            )
            router.push(
              `/checkout-preview/${selectedListingCard?.sellerListingId}/${segment}`,
            )
          }}
        >
          Understand and Proceed
        </Button>
      </div>
    </Modal>
  )
}

export default NineNinePercentsModal
