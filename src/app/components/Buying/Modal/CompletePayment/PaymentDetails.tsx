import { IconCopyBulk, IconArrowRightOutline } from "@kickavenue/ui/components"

const PaymentDetails = () => {
  return (
    <>
      <div className="mt-3 flex items-center justify-between rounded-xl border border-gray-w-80 px-3 py-4">
        <div className="w-full text-sm text-gray-b-65">Invoice Number</div>
        <div className="flex w-full items-center justify-end">
          <div className="mr-1 text-sm font-bold text-gray-b-65">
            KA-150823-OB6-WBA
          </div>
          <div className="">
            <IconCopyBulk />
          </div>
        </div>
      </div>
      <div className="mt-3 rounded-t-xl border border-gray-w-80 p-3 text-sm text-gray-b-65">
        <div className="flex justify-between">
          <div>Total Payment</div>
          <div>IDR 4,100,000</div>
        </div>
        <div className="mt-3 flex justify-between">
          <div>Payment Method</div>
          <div>BCA Virtual Account</div>
        </div>
      </div>
      <div className="mb-6 flex items-center justify-between rounded-b-xl border border-t-0 border-gray-w-80 px-3 py-[14px]">
        <div className="text-sm font-bold text-gray-b-65">
          View Payment Summary
        </div>
        <div className="">
          <IconArrowRightOutline className="size-5" />
        </div>
      </div>
    </>
  )
}

export default PaymentDetails
