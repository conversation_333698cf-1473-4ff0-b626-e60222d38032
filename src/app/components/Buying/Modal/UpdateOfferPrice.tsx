import { Space } from "@kickavenue/ui/dist/src/components"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import UpdatePriceOptions from "@components/shared/UpdatePriceOptions"
import UpdatePriceInput from "@components/shared/UpdatePriceInput"
import UpdatePriceAlert from "@components/shared/UpdatePriceAlert"
import useBuyingUpdateOfferPriceOptions from "@app/hooks/useBuyingUpdateOfferPriceOptions"
import useBulkUpdateOfferPrice from "@app/hooks/useBulkUpdateOfferPrice"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"

const { UPDATE_OFFER_PRICE } = ModalConstant.MODAL_IDS

const UpdateOfferPrice = () => {
  const { setOpen } = useModalStore()
  const { options, handleOptionSelect } = useBuyingUpdateOfferPriceOptions()
  const { handleBulkUpdatePrice, isPending } = useBulkUpdateOfferPrice()

  const {
    updatePrice,
    updatePriceAction,
    selectedRowKeys,
    setUpdatePrice,
    setUpdatePriceAction,
  } = useBuyingOfferStore()

  const handleClearState = () => {
    setUpdatePrice(null)
    setUpdatePriceAction("")
  }

  const handleClose = () => {
    setOpen(false, UPDATE_OFFER_PRICE)
    handleClearState()
  }

  const handleConfirm = () => {
    handleBulkUpdatePrice()
  }

  return (
    <Modal modalId={UPDATE_OFFER_PRICE} className="sm:!max-w-[1036px]">
      <HeaderModal
        onClose={handleClose}
        title={`Update Offer Price (${selectedRowKeys.length} ${selectedRowKeys.length > 1 ? "items" : "item"})`}
      />
      <Space size="md" direction="y" type="margin" />
      <div className="h-[667px] overflow-y-auto">
        <UpdatePriceOptions
          options={options}
          handleOptionSelect={handleOptionSelect}
          renderUpdatePriceInput={() => (
            <UpdatePriceInput
              setUpdatePrice={setUpdatePrice}
              updatePriceAction={updatePriceAction}
              updatePrice={updatePrice}
            />
          )}
          renderUpdatePriceAlert={() => (
            <UpdatePriceAlert
              updatePriceAction={updatePriceAction}
              updatePrice={updatePrice}
              selectedRowKeys={selectedRowKeys}
            />
          )}
        />
      </div>
      <ModalCancelConfirm
        disableConfirm={
          !updatePrice || isPending || selectedRowKeys.length === 0
        }
        onConfirm={handleConfirm}
        onCancel={handleClose}
      />
    </Modal>
  )
}

export default UpdateOfferPrice
