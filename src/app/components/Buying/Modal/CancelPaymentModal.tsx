import { But<PERSON> } from "@kickavenue/ui/components"
import ModalConfirm from "@components/shared/ModalConfirm"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"

const { CANCEL_PAYMENT } = ModalConstant.MODAL_IDS

const CancelPaymentModal = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { cancelPayment } = useBuyingInProgressStore()

  const handleCancel = async () => {
    await cancelPayment()
    setOpen(false, CANCEL_PAYMENT)
  }

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, CANCEL_PAYMENT)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button size="md" variant="danger" onClick={handleCancel}>
        Cancel Payment
      </Button>
    </>
  )

  return (
    <ModalConfirm
      open={open && modalId === CANCEL_PAYMENT}
      onClose={() => setOpen(false, CANCEL_PAYMENT)}
      title="Are you sure you want to cancel this payment?"
      subtitle="Your payment is in progress. Canceling now means you'll need to start the payment process again if you want to purchase this item."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default CancelPaymentModal
