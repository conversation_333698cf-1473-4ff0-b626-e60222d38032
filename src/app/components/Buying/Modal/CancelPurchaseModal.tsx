import { But<PERSON> } from "@kickavenue/ui/components"
import ModalConfirm from "@components/shared/ModalConfirm"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"

const { CANCEL_PURCHASE } = ModalConstant.MODAL_IDS

const CancelPurchaseModal = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { cancelPurchase } = useBuyingInProgressStore()

  const handleCancel = async () => {
    await cancelPurchase()
    setOpen(false, CANCEL_PURCHASE)
  }

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, CANCEL_PURCHASE)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button size="md" variant="danger" onClick={handleCancel}>
        Cancel Purchase
      </Button>
    </>
  )

  return (
    <ModalConfirm
      open={open && modalId === CANCEL_PURCHASE}
      onClose={() => setOpen(false, CANCEL_PURCHASE)}
      title="Are you sure you want to cancel this purchase?"
      subtitle="Your purchase is confirmed and the item is reserved for you. Canceling now means giving up on the item you chose."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default CancelPurchaseModal
