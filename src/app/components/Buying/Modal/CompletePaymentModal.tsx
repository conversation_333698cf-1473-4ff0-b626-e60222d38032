import { Button } from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"

import PendingStatus from "./CompletePayment/PendingStatus"
import ProductDetails from "./CompletePayment/ProductDetails"
import PaymentDetails from "./CompletePayment/PaymentDetails"
import DeliveryDetails from "./CompletePayment/DeliveryDetails"

const { COMPLETE_PAYMENT } = ModalConstant.MODAL_IDS

const CompletePaymentModal = () => {
  const { setOpen } = useModalStore()
  const router = useRouter()

  const handleClose = () => {
    setOpen(false, COMPLETE_PAYMENT)
  }

  const handleCompletePayment = () => {
    setOpen(false, COMPLETE_PAYMENT)
    router.push("/payment/awaiting")
  }

  return (
    <Modal modalId={COMPLETE_PAYMENT}>
      <div className="flex flex-col gap-4">
        <HeaderModal onClose={handleClose} title="Pending Detail" />
        <div className="max-h-[450px] overflow-y-auto p-6">
          <PendingStatus />
          <ProductDetails />
          <PaymentDetails />
          <DeliveryDetails />
        </div>
        <ModalFooter>
          <div className="col-span-12">
            <Button
              size="lg"
              variant="primary"
              className="!w-full"
              onClick={handleCompletePayment}
            >
              Complete Payment
            </Button>
          </div>
        </ModalFooter>
      </div>
    </Modal>
  )
}

export default CompletePaymentModal
