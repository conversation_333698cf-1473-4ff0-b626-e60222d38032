import {
  IconCopyBulk,
  Space,
  Text,
  Divider,
  IconCopyBulkGreen,
} from "@kickavenue/ui/components"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"

import OrderTrackingTimeline from "../components/OrderTrackingTimeline"

import DeliveryDetails from "./CompletePayment/DeliveryDetails"

const { TRACK_SHIPMENT } = ModalConstant.MODAL_IDS

const TrackShipmentModal = () => {
  const { setOpen } = useModalStore()

  const handleClose = () => {
    setOpen(false, TRACK_SHIPMENT)
  }

  return (
    <Modal modalId={TRACK_SHIPMENT} className="sm:!max-w-screen-lg">
      <div className="flex flex-col">
        <HeaderModal onClose={handleClose} title="Track Your Shipment" />
        <div className="max-h-[767px] overflow-y-auto p-6">
          <h5 className="font-bold">Shipment Timeline</h5>
          <Space size="lg" direction="y" type="margin" />
          <Text size="sm" type="bold" state="primary">
            Shipment Info
          </Text>
          <Space size="sm" direction="y" type="margin" />

          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-5 flex flex-col">
              <div className="flex flex-col gap-2 text-sm text-gray-b-65">
                <div className="flex items-center justify-between">
                  <div>Tracking Number</div>
                  <div className="flex items-center gap-2">
                    <div>0157647292772733213</div>
                    <IconCopyBulk color="#0B7A68" />
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div>Current Status</div>
                  <div>In Transit</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>Estimated Delivery Date</div>
                  <div>17 Jul - 19 Jul 2024</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>Shipping Courier</div>
                  <div>JNT - Regular</div>
                </div>
              </div>
              <div className="mt-6">
                <DeliveryDetails />
              </div>
            </div>
            <div className="col-span-1 mx-auto max-h-[329px]">
              <Divider orientation="vertical" type="solid" />
            </div>
            <div className="col-span-6">
              <OrderTrackingTimeline />
            </div>
          </div>

          <Space size="lg" direction="y" type="margin" />
          <Text size="sm" type="bold" state="primary">
            Product Detail
          </Text>
          <Space size="sm" direction="y" type="margin" />
          <div className="flex w-full justify-between rounded-sm border border-solid border-gray-w-80 p-base">
            <ProductDetailPreview />
          </div>
          <Space size="sm" direction="y" type="margin" />
          <div className="flex w-full justify-between rounded-sm border border-solid border-gray-w-80 p-base">
            <Text size="sm" state="primary" type="regular">
              Invoice Number
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <Text size="sm" state="primary" type="bold">
                KA-150823-OB6-WBA
              </Text>
              <IconCopyBulkGreen />
            </div>
          </div>
          <div className="mt-6 w-full rounded-t-base border border-gray-w-80">
            <div className="flex flex-col gap-xs p-base">
              <div className="flex justify-between">
                <Text size="sm" type="medium" state="primary">
                  Payment Method
                </Text>
                <Text size="sm" type="medium" state="primary">
                  Split Payment
                </Text>
              </div>
            </div>
          </div>
          <div className="w-full rounded-b-base border border-gray-w-80">
            <div className="flex flex-col gap-xs p-base font-bold text-gray-b-65">
              <div className="flex justify-between">
                <Text size="sm" type="medium" state="primary">
                  Total Payment
                </Text>
                <Text size="sm" type="medium" state="primary">
                  IDR410,000
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default TrackShipmentModal
