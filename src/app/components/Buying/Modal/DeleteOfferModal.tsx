import { Space } from "@kickavenue/ui/dist/src/components"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import DeleteOfferAlert from "@components/Buying/DeleteOfferAlert"
import DeleteOfferTable from "@components/Buying/DeleteOfferTable"
import { BuyingOfferApiRepository } from "@infrastructure/repositories/buyingOfferApiRepository"

const { DELETE_OFFER } = ModalConstant.MODAL_IDS

export default function DeleteOfferModal() {
  const { setOpen } = useModalStore()
  const { selectedRowKeys } = useBuyingOfferStore()
  const queryClient = useQueryClient()

  const { mutate: deleteOffers, isPending } = useMutation({
    mutationFn: async () => {
      const repository = new BuyingOfferApiRepository()
      await repository.deleteOffers(selectedRowKeys)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["buyingOffers"],
      })
      setOpen(false, DELETE_OFFER)
    },
  })

  return (
    <Modal modalId={DELETE_OFFER} className="!max-w-[1036px]">
      <HeaderModal
        onClose={() => setOpen(false, DELETE_OFFER)}
        title="Delete Offer"
      />
      <Space size="md" direction="y" type="margin" />
      <div className="h-[331px] overflow-y-auto px-lg">
        <DeleteOfferAlert selectedRowKeys={selectedRowKeys} />
        <Space size="lg" direction="y" type="margin" />
        <DeleteOfferTable />
      </div>
      <Space size="lg" direction="y" type="margin" />
      <ModalCancelConfirm
        onCancel={() => setOpen(false, DELETE_OFFER)}
        onConfirm={deleteOffers}
        disableConfirm={false}
        isPending={isPending}
      />
    </Modal>
  )
}
