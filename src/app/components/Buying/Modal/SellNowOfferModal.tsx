import { Button } from "@kickavenue/ui/dist/src/components"
import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import ModalSummaryTotal from "@components/shared/ModalParts/ModalSummaryTotal"
import ModalSellingSummaries from "@components/shared/ModalParts/ModalSellingSummaries"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "@stores/modalStore"
import { useBuyingOfferStore } from "@stores/buyingOfferStore"
import { formatCurrency } from "@utils/separator"
import HeaderModal from "@components/shared/HeaderModal"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"

const { SELL_NOW_OFFER } = ModalConstant.MODAL_IDS

const SellNowOfferModal = () => {
  const { setOpen } = useModalStore()
  const { selectedOfferFromStore } = useBuyingOfferStore()

  const offerAmount = selectedOfferFromStore?.amount || 0
  const platformFee = offerAmount * 0.05
  const totalRevenue = offerAmount - platformFee

  const summaries = [
    {
      text: "Offer Amount",
      value: formatCurrency(offerAmount / 100, ",", "IDR"),
    },
    {
      text: "Platform Fee (5%)",
      value: formatCurrency(platformFee / 100, ",", "IDR"),
    },
  ]

  return (
    <Modal modalId={SELL_NOW_OFFER}>
      <div className="flex flex-col gap-4 p-4">
        <HeaderModal
          onClose={() => setOpen(false, SELL_NOW_OFFER)}
          title="Sell Now"
        />
        {selectedOfferFromStore && (
          <div className="flex flex-col gap-4">
            <ModalProduct
              itemName={selectedOfferFromStore.itemName}
              size={selectedOfferFromStore.size}
            />
            <ModalSellingSummaries items={summaries} />
            <ModalSummaryTotal
              text="Total Sales Revenue"
              value={formatCurrency(totalRevenue / 100, ",", "IDR")}
            />
          </div>
        )}
        <ModalFooter>
          <div className="col-span-12">
            <Button
              size="lg"
              variant="primary"
              disabled={false}
              className="!w-full"
              onClick={() => setOpen(false, SELL_NOW_OFFER)}
            >
              Sell Now
            </Button>
          </div>
        </ModalFooter>
      </div>
    </Modal>
  )
}

export default SellNowOfferModal
