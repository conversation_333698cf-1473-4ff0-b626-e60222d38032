import { But<PERSON> } from "@kickavenue/ui/components"
import ModalConfirm from "@components/shared/ModalConfirm"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"

const { RECEIVE_ITEM } = ModalConstant.MODAL_IDS

const ReceiveItemModal = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { receiveItem } = useBuyingInProgressStore()

  const handleReceive = async () => {
    await receiveItem()
    setOpen(false, RECEIVE_ITEM)
  }

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, RECEIVE_ITEM)}
        size="md"
        variant="secondary"
      >
        Still Waiting
      </Button>
      <Button size="md" variant="primary" onClick={handleReceive}>
        Confirm Delivery
      </Button>
    </>
  )

  return (
    <ModalConfirm
      open={open && modalId === RECEIVE_ITEM}
      onClose={() => setOpen(false, RECEIVE_ITEM)}
      title="Are you sure you have received the item?"
      subtitle="By confirming you have received the item, this will finalize your purchase and close the order."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default ReceiveItemModal
