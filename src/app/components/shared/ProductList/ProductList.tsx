import { DetailedHTMLProps, HTMLAttributes, useCallback, useState } from "react"
import { useRouter } from "next/navigation"
import {
  Product,
  TProductWishlist,
  TProductWishlistCb,
} from "types/product.type"
import { formatCurrencyStripe, getProductImageUrl } from "@utils/misc"
import ProductCard from "@shared/ProductCard"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import WishlistSaveModal from "@components/Wishlist/WishlistSaveModal"
import { ModalConstant } from "@constants/modal"

import useProductWishlist from "./hook/useProductWishlist"
import useAuthSession from "@app/hooks/useAuthSession"

const { WISHLIST_SAVE_PRODUCT_LIST } = ModalConstant.MODAL_IDS

export interface ProductListProps {
  list: Product[]
  containerProps?: DetailedHTMLProps<
    HTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  >
  productWishlist?: TProductWishlist
  onUnWishlisted?: TProductWishlistCb
  onWishlistAdded?: TProductWishlistCb
}

const ProductList = ({
  list,
  productWishlist,
  containerProps,
  onUnWishlisted,
  onWishlistAdded,
}: ProductListProps) => {
  const { className, ...rest } =
    containerProps ||
    ({} as DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement>)
  const router = useRouter()
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)

  const { handleWishlistClick: wishlistClick } = useProductWishlist(
    WISHLIST_SAVE_PRODUCT_LIST,
    onUnWishlisted,
  )

  const handleGoDetail = (product: Product) => {
    router.push(`/product/${product.id}`)
  }

  const { isUnauthenticated } = useAuthSession()

  const handleWishlistClick = useCallback(
    (product: Product) => {
      if (isUnauthenticated) {
        router.push(PageRouteConstant.LOGIN)
        return
      }
      setSelectedProduct(product)
      wishlistClick(product, productWishlist)
    },
    [isUnauthenticated, router, wishlistClick, productWishlist],
  )

  const getStrikeThroughPrice = useCallback((product: Product) => {
    const retailPrice = product?.retailPrice?.minUnitVal
    const lowestAsk = product?.lowestAsk?.minUnitVal

    if (!retailPrice || !lowestAsk) return ""
    if (Number(retailPrice) <= Number(lowestAsk)) return ""
    return formatCurrencyStripe({
      price: product?.retailPrice,
    })
  }, [])

  return (
    <>
      {(list || []).map((product) => (
        <div className={className} key={product?.id} {...rest}>
          <ProductCard
            cardContainer={{
              className: "w-fit",
              style: { width: "100%" },
            }}
            onWishlistClick={() => handleWishlistClick(product)}
            imageProps={{
              src: getProductImageUrl(product),
              width: 230,
              height: 230,
              alt: "Product Image",
              onClick: () => handleGoDetail(product),
              className: "cursor-pointer",
            }}
            brandName={product?.brands?.[0].name || ""}
            itemName={product?.name || ""}
            itemId={product?.id}
            price={formatCurrencyStripe({
              price: product?.lowestAsk,
            })}
            isWishlistActive={Boolean(productWishlist?.[product.id as number])}
            strikeThroughPrice={getStrikeThroughPrice(product)}
          />
        </div>
      ))}
      <WishlistSaveModal
        modalId={WISHLIST_SAVE_PRODUCT_LIST}
        product={selectedProduct || ({} as Product)}
        onWishlistAdded={onWishlistAdded}
      />
    </>
  )
}

export default ProductList
