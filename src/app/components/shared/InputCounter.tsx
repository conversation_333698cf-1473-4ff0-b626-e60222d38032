import { Button } from "@kickavenue/ui/components/Button"
import React from "react"

type Props = {
  onChange: (action: "inc" | "dec") => void
}

const InputCounter = (props: Props) => {
  return (
    <div className="flex flex-row items-center">
      <Button
        variant="link"
        style={{
          padding: 0,
          paddingLeft: 4,
          paddingRight: 4,
          fontSize: 16,
          color: "#242424",
        }}
        onClick={() => props.onChange("dec")}
      >
        -
      </Button>
      <Button
        variant="link"
        style={{
          padding: 0,
          paddingLeft: 4,
          paddingRight: 4,
          fontSize: 16,
          color: "#242424",
        }}
        onClick={() => props.onChange("inc")}
      >
        +
      </Button>
    </div>
  )
}

export default InputCounter
