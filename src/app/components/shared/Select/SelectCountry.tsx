"use client"

import { useCallback } from "react"
import { ControllerRenderProps } from "react-hook-form"
import AsyncCombobox from "@kickavenue/ui/components/AsyncCombobox"
import { TCountryParams } from "types/country.type"
import { CountryApiRepository } from "@infrastructure/repositories/countryApiRepository"

interface SelectCountryProps extends ControllerRenderProps {
  menuPlacement?: "top" | "bottom"
  placeholder?: string
}

const SelectCountry = (props: SelectCountryProps) => {
  const fetchAllCountries = async (filter?: TCountryParams) => {
    const r = new CountryApiRepository()
    const countries = await r.getAll(filter)
    return countries
  }

  const loadOptions = useCallback(
    async (search: string, opt: any, additional: any) => {
      const dataPage = additional?.page ?? 0
      const data = await fetchAllCountries({
        page: dataPage,
        pageSize: 10,
        ...(search && { name: search }),
      })
      return {
        options:
          data?.content.map((country) => ({
            value: country.country?.toString() ?? "",
            label: country.name ?? "",
          })) || [],
        hasMore: !data?.last,
        additional: { page: dataPage + 1 },
      }
    },
    [],
  )

  return (
    <AsyncCombobox
      loadOptions={loadOptions}
      additional={{ page: 0 }}
      placeholder={props.placeholder ?? "Select country"}
      isClearable
      {...props}
    />
  )
}

export default SelectCountry
