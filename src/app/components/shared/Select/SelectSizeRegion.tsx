import { MiscConstant } from "@constants/misc"
import { AsyncCombobox } from "@kickavenue/ui/components/index"
import { SelectOption } from "./types"

interface ISelectSizeRegionProps {
  value: SelectOption
  onChange: (newValue: unknown) => void
}

const SelectSizeRegion = ({ value, onChange }: ISelectSizeRegionProps) => {
  return (
    <div className="flex w-full flex-col gap-xs">
      <AsyncCombobox
        placeholder="Select Size Region"
        loadOptions={() => {
          return {
            options: MiscConstant.SIZE_REGION_OPTIONS,
            hasMore: false,
          }
        }}
        value={value}
        onChange={onChange}
      />
    </div>
  )
}

export default SelectSizeRegion
