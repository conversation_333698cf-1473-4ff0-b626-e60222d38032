import Spinner from "@components/shared/Spinner"
import { IconLogoKickAvenueSecondary } from "@kickavenue/ui/components/icons"
import { cx } from "class-variance-authority"

const LoadingPageSkeleton = ({ className }: { className?: string }) => {
  return (
    <div
      className={cx(
        "flex flex-col items-center justify-center gap-sm",
        "sm:max-w min-h-[calc(100vh-120px)] w-full animate-pulse px-sm",
        className,
      )}
    >
      <Spinner />
      <IconLogoKickAvenueSecondary className="w-full max-w-40" />
    </div>
  )
}

export default LoadingPageSkeleton
