/* eslint-disable no-warning-comments */
import { ButtonAction } from "@kickavenue/ui/components/Button"
import { useRouter } from "next/navigation"
import { EProductDetailState, useProductStore } from "stores/productStore"
import { TItemListing } from "types/itemListing.type"
import { TCountry } from "types/country.type"
import { TItemSummary } from "types/itemSummary.type"
import { findHighestOffer, formatPrice } from "@utils/productDetail"
import useSelectedSize from "@app/hooks/useSelectedSize"
import useAuthSession from "@app/hooks/useAuthSession"

const BuyAndSellButton = () => {
  const router = useRouter()
  const { isUnauthenticated } = useAuthSession()
  const { detail: product, setProductDetailState } = useProductStore()
  const itemListing = product?.itemListing || ({} as TItemListing)
  const itemSummary = product?.itemSummary || ({} as TItemSummary)
  const country = product?.country || ({} as TCountry)
  const { selectedSize } = useSelectedSize()
  const isSellable = product?.isReceiveSellRequest
  const validateSession = () => {
    if (isUnauthenticated) {
      router.push("/login")
    }
  }
  const handleBuyAction = () => {
    setProductDetailState(EProductDetailState.BuyingOfferSizeList)
  }
  const handleSellAction = () => {
    validateSession()
    setProductDetailState(EProductDetailState.SellingAskSizeList)
  }
  return (
    <>
      <div className="col-span-12 md:col-span-6">
        <ButtonAction
          variant="buy"
          isDisabled={false}
          title="Buy"
          subTitle="or Offer"
          Size={selectedSize}
          price={formatPrice(
            itemListing?.lowestAsk || 0,
            country || ({} as TCountry),
          )}
          isLoading={false}
          style={{ width: "100%" }}
          onClick={handleBuyAction}
          disabled={product?.isNonPurchaseable}
        />
      </div>
      <div className="col-span-12 md:col-span-6">
        <ButtonAction
          variant="sell"
          isDisabled={false}
          isLoading={false}
          title="Sell"
          subTitle="or Ask"
          // TODO: use this method when able to create data on backend
          // price={findStdBrandNewNoDefectHighestOffer(itemSummary, country, itemListing.size)}
          price={findHighestOffer(itemSummary, country, itemListing.size)}
          Size={selectedSize}
          style={{ width: "100%" }}
          onClick={handleSellAction}
          disabled={!isSellable}
        />
      </div>
    </>
  )
}

export default BuyAndSellButton
