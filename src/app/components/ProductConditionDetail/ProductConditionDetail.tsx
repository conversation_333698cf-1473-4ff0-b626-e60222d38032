"use client"

import {
  Divide<PERSON>,
  <PERSON><PERSON>,
  IconExpressBold,
  IconPreOrderBold,
  IconStandardBold,
  Space,
  Text,
} from "@kickavenue/ui/components"
import { useParams, useRouter } from "next/navigation"
import SimilarProduct from "@components/SimilarProduct"
import PageBreadcrumb from "@shared/PageBreadcrumb"
import CenterWrapper from "@shared/CenterWrapper"
import ProductImageSlider from "@components/ProductImageSlider"
import ProductDetails from "@components/ProductOverview/ProductDetails"
import ProductDescriptions from "@components/ProductOverview/ProductDescriptions"
import ListingBadge from "@shared/ListingBadge"
import FixedBottomButton from "@shared/FixedBottomButton"
import { useListingItemStore } from "stores/listingItemStore"
import {
  getBadgeTextByListingItem,
  getBadgeTypeByListingItem,
  getFullListingCondtion,
} from "@utils/listingItem"
import { TBadgeType } from "types/misc.type"
import useFetchListingItemById from "@app/hooks/useFetchListingItemById"
import { formatCurrencyStripe } from "@utils/misc"
import { concatBrand } from "@utils/product.utils"
import { useProductStore } from "stores/productStore"
import SpinnerLoading from "@components/shared/SpinnerLoading"

const ProductConditionDetail = () => {
  const router = useRouter()

  const params = useParams<{ listingid: string }>()
  const listingid = params?.listingid
  const idListing = listingid ? parseInt(listingid, 10) : 0

  const { detail: listingItem, setListingItemDetail } = useListingItemStore()
  const { setProductDetail, detail: product } = useProductStore()

  const { isLoading } = useFetchListingItemById(
    idListing,
    Boolean(idListing),
    (listingItem) => {
      setListingItemDetail(listingItem)
      setProductDetail(listingItem?.item)
    },
  )

  const getBadgeIconLeft = () => {
    if (listingItem?.isConsignment || listingItem?.isConsigment) {
      return IconExpressBold
    }
    if (listingItem.isPreOrder) {
      return IconPreOrderBold
    }
    return IconStandardBold
  }

  if (isLoading) {
    return <SpinnerLoading />
  }

  return (
    <>
      <PageBreadcrumb
        listItem={[
          { name: product?.category?.name || "", path: "/" },
          { name: product?.brands?.[0]?.name || "", path: "/product" },
          { name: product?.name || "", path: "/product/detail" },
        ]}
      />
      <CenterWrapper className="pb-0">
        <div className="col-span-4 md:col-span-6">
          <ProductImageSlider images={listingItem?.overallAppearanceImage} />
        </div>
        <div className="col-span-4 md:col-span-6">
          <ListingBadge
            isUsed={listingItem?.itemCondition === "USED"}
            is99Percents={listingItem?.packagingCondition !== "PERFECT_BOX"}
            badgeType={getBadgeTypeByListingItem(listingItem) as TBadgeType}
            type={getBadgeTypeByListingItem(listingItem)}
            iconLeft={getBadgeIconLeft()}
            text={getBadgeTextByListingItem(listingItem)}
          />
          <Space size="xs" direction="y" type="margin" />
          <Text className="mb-xxs" size="base" type="regular" state="secondary">
            Lowest Ask
          </Text>
          <Heading className="mb-sm" heading="4" textStyle="bold">
            {formatCurrencyStripe({ price: listingItem?.sellingPrice })}
          </Heading>
          <Text className="mb-xxs" size="base" type="regular" state="secondary">
            {concatBrand(product?.brands || [])}
          </Text>
          <Heading className="mb-lg" heading="5" textStyle="medium">
            {product?.name || ""}
          </Heading>
          <ProductDetails product={product} />
          <ProductDescriptions description={listingItem?.note} />
          <Heading className="mb-base" heading="5" textStyle="bold">
            Condition
          </Heading>
          <Text size="sm" state="secondary" type="regular">
            {getFullListingCondtion(listingItem)}
          </Text>
        </div>
      </CenterWrapper>
      <Space size="xl" direction="y" type="margin" />
      <Divider orientation="horizontal" type="solid" />
      <Space size="xl" direction="y" type="margin" />
      <SimilarProduct />
      <FixedBottomButton
        onClick={() =>
          router.push(`/checkout-preview/${listingItem?.id}?key=true`)
        }
        text="Continue to Buy / Offer"
      />
    </>
  )
}

export default ProductConditionDetail
