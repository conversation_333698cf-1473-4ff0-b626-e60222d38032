"use client"

import {
  IconBurgerMenuOutline,
  IconSearchOutline,
  Logo,
} from "@kickavenue/ui/components/icons"
import Link from "next/link"
import useHeaderMenuDrawer from "@app/hooks/useHeaderMenuDrawer"
import CenterWrapper from "@app/components/shared/CenterWrapper"
import { useMiscStore } from "stores/miscStore"

import HeaderMenuDrawer from "./HeaderMenuDrawer"

const HeaderTopDrawer = () => {
  const { setShowDrawer, showDrawer } = useHeaderMenuDrawer()
  const { setShowExpandedSearch } = useMiscStore()
  return (
    <>
      <CenterWrapper className="items-center md:hidden">
        <div className="col-span-1">
          <IconBurgerMenuOutline
            onClick={() => {
              setShowDrawer(true)
            }}
          />
        </div>
        <div className="col-span-2">
          <Link href="/">
            <div className="h-auto w-full">
              <Logo />
            </div>
          </Link>
        </div>
        <div className="col-span-1 flex justify-end">
          <IconSearchOutline onClick={() => setShowExpandedSearch(true)} />
        </div>
      </CenterWrapper>
      <HeaderMenuDrawer showDrawer={showDrawer} setShowDrawer={setShowDrawer} />
    </>
  )
}

export default HeaderTopDrawer
