"use client"

import SearchInput from "@components/shared/SearchInput"
import useHeaderTopNonDrawer from "@hooks/useHeaderTopNonDrawer"
import { IconLogoKickAvenueSecondary } from "@kickavenue/ui/components/icons"
import { cx } from "class-variance-authority"
import Link from "next/link"
import AvatarProfile from "./AvatarProfile"

const navLinks = [
  { label: "Home", href: "/" },
  { label: "Market", href: "/search" },
  { label: "Sell", href: "/sell-consignment" },
]

const HeaderTopNonDrawer = () => {
  const {
    ref,
    searchKeyword,
    setSearchKeyword,
    setShowExpandedSearch,
    handleInputChange,
  } = useHeaderTopNonDrawer()

  return (
    <div className="flex flex-col justify-center bg-white">
      {/* default header */}
      <div
        className={cx(
          "hidden items-center gap-lg p-sm md:flex md:px-xxl xl:px-0",
          "m-auto w-full xl:max-w-[calc(100vw-80px)]",
        )}
      >
        <Link href="/">
          <IconLogoKickAvenueSecondary className="!w-[188px]" />
        </Link>
        <div className="w-full">
          <SearchInput
            size="sm"
            placeholder="1,000,000+ authentic items here"
            onFocus={() => setShowExpandedSearch(true)}
            onChange={handleInputChange}
            onClearText={() => setSearchKeyword("")}
            value={searchKeyword}
            ref={ref}
          />
        </div>
        <div className="flex h-full items-center justify-end gap-lg">
          <nav>
            <ul className="flex gap-lg text-base">
              {navLinks.map((link) => (
                <li key={link.label} className="leading-none">
                  <Link href={link.href}>{link.label}</Link>
                </li>
              ))}
            </ul>
          </nav>
          <AvatarProfile />
        </div>
      </div>
    </div>
  )
}

export default HeaderTopNonDrawer
