import {
  Avatar,
  Button,
  DropdownItemProps,
  IconArrowDownOutline,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import { listSideBar } from "@components/Profile/sidebar.utils"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { getMemberFullName } from "@utils/member.utils"
import { TMember } from "types/member.type"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { useMemberStore } from "stores/memberStore"
import { useState } from "react"
import SkeletonText from "@components/shared/Skeleton/SkeletonText"
import useAuthSession from "@app/hooks/useAuthSession"

const AvatarProfile = () => {
  const router = useRouter()
  const { isAuthenticated, isUnauthenticated, isLoading } = useAuthSession()
  const { member, isLoading: isMemberLoading } = useMemberStore()
  const { isLoading: isLoadingPage, goToPage } = useChangePage()
  const [isOpen, setIsOpen] = useState(false)

  const options = listSideBar.map((item) => ({
    text: item.title,
    value: item.title,
    iconLeading: item.icon,
  }))

  let avatarUrl = ""
  avatarUrl = member?.image ? convertS3UrlToCloudFront(member.image) : ""

  const handleOnItemSelect = (opt: DropdownItemProps) => {
    const item = listSideBar.find((i) => i.title === opt.value)
    if (!item) return
    router.push(item.pathname)
  }

  if (isLoading || isMemberLoading) {
    return <SkeletonText className="!h-8 !w-20" />
  }

  if (isUnauthenticated && !member?.id) {
    return (
      <div className="flex gap-xs">
        <Button
          variant="secondary"
          size="md"
          disabled={isLoadingPage[PageRouteConstant.LOGIN]}
          onClick={() => goToPage(PageRouteConstant.LOGIN)}
        >
          Login
        </Button>
        <Button
          variant="primary"
          size="md"
          disabled={isLoadingPage[PageRouteConstant.REGISTER]}
          onClick={() => goToPage(PageRouteConstant.REGISTER)}
        >
          Register
        </Button>
      </div>
    )
  }

  if (isAuthenticated && Boolean(member?.id)) {
    return (
      <div
        className="flex cursor-pointer items-center gap-xs"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Avatar
          name={getMemberFullName(member as TMember)}
          url={avatarUrl}
          size="md"
        />
        <DropdownDynamicChild
          open={isOpen}
          options={options}
          placement="rightBottom"
          onItemSelect={handleOnItemSelect}
          optionClassName="[&>button]:!py-xs [&>button]:!px-sm !z-30 !overflow-hidden"
          optionStyle={{ top: "30px" }}
        >
          <IconArrowDownOutline />
        </DropdownDynamicChild>
      </div>
    )
  }

  return <SkeletonText className="!h-8 !w-20" />
}

export default AvatarProfile
