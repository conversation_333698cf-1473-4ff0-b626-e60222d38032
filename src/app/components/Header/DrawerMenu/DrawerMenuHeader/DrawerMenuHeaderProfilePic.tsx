import { Avatar } from "@kickavenue/ui/dist/src/components"
import { isValidUrl } from "@utils/misc"

export interface DrawerMenuHeaderProfilePicProps {
  urlProfile: string
  name: string
}

const DrawerMenuHeaderProfilePic = ({
  urlProfile,
  name,
}: DrawerMenuHeaderProfilePicProps) => {
  const url = isValidUrl(urlProfile) ? urlProfile : undefined
  return <Avatar name={name} size="xl" url={url} />
}

export default DrawerMenuHeaderProfilePic
