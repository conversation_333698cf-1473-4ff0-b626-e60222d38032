import { Button, Text } from "@kickavenue/ui/dist/src/components"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"

import styles from "../DrawerMenu.module.css"

import DrawerMenuHeaderProfilePic from "./DrawerMenuHeaderProfilePic"

export const ProfileInfo = ({ isLogin, urlProfile, name, email }: any) => {
  const { isLoading, goToPage } = useChangePage()
  if (isLogin) {
    return (
      <div className={styles.profile}>
        <DrawerMenuHeaderProfilePic urlProfile={urlProfile} name={name} />
        <div>
          <Text size="base" type="bold" state="primary">
            {name}
          </Text>
          <Text size="base" type="regular" state="secondary">
            {email}
          </Text>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.btn}>
      <Button
        size="md"
        variant="secondary"
        disabled={isLoading[PageRouteConstant.LOGIN]}
        onClick={() => goToPage(PageRouteConstant.LOGIN)}
      >
        Login
      </Button>
      <Button
        size="md"
        variant="primary"
        disabled={isLoading[PageRouteConstant.REGISTER]}
        onClick={() => goToPage(PageRouteConstant.REGISTER)}
      >
        Register
      </Button>
    </div>
  )
}
