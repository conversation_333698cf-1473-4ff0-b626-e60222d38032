"use client"

import Tab from "@kickavenue/ui/components/Tab"
import { useEffect, useState } from "react"
import useURLQuery from "@app/hooks/useUrlQuery"

import styles from "./HeaderBottomNav.module.scss"
import { PRODUCT_CATEGORIES } from "@constants/productCategories"
import { useRouter } from "next/navigation"
import { objectToQueryParams } from "@utils/query.utils"

const HeaderBottomNav = () => {
  const router = useRouter()
  const [currentCategory, setCurrentCategory] = useState<string>("")
  const { getSearchParam } = useURLQuery()

  const handleClick = ({ category, id }: { category: string; id: string }) => {
    setCurrentCategory(category)

    const searchPath = objectToQueryParams({
      path: "/search",
      query: {
        category,
        categoryID: id,
      },
    })
    router.push(searchPath)
  }

  useEffect(() => {
    const category = getSearchParam("category") || ""

    setCurrentCategory(category as string)
  }, [getSearchParam, setCurrentCategory])

  return (
    <div className={styles["nav-wrapper"]}>
      <Tab className={styles["nav-tab"]}>
        {PRODUCT_CATEGORIES.map(({ category, id }) => (
          <button
            key={category}
            data-active={
              currentCategory === category || currentCategory.includes(category)
            }
            data-text={category}
            onClick={(e) => {
              e.preventDefault()
              handleClick({ category, id })
            }}
          >
            {category}
          </button>
        ))}
      </Tab>
    </div>
  )
}

export default HeaderBottomNav
