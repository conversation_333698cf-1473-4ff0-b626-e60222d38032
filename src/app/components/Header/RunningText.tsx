import useRunningTextGetCurrent from "@components/HomeComponent/hooks/useRunningTextGetCurrent"
import {
  InfiniteWoodenBlock,
  WoodenBlockFace,
  Text,
} from "@kickavenue/ui/components/index"
import { cn } from "@kickavenue/ui/lib/utils"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useMemo } from "react"

const RunningText = () => {
  const pathname = usePathname()
  const isHomePage = useMemo(() => pathname === "/", [pathname])

  const { data: runningTextData } = useRunningTextGetCurrent()

  const runningTextItems = useMemo(() => {
    if (!runningTextData) return []
    return runningTextData.content
  }, [runningTextData])

  if (!isHomePage) return null
  if (!runningTextItems.length) return null

  return (
    <InfiniteWoodenBlock animationType="step" stepDelay={5}>
      {runningTextItems.map((item) => (
        <WoodenBlockFace key={item.id} className="bg-gray-b-65">
          {item.redirectUrl ? (
            <Link
              href={item.redirectUrl}
              className="flex size-full items-center justify-center"
            >
              <Text
                size="sm"
                type="bold"
                state="primary"
                className={cn(
                  "mx-auto line-clamp-1 !cursor-pointer !text-center !text-white",
                  "m-auto px-sm md:px-xxl xl:max-w-[calc(100vw-80px)] xl:px-0",
                )}
              >
                {item.content}
              </Text>
            </Link>
          ) : (
            <div className="flex size-full items-center justify-center">
              <Text
                size="sm"
                type="bold"
                state="primary"
                className={cn(
                  "mx-auto line-clamp-1 !cursor-pointer !text-center !text-white",
                  "m-auto px-sm md:px-xxl xl:max-w-[calc(100vw-80px)] xl:px-0",
                )}
              >
                {item.content}
              </Text>
            </div>
          )}
        </WoodenBlockFace>
      ))}
    </InfiniteWoodenBlock>
  )
}

export default RunningText
