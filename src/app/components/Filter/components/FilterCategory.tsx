import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@kickavenue/ui/components"
import { useCallback, useEffect, useState } from "react"
import useFetchCategories from "@app/hooks/useFetchCategories"
import { ECategoryType, TCategory } from "types/category.type"
import { TProductFilterKey } from "types/product.type"
import { event } from "@lib/gtag"

import { FilterHeader } from "./FilterHeader"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { useGetQueryParams } from "@app/hooks/useGetQueryParams"
import { cn } from "@kickavenue/ui/lib/utils"
import { useInView } from "react-intersection-observer"
import Spinner from "@components/shared/Spinner"

const { CategoryId, Category, SubCategory, SubCategoryId, Size, SizeId } =
  TProductFilterKey

const FilterCategory = () => {
  const replaceQueryParams = useReplaceQueryParams()
  const queryParams = useGetQueryParams()
  const { ref, inView } = useInView()
  const [isOpen, setIsOpen] = useState(false)

  const { categories, hasNextPage, isLoading, fetchNextPage } =
    useFetchCategories({
      type: ECategoryType.Category,
    })

  const queryCategory = queryParams.get<string>(Category)

  const isSelectedCategory = useCallback(
    (category: TCategory) => {
      return queryCategory === category.name
    },
    [queryCategory],
  )

  const handleClickCategory = useCallback(
    (category: TCategory) => {
      replaceQueryParams(
        {
          [CategoryId]: category?.id,
          [Category]: category?.name,
          [SubCategory]: "",
          [SubCategoryId]: "",
        },
        [Size, SizeId],
      )

      event({
        action: "product_searched",
        params: {
          category: category?.name as string,
        },
      })
    },
    [replaceQueryParams],
  )

  useEffect(() => {
    if (inView && !isLoading) {
      fetchNextPage()
    }
  }, [inView, isLoading, fetchNextPage])

  return (
    <div className="my-4">
      <FilterHeader
        title="Category"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
        className={cn(isOpen && "!mb-0", "!mb-4")}
      />
      <div
        className={cn(
          "scrollbar-hide flex flex-col gap-1 overflow-y-auto",
          "transition-all duration-500 ease-out",
          isOpen
            ? "max-h-[365px] translate-y-0 opacity-100"
            : "max-h-0 -translate-y-2 opacity-0",
        )}
      >
        {categories && categories?.length > 0
          ? categories?.map((category) => (
              <Button
                key={category.id}
                variant="secondary"
                onClick={() => handleClickCategory(category)}
                className={cn(
                  `!flex !w-full !justify-start !border-none !py-2 !text-[14px] focus:!bg-gray-w-95`,
                  isSelectedCategory(category)
                    ? "!bg-gray-w-95 !font-bold"
                    : "!font-medium",
                )}
              >
                {category.name}
              </Button>
            ))
          : null}
        {hasNextPage && (
          <div ref={ref} className="flex justify-center">
            <Spinner />
          </div>
        )}
      </div>
      <Divider orientation="horizontal" className={cn(isOpen && "!mt-4")} />
    </div>
  )
}

export default FilterCategory
