import React from "react"
import { CheckBox, ColorPicker, Chip } from "@kickavenue/ui/components"

import {
  FilterItem,
  FilterGroupProps,
  isPriceRangeFilterGroupProps,
} from "../types"
import { cn } from "@kickavenue/ui/lib/utils"

interface SizeButtonProps {
  size: string
  isSelected: boolean
  onClick: () => void
}

export const SizeButton: React.FC<SizeButtonProps> = ({
  size,
  isSelected,
  onClick,
}) => (
  <Chip
    size="md"
    onClick={onClick}
    isSelected={isSelected}
    className={cn(
      "!w-full cursor-pointer !justify-center",
      "data-[is-selected='true']:!bg-gray-w-90",
      "data-[is-selected='false']:!text-gray-b-65",
    )}
  >
    {size}
  </Chip>
)

interface DefaultItemProps {
  item: FilterItem
  isSelected: boolean
  onClick: (itemName: string) => void
}

export const DefaultItem: React.FC<DefaultItemProps> = ({
  item,
  isSelected,
  onClick,
}) => (
  <div className="mt-3 flex items-center">
    <CheckBox
      className="mr-2"
      checked={isSelected}
      onChange={() => onClick(item.name)}
    />
    <div className="text-base text-gray-b-65">{item.name}</div>
  </div>
)

interface ColorItemProps {
  item: FilterItem
  isSelected: boolean
  onClick: (itemName: string) => void
}

export const ColorItem: React.FC<ColorItemProps> = ({
  item,
  isSelected,
  onClick,
}) => (
  <ColorPicker
    key={item.id}
    color={item.color || "#000000"}
    label={item.name}
    className="mb-2 !cursor-pointer"
    checked={isSelected}
    onChange={() => onClick(item.name)}
  />
)

export const updateFromURL = (
  searchParams: URLSearchParams,
  props: FilterGroupProps,
  title: string | unknown,
) => {
  const getParamKey = (title: string) => {
    switch (title) {
      case "Brands":
        return "brand"
      case "Shipping Method":
        return "shippingMethod"
      default:
        return title.toLowerCase()
    }
  }

  const paramKey =
    typeof title === "string" ? getParamKey(title) : String(title)
  const paramValue = searchParams.get(paramKey)
  const selectedItems: string[] = paramValue ? paramValue.split(",") : []

  let minPrice = ""
  let maxPrice = ""
  if (isPriceRangeFilterGroupProps(props)) {
    const priceRange = searchParams.get("priceRange")
    if (priceRange) {
      const [min, max] = priceRange.split("-")
      minPrice = min || ""
      maxPrice = max || ""
    }
  }

  return { selectedItems, minPrice, maxPrice }
}
