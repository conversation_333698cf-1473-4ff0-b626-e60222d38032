import { Divider, Text } from "@kickavenue/ui/dist/src/components"
import { useCallback, useEffect, useMemo, useState } from "react"
import { useInView } from "react-intersection-observer"
import Spinner from "@components/shared/Spinner"
import useFetchUniqueSize from "@app/hooks/useFetchUniqueSize"
import { getUniqueSizeIds } from "@utils/sizeChart.utils"
import { ESizeGender, ESizeRegion, TUniqueSize } from "types/sizeChart.type"
import { TProductFilterKey } from "types/product.type"
import { event } from "@lib/gtag"

import { SizeButton } from "./FilterItem"
import { FilterHeader } from "./FilterHeader"
import { useGetQueryParams } from "@app/hooks/useGetQueryParams"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import SelectSizeRegion from "@components/shared/Select/SelectSizeRegion"
import { MiscConstant } from "@constants/misc"
import { SelectOption } from "@components/shared/Select/types"

const { SizeId, Size, Category } = TProductFilterKey

interface IFilterSizeProps {
  title: string
  gender: ESizeGender
}

// eslint-disable-next-line max-lines-per-function
const FilterSize = ({ title = "Size", gender }: IFilterSizeProps) => {
  const { ref, inView } = useInView()
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [sizeRegion, setSizeRegion] = useState<SelectOption>(
    MiscConstant.SIZE_REGION_OPTIONS[0],
  )

  const replaceQueryParams = useReplaceQueryParams()
  const queryParams = useGetQueryParams()
  const querySize = queryParams.get<string[]>(Size, []).filter(Boolean)
  const querySizeID = queryParams
    .get<number[][]>(SizeId, [[]])
    .filter((item) => item.length > 0)
  const queryCategory = queryParams.get<string>(Category, "")

  const categoryIsSneakersOrApparel = useMemo(() => {
    if (!queryCategory) return false

    return MiscConstant.CATEGORY_IS_SNEAKERS_OR_APPAREL.includes(
      queryCategory.toLowerCase() as (typeof MiscConstant.CATEGORY_IS_SNEAKERS_OR_APPAREL)[number],
    )
  }, [queryCategory])

  const {
    data: uniqueSize,
    hasNextPage,
    isFetching,
    fetchNextPage,
  } = useFetchUniqueSize({
    params: {
      gender: gender,
      category: queryCategory,
      region: sizeRegion.value as ESizeRegion,
    },
  })

  const transformedUniqueSize = useMemo(() => {
    const uniqueSizeData = uniqueSize?.pages.map((page) => page.content).flat()

    return uniqueSizeData?.map((item) => ({
      ...item,
    }))
  }, [uniqueSize])

  const querySizeIDExistingCheck = useCallback(
    (item: TUniqueSize) => {
      return querySizeID.some(
        (arr) =>
          arr.length === item.sizeIds.length &&
          arr.every((val, idx) => val === item.sizeIds[idx]),
      )
    },
    [querySizeID],
  )

  const handleSizeClick = useCallback(
    (item: TUniqueSize) => {
      const joinSizeIds = item.sizeIds.join(",")

      event({
        action: "product_searched",
        params: {
          size: `${item.label} | ${joinSizeIds}`,
        },
      })

      const isExist = querySizeIDExistingCheck(item)

      const newQuerySize = isExist
        ? querySize.filter((size) => size !== item.label)
        : [...querySize, item.label]
      const newQuerySizeID = isExist
        ? querySizeID
            .map((size) => size.join(","))
            .filter((size) => size !== joinSizeIds)
            .map((size) => size.split(","))
        : [...querySizeID, item.sizeIds]

      replaceQueryParams({
        [Size]: newQuerySize,
        [SizeId]: newQuerySizeID,
      })
    },
    [querySize, querySizeID, querySizeIDExistingCheck, replaceQueryParams],
  )

  useEffect(() => {
    if (inView && !isFetching) {
      fetchNextPage()
    }
  }, [inView, isFetching, fetchNextPage])

  return (
    <>
      <FilterHeader
        title={title}
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
        className="!my-base"
      />
      {isOpen && queryCategory && categoryIsSneakersOrApparel && (
        <>
          <SelectSizeRegion
            value={sizeRegion}
            onChange={(newValue) => {
              setSizeRegion(newValue as SelectOption)
            }}
          />
          <div className="mt-4 max-h-[490px] overflow-y-auto pb-4">
            <div className="grid h-full grid-cols-4 gap-2">
              {transformedUniqueSize?.map((item) => {
                const isExist = querySizeIDExistingCheck(item)
                return (
                  <SizeButton
                    key={getUniqueSizeIds(item)}
                    size={item.label}
                    isSelected={isExist}
                    onClick={() => handleSizeClick(item)}
                  />
                )
              }) ?? <></>}
            </div>
            {hasNextPage && (
              <div ref={ref} className="flex justify-center">
                <Spinner className="my-2" />
              </div>
            )}
          </div>
        </>
      )}
      {isOpen && !queryCategory && !categoryIsSneakersOrApparel && (
        <div className="my-4">
          <Text size={"base"} type={"medium"} state={"disabled"}>
            Please select category first
          </Text>
        </div>
      )}
      {isOpen && queryCategory && !categoryIsSneakersOrApparel && (
        <div className="my-4">
          <Text size={"base"} type={"medium"} state={"disabled"}>
            No Size Available
          </Text>
        </div>
      )}
      <Divider orientation="horizontal" />
    </>
  )
}

export default FilterSize
