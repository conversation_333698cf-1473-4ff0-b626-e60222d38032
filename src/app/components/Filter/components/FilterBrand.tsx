import { useCallback, useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON>, Divider } from "@kickavenue/ui/dist/src/components"
import useFetchBrands from "@app/hooks/useFetchBrands"
import SearchInput from "@components/shared/SearchInput"
import { TBrandItem } from "types/brand.type"
import { TProductFilterKey } from "types/product.type"
import { event } from "@lib/gtag"

import { FilterHeader } from "./FilterHeader"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { useGetQueryParams } from "@app/hooks/useGetQueryParams"
import { useInView } from "react-intersection-observer"
import Spinner from "@components/shared/Spinner"
import { cn } from "@kickavenue/ui/lib/utils"

const { BrandId, Brand } = TProductFilterKey

const FilterBrand = () => {
  const { ref, inView } = useInView()
  const queryParams = useGetQueryParams()
  const replaceQueryParams = useReplaceQueryParams()
  const [isOpen, setIsOpen] = useState(true)

  const queryBrands = queryParams.get<string[]>(Brand, []).filter(Boolean)
  const queryBrandIds = queryParams.get<string[]>(BrandId, []).filter(Boolean)

  const { brands, handleBrandSearch, isLoading, fetchNextPage, hasNextPage } =
    useFetchBrands()

  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleBrandSearch(e.target.value)
    },
    [handleBrandSearch],
  )

  const queryBrandIDExistingCheck = useCallback(
    (brand: TBrandItem) => {
      return queryBrandIds.some((arr) => arr === brand.id.toString())
    },
    [queryBrandIds],
  )

  const handleCheckboxChange = useCallback(
    (brand: TBrandItem) => {
      const isExist = queryBrandIDExistingCheck(brand)

      const newQueryBrandIds = isExist
        ? queryBrandIds.filter((id) => id !== brand.id.toString())
        : [...queryBrandIds, brand.id.toString()]
      const newQueryBrands = isExist
        ? queryBrands.filter((item) => item !== brand.name)
        : [...queryBrands, brand.name]

      replaceQueryParams({
        [BrandId]: newQueryBrandIds,
        [Brand]: newQueryBrands,
      })

      event({
        action: "product_searched",
        params: {
          brand: newQueryBrands.join(" | "),
        },
      })
    },
    [queryBrandIds, queryBrands, replaceQueryParams, queryBrandIDExistingCheck],
  )

  useEffect(() => {
    if (inView && !isLoading) {
      fetchNextPage()
    }
  }, [inView, isLoading, fetchNextPage])

  return (
    <div className="relative my-4">
      <FilterHeader
        title="Brand"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
        className={cn(isOpen && "!mb-0", "!mb-4")}
      />
      <div
        className={cn(
          "scrollbar-hide flex flex-col gap-3 overflow-y-auto",
          "transition-all duration-500 ease-out",
          isOpen
            ? "max-h-[360px] translate-y-0 opacity-100"
            : "max-h-0 -translate-y-2 opacity-0",
        )}
      >
        <SearchInput
          title="Brand"
          onClearText={() => {}}
          onChange={handleSearch}
          className="sticky top-0 z-10 mb-1 bg-white"
        />
        {brands?.map((brand) => {
          const isChecked = queryBrandIDExistingCheck(brand)
          return (
            <CheckBox
              key={brand.id}
              label={brand.name}
              checked={isChecked}
              onChange={() => handleCheckboxChange(brand)}
            />
          )
        })}
        {hasNextPage && (
          <div ref={ref} className="flex justify-center">
            <Spinner className="my-2" />
          </div>
        )}
      </div>
      <Divider orientation="horizontal" className={cn(isOpen && "!mt-4")} />
    </div>
  )
}

export default FilterBrand
