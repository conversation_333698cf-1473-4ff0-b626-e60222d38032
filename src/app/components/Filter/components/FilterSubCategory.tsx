import { useCallback, useEffect, useState } from "react"
import { CheckBox, Divider, Text } from "@kickavenue/ui/dist/src/components"
import {
  getNewSelectedFilters,
  isFilterSelected,
  mapCategoryToOption,
  mapFilterParamsToOption,
} from "@utils/product.utils"
import { ECategoryType, TCategory } from "types/category.type"
import { TProductFilterKey } from "types/product.type"
import { stringifyFilterOption } from "@components/FilterDashboard/utils"
import { event } from "@lib/gtag"

import { FilterHeader } from "./FilterHeader"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { useGetQueryParams } from "@app/hooks/useGetQueryParams"
import { useInView } from "react-intersection-observer"
import { cn } from "@kickavenue/ui/lib/utils"
import useFetchCategories from "@app/hooks/useFetchCategories"
import Spinner from "@components/shared/Spinner"

const { Category, CategoryId, SubCategoryId, SubCategory } = TProductFilterKey

const FilterSubcategory = () => {
  const replaceQueryParams = useReplaceQueryParams()
  const queryParams = useGetQueryParams()
  const { ref, inView } = useInView()
  const [isOpen, setIsOpen] = useState(false)
  const queryCategory = queryParams.get<string>(Category)
  const queryCategoryId = queryParams.get<string>(CategoryId)
  const queryParamsAllInString = new URLSearchParams(queryParams.all).toString()

  const {
    categories: subCategories,
    hasNextPage,
    isLoading,
    fetchNextPage,
  } = useFetchCategories({
    type: ECategoryType.SubCategory,
    enabled: Boolean(queryCategory) || Boolean(queryCategoryId),
    parentID: queryCategoryId ? Number(queryCategoryId) : undefined,
  })

  const isSelectedSubCategory = useCallback(
    (category: TCategory) =>
      isFilterSelected(
        mapFilterParamsToOption({
          params: queryParamsAllInString,
          labelKey: SubCategoryId,
          valueKey: SubCategoryId,
        }),
        mapCategoryToOption(category),
      ),
    [queryParamsAllInString],
  )

  const handleCheckboxChange = useCallback(
    (subcategory: TCategory) => {
      const prevSelected = mapFilterParamsToOption({
        params: queryParamsAllInString,
        labelKey: SubCategory,
        valueKey: SubCategoryId,
      })
      const selected = mapCategoryToOption(subcategory)

      const selectedSubcategories = getNewSelectedFilters(
        prevSelected,
        selected,
      )
      replaceQueryParams({
        [SubCategoryId]: stringifyFilterOption("value", selectedSubcategories),
        [SubCategory]: stringifyFilterOption("label", selectedSubcategories),
      })

      event({
        action: "product_searched",
        params: {
          subcategory: selectedSubcategories.map((c) => c.label).join(" | "),
        },
      })
    },
    [queryParamsAllInString, replaceQueryParams],
  )

  useEffect(() => {
    if (inView && !isLoading) {
      fetchNextPage()
    }
  }, [inView, isLoading, fetchNextPage])

  return (
    <div className="my-4">
      <FilterHeader
        title="Subcategory"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
        className={cn(isOpen && "!mb-0", "!mb-4")}
      />
      <div
        className={cn(
          "scrollbar-hide flex flex-col gap-3 overflow-y-auto",
          "transition-all duration-500 ease-out",
          isOpen
            ? "max-h-[360px] translate-y-0 opacity-100"
            : "max-h-0 -translate-y-2 opacity-0",
        )}
      >
        {subCategories && subCategories?.length > 0 ? (
          subCategories?.map((category) => (
            <CheckBox
              key={category.id}
              checked={isSelectedSubCategory(category)}
              onChange={() => handleCheckboxChange(category)}
              label={category.name as string}
            />
          ))
        ) : (
          <Text size={"base"} type={"medium"} state={"disabled"}>
            No Subcategory Available
          </Text>
        )}
        {hasNextPage && (
          <div ref={ref} className="flex justify-center">
            <Spinner />
          </div>
        )}
      </div>
      <Divider orientation="horizontal" className={cn(isOpen && "!mt-4")} />
    </div>
  )
}

export default FilterSubcategory
