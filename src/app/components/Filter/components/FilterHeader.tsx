import React from "react"
import { IconArrowDownOutline } from "@kickavenue/ui/components"
import { cn } from "@kickavenue/ui/lib/utils"

interface FilterHeaderProps {
  title: string
  isOpen: boolean
  toggleOpen: () => void
  className?: string
}

export const FilterHeader: React.FC<FilterHeaderProps> = ({
  title,
  isOpen,
  toggleOpen,
  className,
}) => {
  return (
    <div
      className={cn(
        "mb-3 flex cursor-pointer items-center justify-between",
        className,
      )}
      onClick={toggleOpen}
      role="button"
      tabIndex={0}
      onKeyPress={toggleOpen}
    >
      <div className="text-base font-bold">{title}</div>
      <IconArrowDownOutline
        className={cn(
          "cursor-pointer transition-transform duration-300",
          isOpen ? "rotate-180" : "rotate-0",
        )}
      />
    </div>
  )
}
