import { HTMLAttributes } from "react"
import { cx } from "class-variance-authority"
import Spinner from "@components/shared/Spinner/Spinner"

export interface SearchLoadingProps extends HTMLAttributes<HTMLDivElement> {}

const SearchLoading = ({ className, ...rest }: SearchLoadingProps) => {
  return (
    <div
      className={cx("flex h-[500px] items-center justify-center", className)}
      {...rest}
    >
      <Spinner />
    </div>
  )
}

export default SearchLoading
