import { IconSearchOutline, Text } from "@kickavenue/ui/components"
import { useCallback } from "react"
import useChangePage from "@app/hooks/useChangePage"
import ClickableDiv from "@components/shared/ClickableDiv"
import { ItemConstant } from "@constants/item"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { useMiscStore } from "stores/miscStore"

const Trending = () => {
  const { goToPage } = useChangePage()
  const { setShowExpandedSearch } = useMiscStore()
  const handleClickItem = useCallback(
    (keyword: string) => {
      setShowExpandedSearch(false)
      goToPage(PageRouteConstant.SEARCH, {
        [ItemConstant.FILTER_KEYS.KEYWORD]: keyword,
      })
    },
    [goToPage, setShowExpandedSearch],
  )
  const trendingItems = [
    "Nike x Travis Scott",
    "Air Jordan 1",
    "Yeezy 350",
    "Air Force 1",
    "Air Max 90",
    "Air Max 97",
  ]
  return (
    <div className="flex flex-col gap-base">
      <Text size="base" type="bold" state="primary">
        Trending
      </Text>

      <div className="grid grid-cols-2 gap-sm sm:gap-xl">
        {trendingItems.map((trending) => (
          <div key={trending} className="col-span-2 xs:col-span-1">
            <ClickableDiv
              className="flex items-center gap-xs"
              keyDownHandler={() => handleClickItem(trending)}
              onClick={() => handleClickItem(trending)}
            >
              <IconSearchOutline />
              <Text size="base" type="medium" state="primary">
                {trending}
              </Text>
            </ClickableDiv>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Trending
