import { Text } from "@kickavenue/ui/components"
import { HTMLAttributes } from "react"
import CenterWrapper from "@shared/CenterWrapper"
import Spinner from "@components/shared/Spinner/Spinner"

export interface SearchStatusProps extends HTMLAttributes<HTMLDivElement> {
  text?: string
  spinner?: boolean
}

const SearchStatus = ({ text, spinner, ...rest }: SearchStatusProps) => {
  const renderContent = () => {
    if (spinner) {
      return <Spinner />
    }
    return (
      <Text size="sm" state="primary" type="regular">
        {text}
      </Text>
    )
  }
  return (
    <CenterWrapper {...rest}>
      <div className="col-span-4 md:col-span-12">{renderContent()}</div>
    </CenterWrapper>
  )
}

export default SearchStatus
