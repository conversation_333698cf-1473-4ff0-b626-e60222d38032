"use client"

import {
  IconCloseOutline,
  IconSearchOutline,
  Input,
} from "@kickavenue/ui/components"
import { FormEvent, useEffect, useMemo } from "react"
import useExpandedSearch from "@app/hooks/useExpandedSearch"
import InputCloseIcon from "@shared/InputCloseIcon"
import { useMiscStore } from "stores/miscStore"
import useBodyOverflow from "@app/hooks/useBodyOverflow"

import ExpandedSearchContent from "./ExpandedSearchContent"
import SearchResultPreview from "./SearchResultPreview"
import useRunningTextGetCurrent from "@components/HomeComponent/hooks/useRunningTextGetCurrent"
import { cn } from "@kickavenue/ui/lib/utils"
import { usePathname } from "next/navigation"

const ExpandedSearchUnified = () => {
  const pathname = usePathname()
  const isHomePage = useMemo(() => pathname === "/", [pathname])
  const { data: runningTextData } = useRunningTextGetCurrent()
  const {
    showExpandedSearch,
    showSearchResultPreview,
    setShowSearchResultPreview,
    handleClose,
    handleKeyDown,
  } = useExpandedSearch()
  useBodyOverflow(showSearchResultPreview)
  const { setSearchKeyword, searchKeyword } = useMiscStore()

  const renderContent = showSearchResultPreview ? (
    <SearchResultPreview />
  ) : (
    <ExpandedSearchContent />
  )

  const handleInputChange = (e: FormEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    setSearchKeyword(value)
  }

  useEffect(() => {
    setShowSearchResultPreview(searchKeyword.length > 3)
  }, [searchKeyword, setShowSearchResultPreview])

  // Responsive styles that combine both mobile fullscreen and desktop overlay
  const containerStyles = useMemo(() => {
    const hasRunningText =
      runningTextData?.content?.length && runningTextData.content.length > 0

    return cn(
      "absolute inset-0 overflow-y-auto sm:overflow-y-hidden",
      // Mobile fullscreen styles (default)
      "z-40 bg-white transition-all duration-[0.3s] ease-in-out",
      showExpandedSearch
        ? "translate-y-0 opacity-100"
        : "-translate-y-full opacity-0",
      // Desktop/tablet overlay styles (md and up)
      "md:z-20 min-h-screen md:bg-transparent md:duration-500",
      showExpandedSearch
        ? "md:pointer-events-auto md:translate-y-[65px] md:opacity-100"
        : "md:pointer-events-none md:-translate-y-full md:opacity-0",
      showExpandedSearch &&
        hasRunningText &&
        isHomePage &&
        "md:translate-y-[117px]",
    )
  }, [showExpandedSearch, runningTextData, isHomePage])

  const backdropStyles = useMemo(() => {
    return cn(
      // Hidden on mobile, visible on desktop/tablet
      "hidden md:block size-full h-[50vh] bg-black-dim-40",
      "transition-all duration-300",
      showExpandedSearch ? "opacity-100" : "opacity-0",
    )
  }, [showExpandedSearch])

  return (
    <div className={containerStyles}>
      {/* Mobile search header - only visible on mobile */}
      <div className="sticky top-0 z-20 flex items-center gap-sm bg-white p-base md:hidden">
        <Input
          leftIcon={<IconSearchOutline />}
          rightIcon={
            <InputCloseIcon
              text={searchKeyword}
              onClick={() => setSearchKeyword("")}
            />
          }
          size="sm"
          containerInputProps={{
            className: "!bg-gray-w-95 !border-none",
          }}
          onChange={handleInputChange}
          value={searchKeyword}
        />
        <IconCloseOutline width={24} height={24} onClick={handleClose} />
      </div>

      {/* Content area */}
      {renderContent}

      {/* Desktop/tablet backdrop - only visible on md and up */}
      <div
        className={backdropStyles}
        onClick={handleClose}
        onFocus={handleClose}
        onKeyDown={handleKeyDown}
        aria-label="Close expanded search"
      />
    </div>
  )
}

export default ExpandedSearchUnified
