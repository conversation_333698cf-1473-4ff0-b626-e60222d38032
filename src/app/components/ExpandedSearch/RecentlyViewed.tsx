import { Text } from "@kickavenue/ui/components"
import ProductImage from "@kickavenue/ui/components/ProductImage"
import { getSliderImages } from "@utils/productImageSlider"

const RecentlyViewed = () => {
  const recentlyViewed = [
    "Nike x Travis Scott Nike x <PERSON> Scott",
    "Air Jordan 1",
    "Yeezy 350",
    "Air Force 1",
    "Air Max 90",
    "Air Max 97",
  ]

  return (
    <div className="flex w-full flex-col gap-base">
      <Text size="base" type="bold" state="primary">
        Recently Viewed
      </Text>

      <div className="grid grid-cols-2 gap-lg xs:grid-cols-4 sm:grid-cols-6 md:grid-cols-8 xl:grid-cols-12">
        {recentlyViewed.map((recentlyViewed) => (
          <div key={recentlyViewed} className="col-span-1 flex flex-col gap-xs">
            <ProductImage
              imageProps={{
                width: 82,
                height: 82,
                src: getSliderImages()[0],
                alt: recentlyViewed,
              }}
            />
            <Text
              size="sm"
              type="regular"
              state="primary"
              className="line-clamp-2 truncate"
            >
              {recentlyViewed}
            </Text>
          </div>
        ))}
      </div>
    </div>
  )
}

export default RecentlyViewed
