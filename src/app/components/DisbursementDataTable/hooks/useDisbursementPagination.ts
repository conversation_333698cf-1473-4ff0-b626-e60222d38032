import { useGetQueryParams } from "@app/hooks/useGetQueryParams"
import { MiscConstant } from "@constants/misc"
import { updateUrlSearchParams } from "@utils/url.utils"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS

const useDisbursementPagination = (totalPages: number = 1) => {
  const { page, pageSize } = useGetQueryParams()

  const handleNext = () => {
    const nextPage = page + 1
    updateUrlSearchParams(PAGE, nextPage.toString())
  }

  const handlePrev = () => {
    const prevPage = page - 1
    updateUrlSearchParams(PAGE, prevPage.toString())
  }

  const handlePage = (page: number) => {
    updateUrlSearchParams(PAGE, (page - 1).toString())
  }

  const handlePerPage = (size: number) => {
    updateUrlSearchParams(PAGE_SIZE, size.toString())
    // Reset to first page when changing page size
    updateUrlSearchParams(PAGE, "0")
  }

  return {
    currentPage: page + 1,
    pageSize,
    totalPages: totalPages || 1,
    handleNext,
    handlePrev,
    handlePage,
    handlePerPage,
  }
}

export default useDisbursementPagination
