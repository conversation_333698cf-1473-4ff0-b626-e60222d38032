"use client"

import { Text } from "@kickavenue/ui/components"
import useToast from "@app/hooks/useToast"
import {
  EKickPointDisbursementType,
  ESellerCreditDisbursementType,
} from "types/disbursement.type"
import { capitalizeWords } from "@utils/misc"
import useGetMyDisbursementById from "@app/hooks/useGetMyDisbursementById"

import InfoRow from "./InfoRow"

const TransactionInfo = () => {
  const { disbursementData, disbursementType } = useGetMyDisbursementById()
  const transactionType = capitalizeWords(disbursementData?.actionName || "")
  const { setShowToast } = useToast()

  const handleCopy = async (value: string, message: string) => {
    await navigator.clipboard.writeText(value)
    setShowToast(true, message)
  }

  const isHasOrderId =
    disbursementType === EKickPointDisbursementType.KickPointLog ||
    disbursementType === ESellerCreditDisbursementType.SellerPendingCashOut ||
    disbursementType === ESellerCreditDisbursementType.SellerCreditLog

  return (
    <div className="flex flex-col gap-sm">
      <Text size="base" state="primary" type="bold">
        {transactionType} Info
      </Text>
      <div className="flex flex-col gap-2 rounded-sm border border-gray-w-80 p-4">
        <InfoRow
          label="Disbursement Number"
          value={disbursementData?.disbursementNumber || "-"}
          showIcon
          onClickIcon={(value) =>
            handleCopy(value, "Disbursement Number successfully copied!")
          }
          typeValue="bold"
        />
        {isHasOrderId && (
          <InfoRow
            label="Invoice Number"
            value={disbursementData?.invoiceNumber || "-"}
            showIcon
            onClickIcon={(value) =>
              handleCopy(value, "Invoice Number successfully copied!")
            }
            typeValue="bold"
          />
        )}
        <InfoRow label="Disbursement Type" value={transactionType} />
      </div>
    </div>
  )
}

export default TransactionInfo
