"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@kickavenue/ui/components"
import Link from "next/link"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import {
  EDisbursementType,
  EKickCreditDisbursementType,
  EKickPointDisbursementType,
  ESellerCreditDisbursementType,
} from "types/disbursement.type"
import useGetMyDisbursementById from "@app/hooks/useGetMyDisbursementById"
import Spinner from "@components/shared/Spinner"

import TransactionHeader from "./TransactionHeader"
import TransactionInfo from "./TransactionInfo"
import TransactionSummary from "./TransactionSummary"
import CashOutInfo from "./CashOutInfo"

const { DISBURSEMENT_DETAIL } = ModalConstant.MODAL_IDS

const getDisbursementTitle = (type?: EDisbursementType) => {
  switch (type) {
    case EKickCreditDisbursementType.KickPendingTopUp:
      return "Pending Top Up Detail"
    case EKickCreditDisbursementType.KickPendingCashOut:
    case ESellerCreditDisbursementType.SellerPendingCashOut:
      return "Pending Cash Out Detail"
    case EKickCreditDisbursementType.KickCreditLog:
      return "Kick Credit Log"
    case EKickPointDisbursementType.KickPointLog:
      return "Kick Point Log"
    case ESellerCreditDisbursementType.SellerCreditLog:
      return "Seller Credit log"
    default:
      return "Disbursement Details"
  }
}

const DisbursementDetailModal = () => {
  const { disbursementData, isLoading, disbursementType } =
    useGetMyDisbursementById()

  const { setOpen } = useModalStore()

  const handleClose = () => {
    setOpen(false, DISBURSEMENT_DETAIL)
  }

  const isCashOut =
    disbursementType === EKickCreditDisbursementType.KickPendingCashOut ||
    disbursementType === ESellerCreditDisbursementType.SellerPendingCashOut

  const isKickPendingTopUp =
    disbursementType === EKickCreditDisbursementType.KickPendingTopUp

  return (
    <Modal modalId={DISBURSEMENT_DETAIL}>
      <HeaderModal
        title={getDisbursementTitle(disbursementType)}
        onClose={handleClose}
      />

      {isLoading && (
        <div className="flex min-h-80 w-full items-center justify-center">
          <Spinner />
        </div>
      )}

      {disbursementData && !isLoading && (
        <div className="flex max-h-fit flex-col gap-md overflow-y-auto p-md">
          <TransactionHeader />
          <Divider orientation="horizontal" />
          <TransactionInfo />
          <Divider orientation="horizontal" />
          <TransactionSummary />
          {isCashOut && (
            <>
              <Divider orientation="horizontal" />
              <CashOutInfo />
            </>
          )}

          {isKickPendingTopUp && (
            <Link
              href={`/topup/status/${disbursementData?.topup?.id || 0}/pending`}
              className="w-full"
            >
              <Button size="lg" variant="primary" className="!w-full">
                Complete Top Up
              </Button>
            </Link>
          )}
        </div>
      )}
    </Modal>
  )
}

export default DisbursementDetailModal
