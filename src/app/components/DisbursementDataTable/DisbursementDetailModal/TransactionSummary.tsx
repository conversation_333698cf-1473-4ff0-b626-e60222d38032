"use client"

import { Text } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { capitalizeWords, formatPrice } from "@utils/misc"
import {
  EDisbursementActionNameType,
  EKickCreditDisbursementType,
} from "types/disbursement.type"
import useGetMyDisbursementById from "@app/hooks/useGetMyDisbursementById"
import { EBalanceType } from "types/balance.type"

import InfoRow from "./InfoRow"

const TransactionSummary = () => {
  const { disbursementData, disbursementType } = useGetMyDisbursementById()

  const transactionType = capitalizeWords(disbursementData?.actionName || "")

  const isKickCreditTopUp =
    disbursementData?.balanceType === EBalanceType.KickCredit &&
    disbursementData?.actionName === EDisbursementActionNameType.TopUp

  const isSellerCreditCashOut =
    disbursementData?.balanceType === EBalanceType.SellerCredit &&
    disbursementData?.actionName === EDisbursementActionNameType.CashOut

  const isHasFee = isKickCreditTopUp || isSellerCreditCashOut

  const isKickDisbursement =
    disbursementType === EKickCreditDisbursementType.KickPendingTopUp ||
    disbursementType === EKickCreditDisbursementType.KickCreditLog

  const amount = disbursementData?.amount || 0
  const fee = disbursementData?.fee || 0

  const chosenCurrency =
    disbursementData?.balanceType === EBalanceType.KickPoint ? "KP" : "IDR"
  const amountFormatted = formatPrice(amount, null, chosenCurrency)

  const totalAmount = () => {
    if (isHasFee && isKickCreditTopUp) {
      return formatPrice(amount + fee, null, chosenCurrency)
    }

    if (isHasFee && isSellerCreditCashOut) {
      return formatPrice(amount - fee, null, chosenCurrency)
    }

    return amountFormatted
  }

  return (
    <div className="flex flex-col">
      <Text size="base" state="primary" type="bold" className="mb-sm">
        {transactionType} Summary
      </Text>
      <div className="flex flex-col gap-2 rounded-t-sm border border-b-0 border-gray-w-80 p-sm">
        <InfoRow label={`${transactionType} Amount`} value={amountFormatted} />
        {isHasFee && (
          <InfoRow
            label={`${transactionType} Fee`}
            value={formatPrice(fee, null, isKickDisbursement ? "IDR" : "-IDR")}
            classNameValue={cx(!isKickDisbursement && "!text-danger")}
          />
        )}
      </div>
      <div className="flex flex-col gap-2 rounded-b-sm border border-gray-w-80 p-sm">
        <InfoRow
          label="Total Amount"
          value={totalAmount()}
          typeLabel="bold"
          typeValue="bold"
        />
      </div>
    </div>
  )
}

export default TransactionSummary
