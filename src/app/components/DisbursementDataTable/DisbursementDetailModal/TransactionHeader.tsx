"use client"

import { Text, Badge } from "@kickavenue/ui/components"
import useGetMyDisbursementById from "@app/hooks/useGetMyDisbursementById"
import { capitalizeWords, formatDateWIB } from "@utils/misc"
import { EDisbursementStatusesType } from "types/disbursement.type"

const TransactionHeader = () => {
  const { disbursementData } = useGetMyDisbursementById()

  const getType = () => {
    switch (disbursementData?.status) {
      case EDisbursementStatusesType.Completed:
        return "positive"
      case EDisbursementStatusesType.Pending:
        return "warning"
      case EDisbursementStatusesType.Failed:
      case EDisbursementStatusesType.Canceled:
      case EDisbursementStatusesType.Unverified:
      case EDisbursementStatusesType.Rejected:
        return "negative"
      case EDisbursementStatusesType.InProgress:
        return "information"
      default:
        return "neutral"
    }
  }

  return (
    <div className="flex items-center justify-between gap-base">
      <div className="flex flex-col gap-1">
        <Text size="base" state="primary" type="bold">
          {capitalizeWords(disbursementData?.actionName || "")} Status
        </Text>
        <Text size="sm" state="primary" type="regular">
          {formatDateWIB(disbursementData?.createdAt)}
        </Text>
      </div>
      <Badge
        text={capitalizeWords(disbursementData?.status || "")}
        type={getType()}
      />
    </div>
  )
}

export default TransactionHeader
