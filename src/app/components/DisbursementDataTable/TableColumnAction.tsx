import {
  DropdownItemProps,
  IconKebabMenuVertical,
  IconViewDetailsOutline,
} from "@kickavenue/ui/dist/src/components"
import DropdownDynamicChild from "@shared/Form/DropdownDynamicChild"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import {
  EKickCreditDisbursementType,
  EKickPointDisbursementType,
  ESellerCreditDisbursementType,
  GetMyDisbursementData,
} from "types/disbursement.type"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"

const { DISBURSEMENT_DETAIL } = ModalConstant.MODAL_IDS

interface Props {
  data: GetMyDisbursementData
  disbursementType?:
    | EKickCreditDisbursementType
    | EKickPointDisbursementType
    | ESellerCreditDisbursementType
}

const DisbursementTableColumnAction = ({ data, disbursementType }: Props) => {
  const replaceQueryParams = useReplaceQueryParams()
  const { setOpen } = useModalStore()

  const handleViewDetail = () => {
    replaceQueryParams({
      disbursementId: data.id,
      disbursementType,
    })

    setTimeout(() => {
      setOpen(true, DISBURSEMENT_DETAIL)
    }, 500)
  }

  const handleOnItemSelect = (option: DropdownItemProps) => {
    switch (option.value) {
      case "view-details":
        handleViewDetail()
        break
      default:
    }
  }

  return (
    <div className="flex justify-center gap-xs">
      <DropdownDynamicChild
        options={[
          {
            text: "View Details",
            value: "view-details",
            iconLeading: <IconViewDetailsOutline width={16} height={16} />,
          },
        ]}
        placement="rightBottom"
        onItemSelect={handleOnItemSelect}
      >
        <IconKebabMenuVertical />
      </DropdownDynamicChild>
    </div>
  )
}

export default DisbursementTableColumnAction
