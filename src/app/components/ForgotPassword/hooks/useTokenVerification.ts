import { useState, useEffect, useCallback } from "react"
import { AuthUseCase } from "@application/usecases/AuthUseCase"
import { ConcreteAuthRepository } from "@infrastructure/repositories/ConcreteAuthRepository"
import useToast from "@app/hooks/useToast"

export const useTokenVerification = (
  token: string | null,
  emailParam: string | null,
) => {
  const [isTokenVerified, setIsTokenVerified] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { setShowToast } = useToast()

  const verifyToken = useCallback(
    async (token: string, email: string) => {
      setIsLoading(true)

      const authRepository = new ConcreteAuthRepository()
      const authUseCase = new AuthUseCase(authRepository)

      try {
        const encodedEmail = encodeURIComponent(email)
        await authUseCase.verifyResetToken(encodedEmail, token)
        setIsTokenVerified(true)
      } catch (err) {
        if (err instanceof Error && err.message === "Token is invalid") {
          setIsError(true)
          setIsTokenVerified(false)
        }
        setShowToast(
          true,
          err instanceof Error
            ? err.message
            : "An error occurred while verifying the token",
          "danger",
        )
      } finally {
        setIsLoading(false)
      }
    },
    [setShowToast],
  )

  useEffect(() => {
    if (token && emailParam) {
      verifyToken(token, emailParam)
    }
  }, [token, emailParam, verifyToken])

  return { isTokenVerified, isLoading, isError }
}
