import { useEffect, useState } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@kickavenue/ui/components"
import { usePasswordInput } from "@components/InputPassword/hooks/usePasswordInput"
import {
  PasswordField,
  RequirementsDisplay,
} from "@components/InputPassword/components/PasswordField"

import { useForgotPasswordForm } from "./hooks/useForgotPasswordForm"
import { useTokenVerification } from "./hooks/useTokenVerification"
import { usePasswordReset } from "./hooks/usePasswordReset"

const Form = () => {
  const searchParams = useSearchParams()
  const token = searchParams?.get("token") || null
  const emailParam = searchParams?.get("email") || null
  const { email } = useForgotPasswordForm(emailParam)
  const [password, setPassword] = useState("")
  const [passwordConfirmation, setPasswordConfirmation] = useState("")

  const {
    isTokenVerified,
    isLoading: tokenLoading,
    isError: isTokenError,
  } = useTokenVerification(token, emailParam)
  const { resetPassword, isLoading } = usePasswordReset()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isTokenVerified) {
      await resetPassword(email, password, passwordConfirmation, token ?? "")
    }
  }

  const {
    isPasswordVisible,
    isConfirmationVisible,
    validationResult,
    handleChange,
    handleConfirmationChange,
    togglePasswordVisibility,
    toggleConfirmationVisibility,
    passwordVariant,
    confirmationVariant,
    passwordHelperText,
    confirmationHelperText,
    setFocusedInput,
  } = usePasswordInput({
    value: password,
    onChange: setPassword,
    confirmationValue: passwordConfirmation,
    onConfirmationChange: setPasswordConfirmation,
    showRequirements: true,
  })

  const router = useRouter()
  useEffect(() => {
    if (isTokenError && !isTokenVerified) {
      router.push("/forgot-password/token-expired")
    }
  }, [isTokenError, isTokenVerified, router])

  return (
    <>
      <form onSubmit={handleSubmit}>
        <PasswordField
          label="Password"
          value={password}
          isVisible={isPasswordVisible}
          onChange={handleChange}
          onFocus={() => setFocusedInput("password")}
          toggleVisibility={togglePasswordVisibility}
          helperText={passwordHelperText}
          variant={
            passwordVariant as "success" | "warning" | "danger" | undefined
          }
          placeholder="Password"
        />
        <RequirementsDisplay shouldShow validationResult={validationResult} />
        <PasswordField
          label="Password Confirmation"
          value={passwordConfirmation || ""}
          isVisible={isConfirmationVisible}
          onChange={handleConfirmationChange}
          onFocus={() => setFocusedInput("confirmation")}
          toggleVisibility={toggleConfirmationVisibility}
          helperText={confirmationHelperText}
          variant={
            confirmationVariant as "success" | "warning" | "danger" | undefined
          }
          placeholder="Confirm Password"
          className="mt-4"
        />
        <div className="mt-8 w-full">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            type="submit"
            disabled={
              isLoading ||
              tokenLoading ||
              !validationResult.isValid ||
              password !== passwordConfirmation
            }
          >
            Reset Password
          </Button>
        </div>
      </form>
    </>
  )
}

export default Form
