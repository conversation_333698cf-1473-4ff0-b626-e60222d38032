"use client"

import React from "react"
import Divider from "@kickavenue/ui/components/Divider"
import { useMiscStore } from "stores/miscStore"

import CenterWrapper from "../shared/CenterWrapper"

import FooterLinkSection from "./FooterLinkSection"
import FooterPaymentSection from "./FooterPaymentSection"

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const { showFooter } = useMiscStore()

  if (!showFooter) return null

  return (
    <footer className="min-h-[200px]">
      {" "}
      {/* Added minimum height */}
      <Divider type="solid" orientation="horizontal" />
      <CenterWrapper className="mt-lg">
        <FooterLinkSection />
      </CenterWrapper>
      <CenterWrapper>
        <FooterPaymentSection />
      </CenterWrapper>
      <Divider type="solid" orientation="horizontal" />
      <div className="flex justify-center py-lg">
        <p className="m-0 text-sm text-gray">
          &copy; {currentYear} Kick Avenue. All Rights Reserved.{" "}
        </p>
      </div>
    </footer>
  )
}

export default Footer
