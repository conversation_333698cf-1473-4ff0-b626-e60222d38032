import {
	IconVisa,
	IconBca,
	IconPermataBank,
	IconMandiri,
	IconKredivo,
	IconGopay,
	IconGosend,
	IconMasterCard,
	IconBRI,
	IconAtome,
	IconAkulaku,
	IconJneExpress,
	IconPaxel,
	IconJntExpress,
	IconPosIndonesia,
	IconBni,
} from "@kickavenue/ui/components/icons"
import { PageRouteConstant } from "@constants/pageRoute.constant"

export interface FooterTextLink {
  text: string
  url: string
  arrowUpIcon?: boolean
}

export interface FooterImageLink {
  url: string
  DataImage: React.ComponentType
}

export function getAboutTextLink(): FooterTextLink[] {
  return [
    { text: "Our Story", url: "" },
    { text: "Blog", url: "" },
    { text: "Career", url: "" },
    { text: "Privacy Policy", url: "" },
    { text: "Terms & Condition", url: PageRouteConstant.TERMS_CONDITIONS },
  ]
}

export function getHelpTextLink(): FooterTextLink[] {
  return [
    { text: "Help Center", url: PageRouteConstant.HELP_CENTER },
    { text: "Contact Us", url: "" },
    { text: "FAQ", url: "" },
    { text: "Buyer Guide", url: "" },
    { text: "Seller Guide", url: "" },
  ]
}

export function getSocialMediaTextLink(): FooterTextLink[] {
  return [
    { text: "Instagram", url: "", arrowUpIcon: true },
    { text: "Tiktok", url: "", arrowUpIcon: true },
    { text: "Facebook", url: "", arrowUpIcon: true },
    { text: "Youtube", url: "", arrowUpIcon: true },
  ]
}

export function getPaymentProviderList(): FooterImageLink[] {
  return [
    { url: "", DataImage: IconVisa },
    { url: "", DataImage: IconMasterCard },
    { url: "", DataImage: IconBca },
    { url: "", DataImage: IconBRI },
    { url: "", DataImage: IconBni },
    { url: "", DataImage: IconPermataBank },
    { url: "", DataImage: IconMandiri },
    { url: "", DataImage: IconKredivo },
    { url: "", DataImage: IconGopay },
    { url: "", DataImage: IconAtome },
    { url: "", DataImage: IconAkulaku },
  ]
}

export function getDeliveryProviderList(): FooterImageLink[] {
  return [
    { url: "", DataImage: IconJneExpress },
    { url: "", DataImage: IconJntExpress },
    { url: "", DataImage: IconPaxel },
    { url: "", DataImage: IconGosend },
    { url: "", DataImage: IconPosIndonesia },
  ]
}
