import { Button, Text } from "@kickavenue/ui/dist/src/components"
import useExpandableText from "@app/hooks/useExpandableText"
import { MiscConstant } from "@constants/misc"

const FooterAuthenticSection = () => {
  const { getRenderAction, getRenderText, toggleExpand } = useExpandableText(
    MiscConstant.FOOTER_AUTHENTIC_CONTENT,
    137,
  )
  return (
    <>
      <Text size="sm" state="secondary" type="regular">
        {getRenderText()}
      </Text>
      <Button
        style={{ padding: 0 }}
        variant="link"
        size="md"
        onClick={() => toggleExpand()}
      >
        {getRenderAction()}
      </Button>
    </>
  )
}

export default FooterAuthenticSection
