"use client"

import { ReactNode } from "react"
import { useAuthRedirect } from "@app/hooks/useAuthRedirect"
import Loading from "@components/shared/Loading"

interface AuthRedirectWrapperProps {
  children: ReactNode
  whenAuthed?: string
  whenUnauthed?: string
}

export const AuthRedirectWrapper = ({
  children,
  whenAuthed,
  whenUnauthed,
}: AuthRedirectWrapperProps) => {
  const { loading } = useAuthRedirect({ whenAuthed, whenUnauthed })

  if (loading) {
    return <Loading />
  }

  return <>{children}</>
}
