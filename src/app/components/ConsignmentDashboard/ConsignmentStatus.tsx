import { Text } from "@kickavenue/ui/dist/src/components"
import {
  getConsignmentPendingStatus,
  getSellerConsignmentStatusMap,
} from "@utils/sellerConsignment.utils"
import { TListingItem } from "types/listingItem.type"
import { TSellerConsignment } from "types/sellerConsignment.type"

const ConsignmentStatus = ({
  listing,
  sellerConsignment,
}: {
  listing: TListingItem
  sellerConsignment: TSellerConsignment
}) => {
  const status = getConsignmentPendingStatus({
    sellerListingStock: listing,
    sellerConsignment,
  })

  const map = getSellerConsignmentStatusMap(status)
  return (
    <Text size="sm" state={map?.textState} type="regular">
      {map?.text}
    </Text>
  )
}

export default ConsignmentStatus
