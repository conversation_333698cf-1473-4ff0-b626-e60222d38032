import { Space } from "@kickavenue/ui/dist/src/components"
import Table from "@shared/Table"
import TablePagination from "@shared/Table/TablePagination"
import { useConsignmentPendingStore } from "stores/consignmentPendingStore"
import { camelToSnake } from "@utils/misc"
import { TSorter } from "types/table.type"
import useURLQuery from "@app/hooks/useUrlQuery"
import { ListingItemConstant } from "@constants/listingitem.constant"
import SpinnerLoading from "@components/shared/SpinnerLoading"

import styles from "./ConsignmentPendingTable.module.scss"
import useConsignmentPendingTable from "./hook/useConsignmentPendingTable"
import useFetchConsignmentPending from "./hook/useFetchConsignmentPending"
import useConsignmentPendingPagination from "./hook/useConsignmentPendingPagination"

const { SORT } = ListingItemConstant.FILTER_FIELDS

const ConsignmentPendingTable = () => {
  const { columns } = useConsignmentPendingTable()
  const { isLoading } = useFetchConsignmentPending()
  const { handleChangeQuery } = useURLQuery()
  const { consignmentPendingData } = useConsignmentPendingStore()
  const paging = useConsignmentPendingPagination()
  const handleTableChange = (sorter: TSorter) => {
    const field = camelToSnake(sorter.field)?.toLowerCase()
    handleChangeQuery(SORT, `${field},${sorter.order}`)
  }

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(80vh-250px)]" />
  }

  return (
    <>
      <div className="max-h-[580px] min-h-[calc(80vh-260px)] max-w-full overflow-auto">
        <Table
          columns={columns}
          dataSource={consignmentPendingData}
          rowKey="id"
          className={styles.table}
          loading={isLoading}
          onTableChange={handleTableChange}
        />
      </div>
      <Space size="lg" type="margin" direction="y" />
      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default ConsignmentPendingTable
