import { Text } from "@kickavenue/ui/dist/src/components"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import { TTableColumn } from "types/table.type"
import ConditionColumn from "@components/shared/ConditionColumn"
import PendingTableRowActions from "@components/ConsignmentDashboard/ConsignmentPending/PendingTableRowActions"
import { formatCurrencyStripe } from "@utils/misc"
import { TConsignmentSellerStockResponse } from "types/sellerConsignment.type"
import ConsignmentStatus from "@components/ConsignmentDashboard/ConsignmentStatus"
import { getUsSize } from "@utils/size.utils"
import classes from '../ConsignmentPendingTable.module.scss'
import { cx } from "class-variance-authority"

const columnWidth = {
	consignmentId: 150,
	productDetails: 300,
	size: 64,
	conditions: 108,
	listingPrice: 148,
	status: 128,
	actions: 67,
}

const useConsignmentPendingTable = () => {
	const columns = [
		{
			key: "consignmentId",
			title: "Consignment ID",
			width: columnWidth.consignmentId,
			headerClassName: `${classes['sticky-header-1']} !z-[11]`,
			contentClassName: classes['sticky-content-1'],
			render: (record: TConsignmentSellerStockResponse) => {
				return (
					<Text size="sm" state="primary" type="regular">
						{record.sellerListingStock?.consignmentId}
					</Text>
				)
			},
			sorter: () => { },
		},
		{
			key: "itemName",
			title: "Product Details",
			width: columnWidth.productDetails,
			headerClassName: `${classes['sticky-header-2']} !z-[11]`,
			contentClassName: classes['sticky-content-2'],
			render: (record: TConsignmentSellerStockResponse) => {
				return <ProductDetailColumn listingItem={record?.sellerListingStock} />
			},
			sorter: () => { },
		},
		{
			key: "sizeUs",
			dataIndex: "size",
			title: "Size",
			width: columnWidth.size,
			render: (record: TConsignmentSellerStockResponse) =>
				getUsSize(record?.sellerListingStock?.size),
			sorter: () => { },
		},
		{
			key: "condition",
			title: "Conditions",
			width: columnWidth.conditions,
			render: (record: TConsignmentSellerStockResponse) => (
				<ConditionColumn
					itemCondition={record?.sellerListingStock?.itemCondition}
					packagingCondition={record?.sellerListingStock?.packagingCondition}
				/>
			),
			sorter: () => { },
		},
		{
			key: "sellingPrice",
			title: "Price",
			width: columnWidth.listingPrice,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (record: TConsignmentSellerStockResponse) => {
				const sellingPrice = record?.sellerListingStock?.sellingPrice
				if (!sellingPrice || !sellingPrice?.minUnitVal) return "-"

				return formatCurrencyStripe({ price: sellingPrice })
			},
		},
		{
			key: "status",
			title: "Status",
			width: columnWidth.status,
			render: (record: TConsignmentSellerStockResponse) => {
				return (
					<ConsignmentStatus
						listing={record?.sellerListingStock}
						sellerConsignment={record?.sellerConsignment}
					/>
				)
			},
		},
		{
			key: "actions",
			title: "Actions",
			width: columnWidth.actions,
			headerClassName: `${classes['sticky-right-header-1']} !z-[11] [&>div]:!justify-center`,
			contentClassName: cx(classes['sticky-right-content-1'], "dropdown-options"),
			render: (record: TConsignmentSellerStockResponse) => (
				<PendingTableRowActions record={record} />
			),
		},
	] as TTableColumn[]
	return {
		columns,
	}
}

export default useConsignmentPendingTable
