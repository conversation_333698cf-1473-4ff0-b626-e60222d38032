import { useQuery } from "@tanstack/react-query"
import { useConsignmentPendingStore } from "stores/consignmentPendingStore"
import { ConsignmentDashboardTabEnum } from "types/listingItem.type"
import { isEmpty } from "@utils/misc"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"
import { GetAllSellerStock } from "@application/usecases/getAllSellerStock"
import {
  TConsignmentSellerStockResponse,
  TSellerConsignmentFilter,
} from "types/sellerConsignment.type"
import useURLQuery from "@app/hooks/useUrlQuery"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

const { FILTER_FIELDS } = ListingItemConstant

const useFetchConsignmentPending = () => {
  const {
    setConsignmentPendingData,
    setConsignmentPendingFilter,
    consignmentPendingFilter,
  } = useConsignmentPendingStore()
  const { getSearchParam } = useURLQuery()

  const tab = getSearchParam(
    FILTER_FIELDS.TAB,
    ConsignmentDashboardTabEnum.Pending,
  )

  const fetchConsignmentPending = async () => {
    const r = new SellerConsignmentApiRepository()
    const u = new GetAllSellerStock(r)
    const res = await u.execute(
      consignmentPendingFilter as TSellerConsignmentFilter,
    )
    setConsignmentPendingData(res?.content as TConsignmentSellerStockResponse[])
    setConsignmentPendingFilter({
      ...(consignmentPendingFilter as TSellerConsignmentFilter),
      totalPages: res?.totalPages,
      pageSize: res?.pageSize,
    })
  }

  const enabled =
    tab === ConsignmentDashboardTabEnum.Pending &&
    !isEmpty(consignmentPendingFilter?.page) &&
    !isEmpty(consignmentPendingFilter?.pageSize)

  const consignmentPending = useQuery({
    queryKey: [
      QueryKeysConstant.GET_SELLER_STOCKS,
      consignmentPendingFilter?.page,
      consignmentPendingFilter?.pageSize,
      consignmentPendingFilter?.sort,
      consignmentPendingFilter?.status,
      consignmentPendingFilter?.sellingPriceFrom,
      consignmentPendingFilter?.sellingPriceTo,
      consignmentPendingFilter?.isNewNoDefect,
      consignmentPendingFilter?.itemCondition,
      consignmentPendingFilter?.categoryID,
      consignmentPendingFilter?.brandID,
      consignmentPendingFilter?.sizeID,
      consignmentPendingFilter?.search,
    ],
    queryFn: fetchConsignmentPending,
    staleTime: 1,
    enabled,
  })

  return {
    data: consignmentPending?.data,
    isLoading: consignmentPending?.isLoading,
    error: consignmentPending?.error,
  }
}

export default useFetchConsignmentPending
