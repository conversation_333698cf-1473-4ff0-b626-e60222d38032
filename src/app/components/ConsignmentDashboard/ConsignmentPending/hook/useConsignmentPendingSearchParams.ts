import { useSearchParams } from "next/navigation"
import { useCallback, useEffect } from "react"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { useConsignmentPendingStore } from "stores/consignmentPendingStore"
import { MiscConstant } from "@constants/misc"
import { ConsignmentDashboardTabEnum } from "types/listingItem.type"
import { joinArrayToString } from "@utils/misc"
import { enumerizeAndMapStatus } from "@utils/sellerConsignment.utils"
import { getFilterFromQuery } from "@utils/query.utils"
import { getConditionFilter } from "@utils/listingItem"
import { TSellerConsignmentFilter } from "types/sellerConsignment.type"
import { stringToNumberArray, stringToStringArray } from "@utils/string.utils"

const {
  PAGE,
  PAGE_SIZE,
  TAB,
  STATUS,
  PRICE_MIN,
  PRICE_MAX,
  CONDITION,
  CATEGORY_ID,
  BRAND_ID,
  SIZE_ID,
  SEARCH,
  SORT,
} = ListingItemConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const useConsignmentPendingSearchParams = () => {
  const searchParams = useSearchParams()
  const { setConsignmentPendingFilter } = useConsignmentPendingStore()

  const mapStatusQuery = useCallback((status?: string | null) => {
    if (!status) return undefined
    if (status.includes(",")) {
      const qStatus = status.split(",")
      const statusEnum = enumerizeAndMapStatus(qStatus)
      return joinArrayToString(statusEnum as string[])
    }
    return joinArrayToString(enumerizeAndMapStatus(status) as string[])
  }, [])

  useEffect(() => {
    const tab = searchParams?.get(TAB) || ConsignmentDashboardTabEnum.Pending
    if (
      tab !== ConsignmentDashboardTabEnum.Pending ||
      !searchParams?.toString()
    ) {
      return
    }
    const status = mapStatusQuery(searchParams?.get(STATUS))
    const page = searchParams?.get(PAGE)
    const pageSize = searchParams?.get(PAGE_SIZE)
    const priceMin = searchParams?.get(PRICE_MIN)
    const priceMax = searchParams?.get(PRICE_MAX)
    const condition = searchParams?.get(CONDITION)
    const categoryId = searchParams?.get(CATEGORY_ID)
    const brandId = searchParams?.get(BRAND_ID)
    const sizeId = searchParams?.get(SIZE_ID)
    const search = searchParams?.get(SEARCH)
    const sortQuery = searchParams?.get(SORT)
    const filter = {
      page: page ? Number(page) : PAGE_DEFAULT,
      pageSize: pageSize ? Number(pageSize) : PAGE_SIZE_DEFAULT,
      totalPages: 0,
      sortBy: undefined,
      status: stringToStringArray(status),
      sort: sortQuery ? [sortQuery] : [],
      sellingPriceFrom: getFilterFromQuery(priceMin) as number,
      sellingPriceTo: getFilterFromQuery(priceMax) as number,
      categoryID: stringToNumberArray(categoryId),
      brandID: stringToNumberArray(brandId),
      sizeID: stringToNumberArray(sizeId),
      search,
      ...getConditionFilter(condition),
    } as TSellerConsignmentFilter
    setConsignmentPendingFilter(filter)
  }, [searchParams, setConsignmentPendingFilter, mapStatusQuery])
}

export default useConsignmentPendingSearchParams
