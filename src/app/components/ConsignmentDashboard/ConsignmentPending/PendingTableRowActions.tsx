import {
	DropdownItemProps,
	IconCloseOutline,
	IconInputAwbOutline,
	IconKebabMenuVertical,
	IconReceiveItemOutline,
	IconTrashOutline,
	IconViewDetailsOutline,
} from "@kickavenue/ui/components"
import { useCallback } from "react"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { TConsignmentPendingAction } from "types/consignmentPending.type"
import {
	ESellerConsignmentStatus,
	TConsignmentSellerStockResponse,
} from "types/sellerConsignment.type"
import { getConsignmentPendingStatus } from "@utils/sellerConsignment.utils"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"

const {
	DELETE_REQEUST_CONFIRM,
	CANCEL_REQUEST_CONFIRM,
	RECEIVE_ITEM_CONFIRM,
	CONSIGNMENT_DETAIL,
	CONSIGNMENT_INPUT_AWB,
} = ModalConstant.MODAL_IDS

const { ViewDetails, DeleteRequest, InputAwb, CancelRequest, ReceiveItem } =
	TConsignmentPendingAction

const {
	WaitingSellerDelivering,
	AwaitingReview,
	EnRouteToKickAvenue,
	VerificationFailed,
	EnRouteToSeller,
	QualityControlProcess,
	LegitCheckProcess,
	WaitingReturnAwbNumber,
	WaitingReturnCourier,
	WaitingReturnCourierAssignment,
} = ESellerConsignmentStatus

const STATUS_ACTIONS_MAP: Partial<
	Record<ESellerConsignmentStatus, TConsignmentPendingAction[]>
> = {
	[WaitingSellerDelivering]: [ViewDetails, InputAwb, CancelRequest],
	[AwaitingReview]: [ViewDetails, DeleteRequest],
	[EnRouteToKickAvenue]: [ViewDetails],
	[VerificationFailed]: [ViewDetails],
	[EnRouteToSeller]: [ViewDetails, ReceiveItem],
	[QualityControlProcess]: [ViewDetails],
	[LegitCheckProcess]: [ViewDetails],
	[WaitingReturnAwbNumber]: [ViewDetails],
	[WaitingReturnCourier]: [ViewDetails],
	[WaitingReturnCourierAssignment]: [ViewDetails],
}

const PendingTableRowActions = ({
	record,
}: {
	record: TConsignmentSellerStockResponse
}) => {
	const { setOpen } = useModalStore()
	const { setSellerStock } = useConsignmentModalStore()

	const handleOnItemSelect = (option: DropdownItemProps) => {
		setSellerStock(record)
		switch (option.value) {
			case DeleteRequest:
				setOpen(true, DELETE_REQEUST_CONFIRM)
				break
			case InputAwb:
				setOpen(true, CONSIGNMENT_INPUT_AWB)
				break
			case CancelRequest:
				setOpen(true, CANCEL_REQUEST_CONFIRM)
				break
			case ReceiveItem:
				setOpen(true, RECEIVE_ITEM_CONFIRM)
				break
			case ViewDetails:
				setOpen(true, CONSIGNMENT_DETAIL)
				break
			default:
		}
	}

	const isAllowedStatus = useCallback(
		(action: TConsignmentPendingAction) =>
			STATUS_ACTIONS_MAP[
				getConsignmentPendingStatus(record) as ESellerConsignmentStatus
			]?.includes(action),
		[record],
	)

	const options = [
		{
			text: "View Details",
			value: ViewDetails,
			iconLeading: <IconViewDetailsOutline width={16} height={16} />,
			show: () => isAllowedStatus(ViewDetails),
		},
		{
			text: "Delete Request",
			value: DeleteRequest,
			type: "danger",
			iconLeading: <IconTrashOutline width={16} height={16} color="#FF2323" />,
			show: () => isAllowedStatus(DeleteRequest),
		},
		{
			text: "Input AWB",
			value: InputAwb,
			iconLeading: <IconInputAwbOutline width={16} height={16} />,
			show: () => isAllowedStatus(InputAwb),
		},
		{
			text: "Cancel Request",
			value: CancelRequest,
			type: "danger",
			iconLeading: <IconCloseOutline color="#FF2323" width={16} height={16} />,
			show: () => isAllowedStatus(CancelRequest),
		},
		{
			text: "Receive Item",
			value: ReceiveItem,
			iconLeading: <IconReceiveItemOutline width={16} height={16} />,
			show: () => isAllowedStatus(ReceiveItem),
		},
	].filter((option) => option.show()) as DropdownItemProps[]

	return (
		<div className="flex justify-center gap-xs">
			<DropdownDynamicChild
				options={options}
				placement="rightBottom"
				onItemSelect={handleOnItemSelect}
			>
				<IconKebabMenuVertical />
			</DropdownDynamicChild>
		</div>
	)
}

export default PendingTableRowActions
