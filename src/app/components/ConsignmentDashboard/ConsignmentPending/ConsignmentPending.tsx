import { Space } from "@kickavenue/ui/components"
import { useEffect, useMemo, useState } from "react"
import { useSearchParams } from "next/navigation"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { MiscConstant } from "@constants/misc"
import useURLQuery from "@app/hooks/useUrlQuery"
import { formatListingStatus } from "@utils/listingItem"
import { ESellerConsignmentStatus } from "types/sellerConsignment.type"
import usePopulateFilterData from "@app/hooks/usePopulateFilterData"
import FilterDashboard from "@components/FilterDashboard"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { TConsignmentDashboardTabEnum } from "types/misc.type"

import DeleteRequestConfirm from "../DeleteRequestConfirm"
import CancelRequestConfirm from "../CancelRequestConfirm"
import ReceiveItemConfirm from "../ReceiveItemConfirm"
import ConsignmentDetailModal from "../ConsignmentDetailModal"
import ConsignmentInputAWBModal from "../ConsignmentInputAWBModal"

import ConsignmentPendingTable from "./ConsignmentPendingTable"
import useConsignmentPendingSearchParams from "./hook/useConsignmentPendingSearchParams"

const { DELETE_REQEUST_CONFIRM, CANCEL_REQUEST_CONFIRM, RECEIVE_ITEM_CONFIRM } =
  ModalConstant.MODAL_IDS

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const {
  AwaitingReview,
  InputAwb,
  EnRouteToKickAvenue,
  VerificationFailed,
  AuthenticationProcess,
  WaitingReturnProcess,
  EnRouteToSeller,
} = ESellerConsignmentStatus

const { TAB } = ListingItemConstant.FILTER_FIELDS
const { Pending } = TConsignmentDashboardTabEnum

const ConsignmentPending = () => {
  const { open, modalId } = useModalStore()
  const { handleBulkChangeQuery } = useURLQuery()
  const searchParams = useSearchParams()

  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams?.get(TAB) || Pending

  const renderDeleteRequestConfirm =
    open && modalId === DELETE_REQEUST_CONFIRM ? <DeleteRequestConfirm /> : null
  const renderCancelRequestConfirm =
    open && modalId === CANCEL_REQUEST_CONFIRM ? <CancelRequestConfirm /> : null
  const renderReceiveItemConfirm =
    open && modalId === RECEIVE_ITEM_CONFIRM ? <ReceiveItemConfirm /> : null
  const renderFilterDashboard =
    openFilter && selectedTab === Pending ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  const status = useMemo(
    () =>
      formatListingStatus([
        AwaitingReview,
        InputAwb,
        EnRouteToKickAvenue,
        AuthenticationProcess,
        VerificationFailed,
        WaitingReturnProcess,
        EnRouteToSeller,
      ]) as string[],
    [],
  )

  useConsignmentPendingSearchParams()
  usePopulateFilterData({ status })

  useEffect(() => {
    if (searchParams?.toString()) return
    handleBulkChangeQuery({
      [PAGE]: PAGE_DEFAULT,
      [PAGE_SIZE]: PAGE_SIZE_DEFAULT,
    })
  }, [handleBulkChangeQuery, searchParams])

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <Space size="lg" type="margin" direction="y" />
      <ConsignmentPendingTable />
      <ConsignmentDetailModal />
      <ConsignmentInputAWBModal />
      {renderDeleteRequestConfirm}
      {renderCancelRequestConfirm}
      {renderReceiveItemConfirm}
      {renderFilterDashboard}
    </>
  )
}

export default ConsignmentPending
