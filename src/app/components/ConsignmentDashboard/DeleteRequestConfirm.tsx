import { Button } from "@kickavenue/ui/components"
import { useQueryClient } from "@tanstack/react-query"
import ModalConfirm from "@components/shared/ModalConfirm"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

import useDeleteByIdSellerListing from "./hook/useDeleteByIdSellerListing"

const { DELETE_REQEUST_CONFIRM } = ModalConstant.MODAL_IDS

const DeleteRequestConfirm = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { sellerStock } = useConsignmentModalStore()
  const queryClient = useQueryClient()
  const { mutate: deleteByIdSellerListing, isPending } =
    useDeleteByIdSellerListing({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [QueryKeysConstant.GET_SELLER_STOCKS],
        })
        setOpen(false, DELETE_REQEUST_CONFIRM)
      },
    })
  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, DELETE_REQEUST_CONFIRM)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button
        size="md"
        variant="danger"
        disabled={isPending}
        onClick={() =>
          deleteByIdSellerListing(sellerStock?.sellerListingStock?.id as number)
        }
      >
        Delete Request
      </Button>
    </>
  )
  return (
    <ModalConfirm
      open={open && modalId === DELETE_REQEUST_CONFIRM}
      onClose={() => setOpen(false, DELETE_REQEUST_CONFIRM)}
      title="Are You Sure You Want to Delete This Consignment Request?"
      subtitle="Your consignment request hasn't been approved yet. Deleting it means your items won't be listed on Kick Avenue."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default DeleteRequestConfirm
