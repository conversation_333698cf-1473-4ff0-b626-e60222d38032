import { Divider, Text } from "@kickavenue/ui/dist/src/components"
import { Badge } from "@kickavenue/ui/dist/src/components/Badge"
import useFetchSellerStockById from "@app/hooks/useFetchSellerStockById"
import {
  getConsignmentPendingStatus,
  getSellerConsignmentStatusMap,
} from "@utils/sellerConsignment.utils"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import { getListingCondition } from "@utils/selling"
import CodeWithCopyButton from "@components/shared/CodeWithCopyButton"
import { formatCurrencyStripe } from "@utils/misc"

const ConsignmentDetailModalContent = () => {
  const { sellerStock: selectedSellerStock } = useConsignmentModalStore()

  const { data: sellerStock, isLoading } = useFetchSellerStockById({
    id: selectedSellerStock?.sellerListingStock?.id,
  })

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  const status = getConsignmentPendingStatus(sellerStock)
  const map = getSellerConsignmentStatusMap(status)

  return (
    <div className="flex flex-col gap-lg p-lg">
      <div className="flex justify-between">
        <Text size="base" type="bold" state="primary">
          Consignment Status
        </Text>
        <Badge text={map?.text} type={map?.badgeType} />
      </div>
      <Divider orientation="horizontal" />
      <div className="flex flex-col gap-sm">
        <Text size="sm" type="bold" state="primary">
          Product Detail
        </Text>
        <div className="flex w-full justify-between rounded-sm border border-solid border-gray-w-80 p-base">
          <ProductDetailPreview
            listing={sellerStock?.sellerListingStock}
            listingCondition={getListingCondition(
              sellerStock?.sellerListingStock,
            )}
          />
        </div>
        <div className="flex w-full flex-col gap-sm rounded-sm border border-solid border-gray-w-80 p-base">
          <div className="flex w-full justify-between">
            <Text size="sm" state="primary" type="regular">
              Consignment ID
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <CodeWithCopyButton
                code={sellerStock?.sellerListingStock?.consignmentId as string}
                message="Consignment ID successfully copied!"
              />
            </div>
          </div>
          <div className="flex w-full justify-between">
            <Text size="sm" state="primary" type="regular">
              Price
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <Text size="sm" state="primary" type="regular">
                {formatCurrencyStripe({
                  price: sellerStock?.sellerListingStock?.sellingPrice,
                })}
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConsignmentDetailModalContent
