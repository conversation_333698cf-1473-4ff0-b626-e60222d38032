import {
  IconConsignToWarehouseBulk,
  IconSellerPointBold,
  IconTotalPurchaseBulk,
} from "@kickavenue/ui/components"
import SummaryCard from "@shared/SummaryCard"
import { formatPrice } from "@utils/misc"

import useFetchConsignmentSummary from "./hook/useFetchConsignmentSummary"

const ConsignmentSummaries = () => {
  const { data, isLoading } = useFetchConsignmentSummary()

  return (
    <div className="grid grid-cols-4 gap-base md:grid-cols-12">
      <div className="col-span-4">
        <SummaryCard
          title="Consignment Revenue"
          value={formatPrice(data?.consignmentRevenue || 0, null, "IDR")}
          Icon={
            <IconTotalPurchaseBulk width={24} height={24} color="#960018" />
          }
          isLoading={isLoading}
        />
      </div>
      <div className="col-span-4">
        <SummaryCard
          title="Consigned Inventory"
          value={data?.consignedInventory?.toString() || "0"}
          Icon={
            <IconConsignToWarehouseBulk
              width={24}
              height={24}
              color="#960018"
            />
          }
          isLoading={isLoading}
        />
      </div>
      <div className="col-span-4">
        <SummaryCard
          title="Average Inventory Value"
          value={formatPrice(data?.averageInventoryValue || 0, null, "IDR")}
          Icon={<IconSellerPointBold width={24} height={24} color="#960018" />}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}

export default ConsignmentSummaries
