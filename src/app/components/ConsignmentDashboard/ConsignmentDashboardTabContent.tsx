import { useSearchParams } from "next/navigation"
import { TConsignmentDashboardTab } from "types/misc.type"

import ConsignmentPending from "./ConsignmentPending"
import ConsignmentInProgress from "./ConsignmentInProgress"
import ConsignmentActive from "./ConsignmentActive"
import ConsignmentHistory from "./ConsignmentHistory"

const ConsignmentDashboardTabContent = () => {
  const activeTab =
    (useSearchParams()?.get("tab") as TConsignmentDashboardTab) || "pending"
  const renderTabContent = () => {
    switch (activeTab) {
      case "pending":
        return <ConsignmentPending />
      case "active":
        return <ConsignmentActive />
      case "cg-in-progress":
        return <ConsignmentInProgress />
      case "history":
        return <ConsignmentHistory />
      default:
        return null
    }
  }
  return renderTabContent()
}

export default ConsignmentDashboardTabContent
