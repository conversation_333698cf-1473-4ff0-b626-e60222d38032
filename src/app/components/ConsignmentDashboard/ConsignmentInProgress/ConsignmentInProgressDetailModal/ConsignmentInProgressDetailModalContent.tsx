import { Badge, Divider, Text } from "@kickavenue/ui/dist/src/components"
import useFetchSaleDetail from "@app/hooks/useFetchSaleDetail"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { getConsignmentInProgressStatusMap } from "@utils/sellerConsignment.utils"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import useFetchSellerListingById from "@app/hooks/useFetchSellerListingById"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import { getListingCondition } from "@utils/selling"
import ModalRoundedContent from "@components/shared/ModalParts/ModalRoundedContent"
import CodeWithCopyButton from "@components/shared/CodeWithCopyButton"
import ModalRoundedContentItem from "@components/shared/ModalParts/ModalRoundedContentItem"
import { formatCurrencyStripe, formatDateWIB } from "@utils/misc"
import { TStripePrice } from "types/stripe.type"
import { SellerListing } from "types/sellerListing"

const ConsignmentInProgressDetailModalContent = () => {
  const { txDetailId } = useConsignmentModalStore()

  const { data: txDetail, isLoading: loadSale } = useFetchSaleDetail({
    txDetailId,
  })

  const { data: sellerListing, isLoading: loadSellerListing } =
    useFetchSellerListingById(txDetail?.sellerListingId)

  const sellerStatus = txDetail?.sellerStatus
  const map = getConsignmentInProgressStatusMap(sellerStatus)

  if (loadSale || loadSellerListing) {
    return <SpinnerLoading className="h-[602px]" />
  }

  return (
    <div className="flex h-[602px] flex-col gap-lg p-lg">
      <div className="flex justify-between">
        <Text size="base" type="bold" state="primary">
          Sale Status
        </Text>
        <Badge text={map?.text} type={map?.badgeType} />
      </div>
      <Divider orientation="horizontal" />
      <div className="flex flex-col gap-sm">
        <Text size="sm" type="bold" state="primary">
          Product Detail
        </Text>
        <div className="flex w-full justify-between rounded-sm border border-solid border-gray-w-80 p-base">
          <ProductDetailPreview
            listing={sellerListing as SellerListing}
            listingCondition={getListingCondition(
              sellerListing as SellerListing,
            )}
          />
        </div>
        <ModalRoundedContent>
          <div className="flex w-full justify-between px-base">
            <Text size="sm" state="primary" type="regular">
              Invoice Number
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <CodeWithCopyButton
                code={txDetail?.invoiceNumber as string}
                message="Invoice Number successfully copied!"
              />
            </div>
          </div>
          <div className="flex w-full justify-between px-base">
            <Text
              size="sm"
              state="primary"
              type="regular"
              style={{ textWrap: "wrap" }}
            >
              Consignment ID
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <CodeWithCopyButton
                code={txDetail?.listingItem?.consignmentId as string}
                message="Consignment ID successfully copied!"
              />
            </div>
          </div>
        </ModalRoundedContent>
        <ModalRoundedContent>
          <ModalRoundedContentItem
            text="Sale Date"
            value={formatDateWIB(txDetail?.createdAt)}
          />
          <ModalRoundedContentItem
            text="Listing Price"
            value={formatCurrencyStripe({
              price: sellerListing?.sellingPrice as TStripePrice,
            })}
          />
        </ModalRoundedContent>
      </div>
    </div>
  )
}

export default ConsignmentInProgressDetailModalContent
