import { Text } from "@kickavenue/ui/dist/src/components"
import { getConsignmentInProgressStatusMap } from "@utils/sellerConsignment.utils"
import { TTransactionDetail } from "types/transactionDetail.type"

const ConsignmentInProgressStatus = ({
  record,
}: {
  record: TTransactionDetail
}) => {
  const sellerStatus = record.sellerStatus
  const map = getConsignmentInProgressStatusMap(sellerStatus)

  if (!map) return "-"

  return (
    <Text size="sm" state={map?.textState} type="regular">
      {map?.text}
    </Text>
  )
}

export default ConsignmentInProgressStatus
