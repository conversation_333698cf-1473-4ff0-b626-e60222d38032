import {
	DropdownItemProps,
	IconKebabMenuVertical,
	IconViewDetailsOutline,
} from "@kickavenue/ui/components"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { useModalStore } from "stores/modalStore"
import { TTransactionDetail } from "types/transactionDetail.type"
import { ModalConstant } from "@constants/modal"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"

const { CONSIGNMENT_IN_PROGRESS_DETAIL } = ModalConstant.MODAL_IDS

const InProgressTableRowActions = ({
	record,
}: {
	record: TTransactionDetail
}) => {
	const { setTxDetailId } = useConsignmentModalStore()
	const { setOpen } = useModalStore()

	const options = [
		{
			text: "View Details",
			value: "view-details",
			iconLeading: <IconViewDetailsOutline width={16} height={16} />,
		},
	] as DropdownItemProps[]

	const handleOnItemSelect = () => {
		setTxDetailId(record.id)
		setOpen(true, CONSIGNMENT_IN_PROGRESS_DETAIL)
	}

	return (
		<div className="flex justify-center gap-xs">
			<DropdownDynamicChild
				options={options}
				placement="rightBottom"
				onItemSelect={handleOnItemSelect}
			>
				<IconKebabMenuVertical />
			</DropdownDynamicChild>
		</div>
	)
}

export default InProgressTableRowActions
