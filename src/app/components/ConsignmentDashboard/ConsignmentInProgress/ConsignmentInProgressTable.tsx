import { Space } from "@kickavenue/ui/components"
import { useCallback } from "react"
import Table from "@components/shared/Table"
import TablePagination from "@components/shared/Table/TablePagination"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { useConsignmentInProgressStore } from "stores/consignmentInProgressStore"
import { TSorter } from "types/table.type"
import { camelToSnake } from "@utils/misc"
import useURLQuery from "@app/hooks/useUrlQuery"
import { ListingItemConstant } from "@constants/listingitem.constant"

import styles from "./ConsignmentInProgressTable.module.scss"
import useConsignmentInProgressTable from "./hook/useConsignmentInProgressTable"
import useConsignmentInProgressPagination from "./hook/useConsignmentInProgressPagination"
import useFetchConsignmentInProgress from "./hook/useFetchConsignmentInProgress"

const { SORT_BY } = ListingItemConstant.FILTER_FIELDS

const ConsignmentInProgressTable = () => {
  const { columns } = useConsignmentInProgressTable()
  const { list, filter } = useConsignmentInProgressStore()
  const { isLoading } = useFetchConsignmentInProgress()
  const paging = useConsignmentInProgressPagination()

  const { handleChangeQuery } = useURLQuery()

  const handleTableChange = useCallback(
    (sorter: TSorter) => {
      const field = camelToSnake(sorter.field)?.toLowerCase()
      handleChangeQuery(SORT_BY, `${field},${sorter.order}`)
    },
    [handleChangeQuery],
  )

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(80vh-250px)]" />
  }

  return (
    <>
      <div className="max-h-[580px] min-h-[calc(80vh-260px)] max-w-full overflow-x-auto">
        <Table
          columns={columns}
          dataSource={list}
          rowKey="id"
          className={styles.table}
          loading={isLoading}
          onTableChange={handleTableChange}
          activeSort={filter?.sortBy}
        />
      </div>
      <Space size="lg" type="margin" direction="y" />
      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default ConsignmentInProgressTable
