import { Space } from "@kickavenue/ui/components"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import { MiscConstant } from "@constants/misc"
import useURLQuery from "@app/hooks/useUrlQuery"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { TConsignmentDashboardTabEnum } from "types/misc.type"
import FilterDashboard from "@components/FilterDashboard"

import ConsignmentInProgressTable from "./ConsignmentInProgressTable"
import useConsignmentInProgressSearchParams from "./hook/useConsignmentInProgressSearchParams"
import ConsignmentInProgressDetailModal from "./ConsignmentInProgressDetailModal"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT
const { TAB, SORT_BY } = ListingItemConstant.FILTER_FIELDS
const { CgInProgress } = TConsignmentDashboardTabEnum

const ConsignmentInProgress = () => {
  const { handleBulkChangeQuery } = useURLQuery()
  const searchParams = useSearchParams()

  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams?.get(TAB) || CgInProgress

  useConsignmentInProgressSearchParams()

  useEffect(() => {
    if (searchParams?.get(PAGE) && searchParams?.get(PAGE_SIZE)) return
    handleBulkChangeQuery({
      [PAGE]: PAGE_DEFAULT,
      [PAGE_SIZE]: PAGE_SIZE_DEFAULT,
      [SORT_BY]: "updated_at,DESC",
    })
  }, [handleBulkChangeQuery, searchParams])

  const renderFilterDashboard =
    openFilter && selectedTab === CgInProgress ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <Space size="lg" type="margin" direction="y" />
      <ConsignmentInProgressTable />
      <ConsignmentInProgressDetailModal />
      {renderFilterDashboard}
    </>
  )
}

export default ConsignmentInProgress
