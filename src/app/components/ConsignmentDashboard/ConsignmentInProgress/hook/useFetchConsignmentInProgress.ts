import { useQuery } from "@tanstack/react-query"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { TransactionDetailApiRepository } from "@infrastructure/repositories/transactionDetailApiRepository"
import { TTransactionDetailFilter } from "types/transactionDetail.type"
import { useConsignmentInProgressStore } from "stores/consignmentInProgressStore"
import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import { ConsignmentDashboardTabEnum } from "types/listingItem.type"
import { isEmpty } from "@utils/misc"
import { GetMySell } from "@application/usecases/getMySell"

const { TAB } = MiscConstant.FILTER_FIELDS

const useFetchConsignmentInProgress = () => {
  const { filter, setList, setFilter } = useConsignmentInProgressStore()
  const { getSearchParam } = useURLQuery()
  const tab = getSearchParam(TAB)

  const fetchConsignmentInProgress = async () => {
    const r = new TransactionDetailApiRepository()
    const u = new GetMySell(r)
    const res = await u.execute(filter as TTransactionDetailFilter)
    setList(res.content)
    setFilter({
      ...(filter as TTransactionDetailFilter),
      totalPages: res.totalPages,
      pageSize: res.pageSize,
    })
    return res
  }

  const enabled =
    tab === ConsignmentDashboardTabEnum.CgInProgress &&
    !isEmpty(filter?.page) &&
    !isEmpty(filter?.pageSize)

  const query = useQuery({
    queryKey: [
      QueryKeysConstant.GET_CONSIGNMENT_IN_PROGRESS,
      filter?.page,
      filter?.pageSize,
      filter?.search,
      filter?.sortBy,
      filter?.startPurchaseAmount,
      filter?.endPurchaseAmount,
      filter?.startDate,
      filter?.endDate,
      filter?.categoryId,
      filter?.brandId,
      filter?.itemCondition,
      filter?.isNewNoDefect,
      filter?.sizeId,
    ],
    queryFn: fetchConsignmentInProgress,
    enabled,
    staleTime: 1,
    retry: false,
  })

  return {
    ...query,
    data: query.data?.content,
  }
}

export default useFetchConsignmentInProgress
