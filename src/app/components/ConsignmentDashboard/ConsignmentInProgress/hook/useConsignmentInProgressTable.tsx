import { Text } from "@kickavenue/ui/components"
import InProgressTableRowActions from "@components/ConsignmentDashboard/ConsignmentInProgress/InProgressTableRowActions"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import { formatDateObj, formatPriceMinUnitVal } from "@utils/misc"
import { formatCurrency } from "@utils/separator"
import { TTableColumn } from "types/table.type"
import { TTransactionDetail } from "types/transactionDetail.type"
import { getUsSize } from "@utils/size.utils"
import classes from '../ConsignmentInProgressTable.module.scss'

import ConsignmentInProgressStatus from "../ConsignmentInProgressStatus"
import { cx } from "class-variance-authority"

const columnWidth = {
	consignmentId: 150,
	productDetails: 300,
	size: 64,
	conditions: 108,
	soldPrice: 172,
	soldDate: 140,
	status: 128,
	actions: 67,
}

const useConsignmentInProgressTable = () => {
	const columns = [
		{
			key: "consignmentId",
			title: "Invoice Number",
			width: columnWidth.consignmentId,
			headerClassName: classes['sticky-header-1'],
			contentClassName: classes['sticky-content-1'],
			render: ({ listingItem, invoiceNumber }: TTransactionDetail) => {
				return (
					<>
						<Text size="sm" state="primary" type="regular">
							{invoiceNumber}
						</Text>
						<Text size="xs" state="primary" type="regular" style={{ color: "#666" }}>
							{listingItem?.consignmentId}
						</Text>
					</>
				)
			},
		},
		{
			key: "productDetails",
			title: "Product Details",
			headerClassName: classes['sticky-header-2'],
			contentClassName: classes['sticky-content-2'],
			width: columnWidth.productDetails,
			render: ({ listingItem }: TTransactionDetail) => {
				return <ProductDetailColumn listingItem={listingItem} />
			},
		},
		{
			key: "size",
			dataIndex: "size",
			title: "Size",
			width: columnWidth.size,
			render: ({ listingItem }: TTransactionDetail) =>
				getUsSize(listingItem?.size),
		},
		{
			key: "itemCondition",
			title: "Conditions",
			width: columnWidth.conditions,
			render: ({ listingItem }: TTransactionDetail) => (
				<ConditionColumn
					itemCondition={listingItem?.itemCondition}
					packagingCondition={listingItem?.packagingCondition}
				/>
			),
		},
		{
			key: "price",
			title: "Sold Price",
			width: columnWidth.soldPrice,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: ({ listingItem }: TTransactionDetail) => {
				const sellingPrice = listingItem?.sellingPrice?.minUnitVal
				if (!sellingPrice) return "-"

				return formatCurrency(formatPriceMinUnitVal(sellingPrice), ",", "IDR")
			},
			sorter: () => { },
		},
		{
			key: "createdAt",
			title: "Sold Date",
			width: columnWidth.soldDate,
			render: (record: TTransactionDetail) =>
				formatDateObj(new Date(record.createdAt as string)),
			sorter: () => { },
		},
		{
			key: "sellerStatus",
			title: "Status",
			width: columnWidth.status,
			render: (record: TTransactionDetail) => (
				<ConsignmentInProgressStatus record={record} />
			),
		},
		{
			key: "actions",
			title: "Actions",
			width: columnWidth.actions,
			headerClassName: `${classes['sticky-right-header-1']} text-center [&>div]:!justify-center`,
			contentClassName: cx(classes['sticky-right-content-1'], "dropdown-options"),
			render: (record: TTransactionDetail) => (
				<InProgressTableRowActions record={record} />
			),
		},
	] as TTableColumn[]

	return { columns }
}

export default useConsignmentInProgressTable
