import { Button } from "@kickavenue/ui/components"
import { useQueryClient } from "@tanstack/react-query"
import ModalConfirm from "@components/shared/ModalConfirm"
import { ModalConstant } from "@constants/modal"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { useModalStore } from "stores/modalStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

import useCancelConsignmentById from "./hook/useCancelConsignmentById"

const { CANCEL_REQUEST_CONFIRM } = ModalConstant.MODAL_IDS

const CancelRequestConfirm = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { sellerStock } = useConsignmentModalStore()
  const queryClient = useQueryClient()
  const { mutate: cancelConsignment, isPending } = useCancelConsignmentById({
    onSuccess: () => {
      setOpen(false, CANCEL_REQUEST_CONFIRM)
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_SELLER_STOCKS],
      })
    },
  })
  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, CANCEL_REQUEST_CONFIRM)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button
        size="md"
        variant="danger"
        disabled={isPending}
        onClick={() =>
          cancelConsignment(sellerStock?.sellerConsignment?.id as number)
        }
      >
        Cancel Request
      </Button>
    </>
  )
  return (
    <ModalConfirm
      open={open && modalId === CANCEL_REQUEST_CONFIRM}
      onClose={() => setOpen(false, CANCEL_REQUEST_CONFIRM)}
      title="Are You Sure You Want to Cancel This  Consignment Request?"
      subtitle="Canceling now means missing the opportunity to consign your item with Kick Avenue"
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default CancelRequestConfirm
