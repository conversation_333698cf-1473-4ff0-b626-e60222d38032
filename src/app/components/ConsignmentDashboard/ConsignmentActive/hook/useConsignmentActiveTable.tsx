import { Text } from "@kickavenue/ui/components"
import CheckboxHeader from "@components/SellingDashboard/CheckboxHeader"
import { TListingItem } from "types/listingItem.type"
import { TTableColumn } from "types/table.type"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import { formatCurrency } from "@utils/separator"
import {
	formatDateObj,
	formatPriceMinUnitVal,
	getStripAmount,
} from "@utils/misc"
import ActiveTableRowActions from "@components/ConsignmentDashboard/ConsignmentActive/ActiveTableRowActions"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import CurrentTableStatus from "@components/SellingDashboard/SellingCurrent/CurrentTable/CurrentTableStatus"
import classes from '../ConsignmentActiveTable.module.scss'
import { cx } from "class-variance-authority"

const columnWidth = {
	checkbox: 44,
	consignmentId: 150,
	productDetails: 300,
	size: 64,
	conditions: 108,
	listingPrice: 148,
	standardLowestAsk: 148,
	expressLowestAsk: 148,
	highestOffer: 148,
	listedDate: 140,
	status: 128,
	actions: 67,
}

// eslint-disable-next-line max-lines-per-function
const useConsignmentActiveTable = () => {
	const { selectedRowKeys, setSelectedRowKeys, consignmentActiveData } =
		useConsignmentActiveStore()
	const columns = [
		{
			key: "checkbox",
			title: "",
			width: columnWidth.checkbox,
			headerClassName: classes['sticky-header-0'],
			contentClassName: classes['sticky-content-0'],
			renderHeader: () => (
				<CheckboxHeader
					selectedRowKeys={selectedRowKeys}
					setSelectedRowKeys={setSelectedRowKeys}
					data={consignmentActiveData}
				/>
			),
		},
		{
			key: "consignmentId",
			title: "Consignment ID",
			width: columnWidth.consignmentId,
			headerClassName: classes['sticky-header-1'],
			contentClassName: classes['sticky-content-1'],
			render: (record: TListingItem) => {
				const consignmentId = record.consignmentId
				if (!consignmentId) return "-"

				return (
					<Text size="sm" state="primary" type="regular">
						{consignmentId}
					</Text>
				)
			},
			sorter: () => { },
		},
		{
			key: "productDetails",
			title: "Product Details",
			width: columnWidth.productDetails,
			headerClassName: classes['sticky-header-2'],
			contentClassName: classes['sticky-content-2'],
			render: (record: TListingItem) => {
				return <ProductDetailColumn listingItem={record} />
			},
		},
		{
			key: "size",
			dataIndex: "size",
			title: "Size",
			width: columnWidth.size,
			render: (record: TListingItem) =>
				record?.size?.us ? `US ${record.size.us}` : "-",
		},
		{
			key: "itemCondition",
			title: "Conditions",
			width: columnWidth.conditions,
			render: (record: TListingItem) => (
				<ConditionColumn
					itemCondition={record?.itemCondition}
					packagingCondition={record?.packagingCondition}
				/>
			),
		},
		{
			key: "sellingPrice",
			title: "Listing Price",
			width: columnWidth.listingPrice,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			sorter: () => { },
			render: (item: TListingItem) => {
				const sellingPrice = Number(item?.sellingPrice?.amount || 0)
				const standardLowestAsk = Number(item?.standardLowestAsk?.amount || 0)

				if (sellingPrice === 0 || !sellingPrice) return "-"

				const isGreen =
					sellingPrice <= standardLowestAsk && standardLowestAsk !== 0
				const isRed =
					sellingPrice > standardLowestAsk && standardLowestAsk !== 0

				return (
					<Text
						size={"sm"}
						type={"regular"}
						state={isGreen ? "success" : isRed ? "danger" : "primary"}
					>
						{formatCurrency(sellingPrice, ",", "IDR")}
					</Text>
				)
			},
		},
		{
			key: "standardLowestAsk",
			dataIndex: "standardLowestAsk",
			title: "Standard Lowest Ask",
			width: columnWidth.standardLowestAsk,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (item: TListingItem) => {
				const standardLowestAsk = item?.standardLowestAsk?.minUnitVal
				if (!standardLowestAsk) return "-"

				return formatCurrency(
					formatPriceMinUnitVal(standardLowestAsk),
					",",
					"IDR",
				)
			},
		},
		{
			key: "expressLowestAsk",
			dataIndex: "expressLowestAsk",
			title: "Express Lowest Ask",
			width: columnWidth.expressLowestAsk,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (item: TListingItem) => {
				const expressLowestAsk = item?.expressLowestAsk?.minUnitVal
				if (!expressLowestAsk) return "-"

				return formatCurrency(
					formatPriceMinUnitVal(expressLowestAsk),
					",",
					"IDR",
				)
			},
		},
		{
			key: "highestOfferAmount",
			title: "Highest Offer",
			width: columnWidth.highestOffer,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (item: TListingItem) => {
				const highestOfferAmount = item?.highestOfferAmount
				if (!highestOfferAmount) return "-"

				return formatCurrency(getStripAmount(highestOfferAmount), ",", "IDR")
			},
		},
		{
			key: "createdAt",
			title: "Listed Date",
			width: columnWidth.listedDate,
			render: (item: TListingItem) =>
				formatDateObj(new Date(item.createdAt || "")),
			sorter: () => { },
		},
		{
			key: "status",
			title: "Status",
			width: columnWidth.status,
			render: (record: TListingItem) => <CurrentTableStatus record={record} />,
		},
		{
			key: "actions",
			title: "Actions",
			headerClassName: classes['sticky-right-header-1'],
			contentClassName: cx(classes['sticky-right-content-1'], 'dropdown-options'),
			width: columnWidth.actions,
			render: (record: TListingItem) => (
				<ActiveTableRowActions record={record} />
			),
		},
	] as TTableColumn[]
	return { columns }
}

export default useConsignmentActiveTable
