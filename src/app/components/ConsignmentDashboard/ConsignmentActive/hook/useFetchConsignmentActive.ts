import { useQuery } from "@tanstack/react-query"
import { ListingItemApiRepository } from "@infrastructure/repositories/listingItemApiRepository"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import {
  ConsignmentDashboardTabEnum,
  TListingItemFilter,
} from "types/listingItem.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useURLQuery from "@app/hooks/useUrlQuery"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { isEmpty } from "@utils/misc"
import { GetMyListing } from "@application/usecases/getMyListing"
import { removeDuplicates } from "@utils/array.utils"

const useFetchConsignmentActive = () => {
  const {
    setConsignmentActiveData,
    filter,
    setFilter,
    consignmentAllData,
    setConsignmentAllData,
  } = useConsignmentActiveStore()
  const { getSearchParam } = useURLQuery()
  const tab = getSearchParam(ListingItemConstant.FILTER_FIELDS.TAB)

  const fetchConsignmentActive = async () => {
    const r = new ListingItemApiRepository()
    const u = new GetMyListing(r)
    const res = await u.execute(filter as TListingItemFilter)
    const newData = removeDuplicates(
      [...(res?.content || []), ...consignmentAllData],
      "id",
    )
    setConsignmentAllData(newData)
    setConsignmentActiveData(res?.content || [])
    setFilter({
      ...(filter as TListingItemFilter),
      totalPages: res?.totalPages ?? 0,
      pageSize: res?.pageSize ?? filter?.pageSize,
    })
  }

  const enabled =
    tab === ConsignmentDashboardTabEnum.Active &&
    !isEmpty(filter?.status) &&
    !isEmpty(filter?.hasQuantity) &&
    !isEmpty(filter?.page) &&
    !isEmpty(filter?.pageSize)

  const consignmentActive = useQuery({
    queryKey: [
      QueryKeysConstant.GET_CONSIGNMENT_ACTIVE,
      filter?.page,
      filter?.pageSize,
      filter?.sortBy,
      filter?.itemCondition,
      filter?.isNewNoDefect,
      filter?.sizeId,
      filter?.categoryId,
      filter?.categoryName,
      filter?.search,
      filter?.priceFrom,
      filter?.priceTo,
      filter?.sellDateFrom,
      filter?.sellDateTo,
      filter?.hasOffer,
    ],
    queryFn: fetchConsignmentActive,
    enabled,
    staleTime: 1,
    retry: false,
  })

  return {
    data: consignmentActive?.data,
    isLoading: consignmentActive?.isLoading,
    error: consignmentActive?.error,
  }
}

export default useFetchConsignmentActive
