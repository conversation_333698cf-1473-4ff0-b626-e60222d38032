import {
  Divider,
  DropdownItemProps,
  IconReceiveItemOutline,
  IconUpdatePriceOutline,
  Space,
  Text,
} from "@kickavenue/ui/components"
import Dropdown from "@shared/Form/Dropdown"
import { ModalConstant } from "@constants/modal"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import { useModalStore } from "stores/modalStore"

const { CONSIGNMENT_UPDATE_LISTING_PRICE, CONSIGNMENT_TAKE_OUT } =
  ModalConstant.MODAL_IDS

const ConsignmentActiveBulkActions = () => {
  const { setOpen } = useModalStore()
  const { selectedRowKeys } = useConsignmentActiveStore()
  const handleActionSelected = (value: string) => {
    switch (value) {
      case "update-price":
        setOpen(true, CONSIGNMENT_UPDATE_LISTING_PRICE)
        break
      case "take-out-consignment":
        setOpen(true, CONSIGNMENT_TAKE_OUT)
        break
    }
  }
  const options = [
    {
      text: "Update Listing Price",
      value: "update-price",
      iconLeading: <IconUpdatePriceOutline />,
    },
    {
      text: "Take Out Consignment",
      value: "take-out-consignment",
      iconLeading: <IconReceiveItemOutline width={16} height={16} />,
    },
  ] as DropdownItemProps[]
  if (!selectedRowKeys.length) return null
  return (
    <>
      <Space size="lg" type="margin" direction="y" />
      <Divider orientation="horizontal" state="default" />
      <Space size="lg" type="margin" direction="y" />
      <div className="flex items-center justify-between">
        <Text size="base" type="bold" state="primary">
          {selectedRowKeys?.length} Items Selected
        </Text>
        <Dropdown
          options={options}
          type="button"
          placeholder="More Actions"
          className="!w-[186px]"
          placement="centerTop"
          onItemSelect={handleActionSelected}
        />
      </div>
    </>
  )
}

export default ConsignmentActiveBulkActions
