import { Space } from "@kickavenue/ui/components"
import Table from "@shared/Table"
import TablePagination from "@shared/Table/TablePagination"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import { TSorter } from "types/table.type"
import { camelToSnake } from "@utils/misc"
import { TListingItemFilter } from "types/listingItem.type"
import SpinnerLoading from "@components/shared/SpinnerLoading"

import styles from "./ConsignmentActiveTable.module.scss"
import useConsignmentActiveTable from "./hook/useConsignmentActiveTable"
import useFetchConsignmentActive from "./hook/useFetchConsignmentActive"
import useConsignmentActivePagination from "./hook/useConsignmentActivePagination"

const ConsignmentActiveTable = () => {
  const { columns } = useConsignmentActiveTable()
  const {
    setSelectedRowKeys,
    setFilter,
    filter,
    selectedRowKeys,
    consignmentActiveData,
  } = useConsignmentActiveStore()
  const paging = useConsignmentActivePagination()

  const { isLoading } = useFetchConsignmentActive()

  const handleTableChange = (sorter: TSorter) => {
    const field = camelToSnake(sorter.field)?.toLowerCase()
    setFilter({
      ...(filter as TListingItemFilter),
      sortBy: `${field},${sorter.order}`,
    })
  }

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(80vh-250px)]" />
  }

  return (
    <div className={styles.container}>
      <div className={styles["table-container"]}>
        <Table
          columns={columns}
          dataSource={consignmentActiveData}
          rowKey="id"
          className={styles.table}
          rowSelection={{
            selectedRowKeys,
            onChange: (selected: number[]) => setSelectedRowKeys(selected),
          }}
          loading={isLoading}
          onTableChange={handleTableChange}
        />
      </div>
      <Space size="lg" type="margin" direction="y" />
      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </div>
  )
}

export default ConsignmentActiveTable
