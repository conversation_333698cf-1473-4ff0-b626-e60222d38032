import {
  DropdownItemProps,
  IconKebabMenuVertical,
  IconReceiveItemOutline,
  IconTagOutline,
  IconUpdatePriceOutline,
  IconViewDetailsOutline,
} from "@kickavenue/ui/components"
import { useCallback } from "react"
import { ModalConstant } from "@constants/modal"
import DropdownDynamicChild from "@shared/Form/DropdownDynamicChild"
import { useModalStore } from "stores/modalStore"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import { TListingItem, TListingStatusEnum } from "types/listingItem.type"
import { useConsignmentModalStore } from "stores/consignmentModalStore"

import { EConsignmentActiveAction } from "./consignmentActive.type"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { getStripAmount } from "@utils/misc"

const {
  CONSIGNMENT_SELL_NOW,
  CONSIGNMENT_UPDATE_LISTING_PRICE,
  CONSIGNMENT_TAKE_OUT,
  CONSIGNMENT_ACTIVE_DETAIL,
} = ModalConstant.MODAL_IDS

const { ViewDetails, SellNow, UpdateListingPrice, TakeOutConsignment } =
  EConsignmentActiveAction

const { ListingApproved } = TListingStatusEnum

const STATUS_ACTIONS_MAP: Partial<
  Record<TListingStatusEnum, EConsignmentActiveAction[]>
> = {
  [ListingApproved]: [
    ViewDetails,
    SellNow,
    UpdateListingPrice,
    TakeOutConsignment,
  ],
}

export interface ActiveTableRowActionsProps {
  record: TListingItem
}

const ActiveTableRowActions = ({ record }: ActiveTableRowActionsProps) => {
  const replaceQueryParams = useReplaceQueryParams()
  const { setOpen } = useModalStore()
  const { setSelectedRowKeys } = useConsignmentActiveStore()
  const { setSellerListingId } = useConsignmentModalStore()

  const isAllowedStatus = useCallback(
    (action: EConsignmentActiveAction) =>
      STATUS_ACTIONS_MAP[record?.status as TListingStatusEnum]?.includes(
        action,
      ),
    [record],
  )

  const handleOnItemSelect = useCallback(
    (option: DropdownItemProps) => {
      switch (option.value) {
        case SellNow:
          replaceQueryParams({
            sellingId: record?.id,
            itemId: record?.item?.id,
            sizeId: record?.size?.id,
          })
          setOpen(true, CONSIGNMENT_SELL_NOW)
          break
        case UpdateListingPrice:
          setSelectedRowKeys([record?.id])
          setOpen(true, CONSIGNMENT_UPDATE_LISTING_PRICE)
          break
        case TakeOutConsignment:
          setSelectedRowKeys([record?.id])
          setOpen(true, CONSIGNMENT_TAKE_OUT)
          break
        case ViewDetails:
          setOpen(true, CONSIGNMENT_ACTIVE_DETAIL)
          setSellerListingId(record?.id)
          break
        default:
      }
    },
    [
      record?.id,
      record?.item?.id,
      record?.size?.id,
      setSelectedRowKeys,
      setSellerListingId,
      setOpen,
      replaceQueryParams,
    ],
  )

  const options = [
    {
      text: "View Details",
      value: ViewDetails,
      iconLeading: <IconViewDetailsOutline width={16} height={16} />,
      show: () => isAllowedStatus(ViewDetails),
    },
    {
      text: "Sell Now",
      value: SellNow,
      iconLeading: <IconTagOutline width={16} height={16} />,
      show: () =>
        record?.highestOfferAmount &&
        getStripAmount(record?.highestOfferAmount) > 0,
    },
    {
      text: "Update Listing Price",
      value: UpdateListingPrice,
      iconLeading: <IconUpdatePriceOutline width={16} height={16} />,
      show: () => isAllowedStatus(UpdateListingPrice),
    },
    {
      text: "Take Out Consignment",
      value: TakeOutConsignment,
      iconLeading: <IconReceiveItemOutline width={16} height={16} />,
      show: () => isAllowedStatus(TakeOutConsignment),
    },
  ].filter((item) => item.show()) as unknown as DropdownItemProps[]

  return (
    <div className="flex justify-center gap-xs">
      <DropdownDynamicChild
        options={options}
        placement="rightBottom"
        onItemSelect={handleOnItemSelect}
      >
        <IconKebabMenuVertical />
      </DropdownDynamicChild>
    </div>
  )
}

export default ActiveTableRowActions
