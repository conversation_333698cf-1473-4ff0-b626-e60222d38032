import { Button } from "@kickavenue/ui/components"
import { useQueryClient } from "@tanstack/react-query"
import ModalConfirm from "@components/shared/ModalConfirm"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

import useSubmitReceiveItem from "./hook/useSubmitReceiveItem"

const { RECEIVE_ITEM_CONFIRM } = ModalConstant.MODAL_IDS

const ReceiveItemConfirm = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { sellerStock } = useConsignmentModalStore()
  const queryClient = useQueryClient()
  const { mutate: receiveItem, isPending } = useSubmitReceiveItem({
    onSuccess: () => {
      setOpen(false, RECEIVE_ITEM_CONFIRM)
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_SELLER_STOCKS],
      })
    },
  })
  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, RECEIVE_ITEM_CONFIRM)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button
        size="md"
        variant="primary"
        onClick={() =>
          receiveItem(sellerStock?.sellerConsignment?.id as number)
        }
        disabled={isPending}
      >
        Confirm Delivery
      </Button>
    </>
  )
  return (
    <ModalConfirm
      open={open && modalId === RECEIVE_ITEM_CONFIRM}
      onClose={() => setOpen(false, RECEIVE_ITEM_CONFIRM)}
      title="Have You Received Your Item?"
      subtitle="If you confirm receipt, this will finalize the consignment return process and update the status."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default ReceiveItemConfirm
