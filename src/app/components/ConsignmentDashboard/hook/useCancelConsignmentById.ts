import { useMutation } from "@tanstack/react-query"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"
import { CancelConsignment } from "@application/usecases/cancelConsignment"

const useCancelConsignmentById = ({
  onSuccess,
}: {
  onSuccess?: (val: boolean) => void
}) => {
  const cancelConsignment = async (sellerConsignmentId: number) => {
    const r = new SellerConsignmentApiRepository()
    const u = new CancelConsignment(r)
    const res = await u.execute(sellerConsignmentId)
    return res
  }

  const mutation = useMutation({
    mutationFn: (sellerConsignmentId: number) =>
      cancelConsignment(sellerConsignmentId),
    onSuccess: (val) => {
      onSuccess?.(val)
    },
  })

  return mutation
}

export default useCancelConsignmentById
