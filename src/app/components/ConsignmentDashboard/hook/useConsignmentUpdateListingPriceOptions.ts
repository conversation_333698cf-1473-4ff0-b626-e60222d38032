import { useCallback } from "react"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"

const useConsignmentUpdateListingPriceOptions = () => {
  const { setUpdatePrice, setUpdatePriceAction } = useConsignmentActiveStore()
  const options = [
    { text: "Update Listing Price to", value: "update-to" },
    { text: "Update Listing Price by", value: "update-by" },
  ]
  const handleOptionSelect = useCallback(
    (val: string) => {
      setUpdatePrice(null)
      switch (val) {
        case "update-to":
          setUpdatePriceAction("update-to")
          break
        case "update-by":
          setUpdatePriceAction("update-by")
          break
        default:
          setUpdatePriceAction("")
          break
      }
    },
    [setUpdatePrice, setUpdatePriceAction],
  )
  return { handleOptionSelect, options }
}

export default useConsignmentUpdateListingPriceOptions
