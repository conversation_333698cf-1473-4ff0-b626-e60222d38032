import { useMutation } from "@tanstack/react-query"
import { ConsignmentReceiveItem } from "@application/usecases/consignmentReceiveItem"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"

const useSubmitReceiveItem = ({
  onSuccess,
}: {
  onSuccess?: (val: boolean) => void
}) => {
  const submitReceiveItem = async (sellerConsignmentId: number) => {
    const r = new SellerConsignmentApiRepository()
    const u = new ConsignmentReceiveItem(r)
    const res = await u.execute(sellerConsignmentId)
    return res
  }
  const mutation = useMutation({
    mutationFn: (sellerConsignmentId: number) =>
      submitReceiveItem(sellerConsignmentId),
    onSuccess: (val) => {
      if (val) {
        onSuccess?.(val)
      }
    },
  })

  return mutation
}

export default useSubmitReceiveItem
