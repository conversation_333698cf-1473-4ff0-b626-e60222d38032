import { useMutation } from "@tanstack/react-query"
import { BulkTakeOutConsignment } from "@application/usecases/bulkTakeOutConsignment"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"

const useBulkTakeoutConsignment = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void
  onError?: (error: Error) => void
}) => {
  const bulkTakeOutConsignment = async (ids: number[]) => {
    const r = new SellerConsignmentApiRepository()
    const u = new BulkTakeOutConsignment(r)
    const res = await u.execute(ids)
    return res
  }

  const mutation = useMutation({
    mutationFn: bulkTakeOutConsignment,
    onSuccess,
    onError,
  })

  return mutation
}

export default useBulkTakeoutConsignment
