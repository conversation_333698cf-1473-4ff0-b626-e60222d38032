import { useMutation } from "@tanstack/react-query"
import { ListingItemApiRepository } from "@infrastructure/repositories/listingItemApiRepository"
import { DeleteByIdSellerListing } from "@application/usecases/deleteByIdSellerListing"

const useDeleteByIdSellerListing = ({
  onSuccess,
}: {
  onSuccess?: (val: boolean) => void
}) => {
  const deleteByIdSellerListing = async (sellerListingId: number) => {
    const r = new ListingItemApiRepository()
    const u = new DeleteByIdSellerListing(r)
    const res = await u.execute(sellerListingId)
    return res
  }

  const mutation = useMutation({
    mutationFn: (sellerListingId: number) =>
      deleteByIdSellerListing(sellerListingId),
    onSuccess: (val) => {
      if (val) {
        onSuccess?.(val)
      }
    },
  })

  return mutation
}

export default useDeleteByIdSellerListing
