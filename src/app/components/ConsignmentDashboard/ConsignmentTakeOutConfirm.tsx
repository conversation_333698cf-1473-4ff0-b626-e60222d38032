import { Button } from "@kickavenue/ui/components"
import { useQueryClient } from "@tanstack/react-query"
import ModalConfirm from "@components/shared/ModalConfirm"
import { useSellingModalStore } from "stores/sellingModalStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"

import useBulkTakeoutConsignment from "./hook/useBulkTakeoutConsignment"

const ConsignmentTakeOutConfirm = () => {
  const { openConsignTakeOutConfirm, setOpenConsignTakeOutConfirm } =
    useSellingModalStore()
  const { selectedRowKeys, setSelectedRowKeys, consignmentAllData } =
    useConsignmentActiveStore()
  const { setShowToast } = useToast()

  const queryClient = useQueryClient()

  const { mutate, isPending } = useBulkTakeoutConsignment({
    onSuccess: () => {
      setOpenConsignTakeOutConfirm(false)
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_CONSIGNMENT_ACTIVE],
      })
      setShowToast(true, "Take out consignment successfully", "success")
      setSelectedRowKeys([])
    },
    onError: (error) => {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })

  const getSellerConsignmentIds = () => {
    const ids = consignmentAllData
      ?.filter((item) => selectedRowKeys.includes(item.id))
      ?.map((item) => item.sellerConsignmentId)

    return (ids || []) as number[]
  }

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpenConsignTakeOutConfirm(false)}
        size="md"
        variant="secondary"
      >
        Cancel
      </Button>
      <Button
        size="md"
        variant="danger"
        onClick={() => {
          mutate(getSellerConsignmentIds())
        }}
        disabled={isPending}
      >
        Confirm Take Out
      </Button>
    </>
  )

  if (!openConsignTakeOutConfirm) return null

  return (
    <ModalConfirm
      open={openConsignTakeOutConfirm}
      onClose={() => setOpenConsignTakeOutConfirm(false)}
      title="Are you sure you want to take out these consigned items?"
      subtitle="By confirming, your consigned items will be delisted from sale on the Kick Avenue Platform."
      slotAction={renderSlotAction()}
    />
  )
}

export default ConsignmentTakeOutConfirm
