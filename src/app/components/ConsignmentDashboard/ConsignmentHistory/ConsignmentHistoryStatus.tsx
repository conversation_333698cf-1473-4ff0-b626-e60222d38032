import { Text } from "@kickavenue/ui/dist/src/components"
import {
  getConsignmentHistoryStatus,
  getConsignmentHistoryStatusMap,
} from "@utils/sellerConsignment.utils"
import { TConsignmentTransactionResponse } from "types/sellerConsignment.type"

const ConsignmentHistoryStatus = ({
  record,
}: {
  record: TConsignmentTransactionResponse
}) => {
  const status = getConsignmentHistoryStatus(record)
  const map = getConsignmentHistoryStatusMap(status)

  if (!map) return "-"

  return (
    <Text size="sm" state={map?.textState} type="regular">
      {map?.text}
    </Text>
  )
}

export default ConsignmentHistoryStatus
