import { useQuery } from "@tanstack/react-query"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { useConsignmentHistoryStore } from "stores/consignmentHistoryStore"
import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import { ConsignmentDashboardTabEnum } from "types/listingItem.type"
import { isEmpty } from "@utils/misc"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"
import { TSellerConsignmentFilter } from "types/sellerConsignment.type"
import { GetAllConsignSellerTx } from "@application/usecases/getAllConsignSellerTx"

const { TAB } = MiscConstant.FILTER_FIELDS

const useFetchConsignmentHistory = () => {
  const { filter, setList, setFilter } = useConsignmentHistoryStore()
  const { getSearchParam } = useURLQuery()
  const tab = getSearchParam(TAB)

  const fetchConsignmentHistory = async () => {
    const r = new SellerConsignmentApiRepository()
    const u = new GetAllConsignSellerTx(r)
    const res = await u.execute(filter as TSellerConsignmentFilter)
    setList(res.content)
    setFilter({
      ...(filter as TSellerConsignmentFilter),
      totalPages: res.totalPages,
      pageSize: res.pageSize,
    })
    return res
  }

  const enabled =
    tab === ConsignmentDashboardTabEnum.History &&
    !isEmpty(filter?.page) &&
    !isEmpty(filter?.pageSize)

  const { isLoading } = useQuery({
    queryKey: [
      QueryKeysConstant.GET_CONSIGNMENT_HISTORY,
      filter?.page,
      filter?.pageSize,
      filter?.status,
      filter?.soldPriceFrom,
      filter?.soldPriceTo,
      filter?.soldDateFrom,
      filter?.soldDateTo,
      filter?.itemCondition,
      filter?.isNewNoDefect,
      filter?.sizeID,
      filter?.categoryID,
      filter?.brandID,
      filter?.sort,
      filter?.search,
    ],
    queryFn: fetchConsignmentHistory,
    enabled,
    staleTime: 1,
    retry: false,
  })

  return { isLoading }
}

export default useFetchConsignmentHistory
