import { useCallback, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { ConsignmentDashboardTabEnum } from "types/listingItem.type"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { useConsignmentHistoryStore } from "stores/consignmentHistoryStore"
import { MiscConstant } from "@constants/misc"
import { TSellerConsignmentFilter } from "types/sellerConsignment.type"
import { enumerizeAndMapStatus } from "@utils/sellerConsignment.utils"
import { joinArrayToString } from "@utils/misc"
import { getFilterFromQuery } from "@utils/query.utils"
import { formatDate } from "@utils/date.utils"
import { getConditionFilter } from "@utils/listingItem"
import { stringToNumberArray, stringToStringArray } from "@utils/string.utils"

const {
  PAGE,
  PAGE_SIZE,
  TAB,
  STATUS,
  SOLD_PRICE_MIN,
  SOLD_PRICE_MAX,
  SOLD_DATE_START,
  SOLD_DATE_END,
  CONDITION,
  SIZE_ID,
  CATEGORY_ID,
  BRAND_ID,
  SEARCH,
} = ListingItemConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const useConsignmentHistorySearchParams = () => {
  const searchParams = useSearchParams()
  const { setFilter } = useConsignmentHistoryStore()

  const mapStatusQuery = useCallback((status?: string | null) => {
    if (!status) return undefined
    if (status.includes(",")) {
      const qStatus = status.split(",")
      const statusEnum = enumerizeAndMapStatus(qStatus)
      return joinArrayToString(statusEnum as string[])
    }
    return joinArrayToString(enumerizeAndMapStatus(status) as string[])
  }, [])

  useEffect(() => {
    const tab = searchParams?.get(TAB)
    if (
      tab !== ConsignmentDashboardTabEnum.History ||
      !searchParams?.toString()
    ) {
      return
    }
    const page = searchParams?.get(PAGE)
    const pageSize = searchParams?.get(PAGE_SIZE)
    const status = mapStatusQuery(searchParams?.get(STATUS))
    const soldPriceMin = searchParams?.get(SOLD_PRICE_MIN)
    const soldPriceMax = searchParams?.get(SOLD_PRICE_MAX)
    const soldDateStart = searchParams?.get(SOLD_DATE_START)
    const soldDateEnd = searchParams?.get(SOLD_DATE_END)
    const condition = searchParams?.get(CONDITION)
    const sizeId = searchParams?.get(SIZE_ID)
    const categoryId = searchParams?.get(CATEGORY_ID)
    const brandId = searchParams?.get(BRAND_ID)
    const search = searchParams?.get(SEARCH)

    const filter = {
      page: page ? Number(page) : PAGE_DEFAULT,
      pageSize: pageSize ? Number(pageSize) : PAGE_SIZE_DEFAULT,
      sort: [],
      status: stringToStringArray(status),
      soldPriceFrom: getFilterFromQuery(soldPriceMin) as number,
      soldPriceTo: getFilterFromQuery(soldPriceMax) as number,
      soldDateFrom: formatDate({
        date: soldDateStart,
        format: MiscConstant.DATE_FORMAT_ISO,
        fallback: null,
      }),
      soldDateTo: formatDate({
        date: soldDateEnd,
        format: MiscConstant.DATE_FORMAT_ISO,
        fallback: null,
      }),
      sizeID: stringToNumberArray(sizeId),
      categoryID: stringToNumberArray(categoryId),
      brandID: stringToNumberArray(brandId),
      search,
      ...getConditionFilter(condition),
    } as TSellerConsignmentFilter

    setFilter(filter)
  }, [searchParams, setFilter, mapStatusQuery])
}

export default useConsignmentHistorySearchParams
