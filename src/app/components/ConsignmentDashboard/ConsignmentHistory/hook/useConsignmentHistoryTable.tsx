import { Text } from "@kickavenue/ui/components"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import { formatCurrencyStripe, formatDateObj } from "@utils/misc"
import { TTableColumn } from "types/table.type"
import HistoryTableRowActions from "@components/ConsignmentDashboard/ConsignmentHistory/HistoryTableRowActions"
import { TConsignmentTransactionResponse } from "types/sellerConsignment.type"
import { getUsSize } from "@utils/size.utils"
import classes from '../ConsignmentHistoryTable.module.scss'

import ConsignmentHistoryStatus from "../ConsignmentHistoryStatus"
import { cx } from "class-variance-authority"

const columnWidth = {
	consignmentId: 150,
	productDetails: 300,
	size: 72,
	conditions: 108,
	soldPrice: 148,
	soldDate: 140,
	status: 128,
	actions: 67,
}

const useConsignmentHistoryTable = () => {
	const formatDate = (date?: string | null) => {
		if (!date) return "-"
		return formatDateObj(new Date(date))
	}

	const columns = [
		{
			key: "consignmentId",
			title: "Invoice Number",
			width: columnWidth.consignmentId,
			headerClassName: classes['sticky-header-1'],
			contentClassName: classes['sticky-content-1'],
			render: (record: TConsignmentTransactionResponse) => {
				return (
					<>
						<Text size="sm" state="primary" type="regular">
							{record.transactionDetail.invoiceNumber || "-"}
						</Text>
						<Text size="xs" state="primary" type="regular" style={{ color: "#666" }}>
							{record.sellerListingStock?.consignmentId || "-"}
						</Text>
					</>
				)
			},
		},
		{
			key: "product_detail",
			title: "Product Details",
			headerClassName: classes['sticky-header-2'],
			contentClassName: classes['sticky-content-2'],
			width: columnWidth.productDetails,
			render: (record: TConsignmentTransactionResponse) => {
				return <ProductDetailColumn listingItem={record.sellerListingStock} />
			},
			sorter: () => { },
		},
		{
			key: "size",
			dataIndex: "size",
			title: "Size",
			width: columnWidth.size,
			render: (record: TConsignmentTransactionResponse) =>
				getUsSize(record.sellerListingStock?.size),
			sorter: () => { },
		},
		{
			key: "condition",
			title: "Conditions",
			width: columnWidth.conditions,
			render: (record: TConsignmentTransactionResponse) => (
				<ConditionColumn
					itemCondition={record.sellerListingStock?.itemCondition}
					packagingCondition={record.sellerListingStock?.packagingCondition}
				/>
			),
			sorter: () => { },
		},
		{
			key: "soldPrice",
			title: "Sold Price",
			width: columnWidth.soldPrice,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (record: TConsignmentTransactionResponse) => {
				const price = record.transactionDetail?.price
				if (!price || !price?.minUnitVal) return "-"

				return formatCurrencyStripe({ price })
			},
			sorter: () => { },
		},
		{
			key: "soldDate",
			title: "Sold Date",
			width: columnWidth.soldDate,
			render: (record: TConsignmentTransactionResponse) =>
				formatDate(record.transactionDetail?.createdAt),
			sorter: () => { },
		},
		{
			key: "sellerStatus",
			title: "Status",
			width: columnWidth.status,
			render: (record: TConsignmentTransactionResponse) => (
				<ConsignmentHistoryStatus record={record} />
			),
		},
		{
			key: "actions",
			title: "Actions",
			width: columnWidth.actions,
			headerClassName: `${classes['sticky-right-header-1']} text-center [&>div]:!justify-center`,
			contentClassName: cx(classes['sticky-right-content-1'], 'dropdown-options'),
			render: (record: TConsignmentTransactionResponse) => (
				<HistoryTableRowActions record={record} />
			),
		},
	] as TTableColumn[]

	return { columns }
}

export default useConsignmentHistoryTable
