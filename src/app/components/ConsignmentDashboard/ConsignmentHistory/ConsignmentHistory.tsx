import { Space } from "@kickavenue/ui/components"
import { useEffect, useMemo, useState } from "react"
import { useSearchParams } from "next/navigation"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import { TConsignmentDashboardTabEnum } from "types/misc.type"
import { ListingItemConstant } from "@constants/listingitem.constant"
import FilterDashboard from "@components/FilterDashboard"
import { formatListingStatus } from "@utils/listingItem"
import { ESellerConsignmentStatus } from "types/sellerConsignment.type"
import usePopulateFilterData from "@app/hooks/usePopulateFilterData"

import useConsignmentHistorySearchParams from "./hook/useConsignmentHistorySearchParams"
import ConsignmentHistoryTable from "./ConsignmentHistoryTable"
import ConsignmentHistoryDetailModal from "./ConsignmentHistoryDetailModal"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const { History } = TConsignmentDashboardTabEnum
const { TAB } = ListingItemConstant.FILTER_FIELDS

const { Completed, Returned, Canceled, Failed } = ESellerConsignmentStatus

const ConsignmentHistory = () => {
  const searchParams = useSearchParams()
  const { handleBulkChangeQuery } = useURLQuery()

  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams?.get(TAB) || History

  const renderFilterDashboard =
    openFilter && selectedTab === History ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  const status = useMemo(
    () =>
      formatListingStatus([Completed, Returned, Canceled, Failed]) as string[],
    [],
  )

  useConsignmentHistorySearchParams()

  usePopulateFilterData({ status })

  useEffect(() => {
    if (searchParams?.get(PAGE) && searchParams?.get(PAGE_SIZE)) return
    handleBulkChangeQuery({
      [PAGE]: PAGE_DEFAULT,
      [PAGE_SIZE]: PAGE_SIZE_DEFAULT,
    })
  }, [handleBulkChangeQuery, searchParams])

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <Space size="lg" type="margin" direction="y" />
      <ConsignmentHistoryTable />
      <ConsignmentHistoryDetailModal />
      {renderFilterDashboard}
    </>
  )
}

export default ConsignmentHistory
