import { Space } from "@kickavenue/ui/components"
import { useCallback } from "react"
import Table from "@components/shared/Table"
import TablePagination from "@components/shared/Table/TablePagination"
import { useConsignmentHistoryStore } from "stores/consignmentHistoryStore"
import { TSorter } from "types/table.type"
import { TSellerConsignmentFilter } from "types/sellerConsignment.type"
import SpinnerLoading from "@components/shared/SpinnerLoading"

import styles from "./ConsignmentHistoryTable.module.scss"
import useFetchConsignmentHistory from "./hook/useFetchConsignmentHistory"
import useConsignmentHistoryTable from "./hook/useConsignmentHistoryTable"
import useConsignmentHistoryPagination from "./hook/useConsignmentHistoryPagination"

const ConsignmentHistoryTable = () => {
  const { columns } = useConsignmentHistoryTable()
  const { list, setFilter, filter } = useConsignmentHistoryStore()
  const { isLoading } = useFetchConsignmentHistory()
  const paging = useConsignmentHistoryPagination()
  const handleTableChange = useCallback(
    (sorter: TSorter) => {
      const field = sorter.field
      setFilter({
        ...(filter as TSellerConsignmentFilter),
        sort: [`${field},${sorter.order}`],
      })
    },
    [filter, setFilter],
  )

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(80vh-250px)]" />
  }

  return (
    <>
      <div className="max-h-[580px] min-h-[calc(80vh-260px)] max-w-full overflow-x-auto">
        <Table
          columns={columns}
          dataSource={list}
          rowKey="id"
          className={styles.table}
          loading={isLoading}
          onTableChange={handleTableChange}
        />
      </div>
      <Space size="lg" type="margin" direction="y" />
      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default ConsignmentHistoryTable
