import { Badge, Divider, Text } from "@kickavenue/ui/dist/src/components"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import {
  getConsignmentHistoryStatus,
  getConsignmentHistoryStatusMap,
} from "@utils/sellerConsignment.utils"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import { getListingCondition } from "@utils/selling"
import ModalRoundedContent from "@components/shared/ModalParts/ModalRoundedContent"
import CodeWithCopyButton from "@components/shared/CodeWithCopyButton"
import ModalRoundedContentItem from "@components/shared/ModalParts/ModalRoundedContentItem"
import { formatCurrencyStripe, formatDateWIB } from "@utils/misc"

import useFetchSellerTransactionById from "../hook/useFetchSellerTransactionById"

const ConsignmentHistoryDetailModalContent = () => {
  const { sellerListingId } = useConsignmentModalStore()

  const { data: sellerTransaction, isLoading } = useFetchSellerTransactionById({
    id: sellerListingId,
  })

  const status = getConsignmentHistoryStatus(sellerTransaction)
  const map = getConsignmentHistoryStatusMap(status)

  if (isLoading) return <SpinnerLoading className="h-[602px]" />

  return (
    <div className="flex h-[602px] flex-col gap-lg p-lg">
      <div className="flex justify-between">
        <Text size="base" type="bold" state="primary">
          History Status
        </Text>
        <Badge text={map?.text} type={map?.badgeType} />
      </div>
      <Divider orientation="horizontal" />
      <div className="flex flex-col gap-sm">
        <Text size="sm" type="bold" state="primary">
          Product Detail
        </Text>
        <div className="flex w-full justify-between rounded-sm border border-solid border-gray-w-80 p-base">
          <ProductDetailPreview
            listing={sellerTransaction?.sellerListingStock}
            listingCondition={getListingCondition(
              sellerTransaction?.sellerListingStock,
            )}
          />
        </div>
        <ModalRoundedContent title="Listing Detail">
          <div className="flex w-full justify-between px-base">
            <Text size="sm" state="primary" type="regular">
              Invoice Number
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <CodeWithCopyButton
                code={
                  sellerTransaction?.transactionDetail?.invoiceNumber as string
                }
                message="Invoice Number successfully copied!"
              />
            </div>
          </div>
          <div className="flex w-full justify-between px-base">
            <Text size="sm" state="primary" type="regular">
              Consignment ID
            </Text>
            <div className="flex items-center gap-x-xxxs">
              <CodeWithCopyButton
                code={
                  sellerTransaction?.sellerListingStock?.consignmentId as string
                }
                message="Consignment ID successfully copied!"
              />
            </div>
          </div>
        </ModalRoundedContent>
        <ModalRoundedContent title="Listing Detail">
          <ModalRoundedContentItem
            text="Sale Date"
            value={formatDateWIB(
              sellerTransaction?.transactionDetail?.createdAt,
            )}
          />
          <ModalRoundedContentItem
            text="Listing Price"
            value={formatCurrencyStripe({
              price: sellerTransaction?.sellerListingStock?.sellingPrice,
            })}
          />
        </ModalRoundedContent>
      </div>
    </div>
  )
}

export default ConsignmentHistoryDetailModalContent
