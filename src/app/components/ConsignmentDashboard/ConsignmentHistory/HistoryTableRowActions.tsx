import {
	IconKebabMenuVertical,
	IconViewDetailsOutline,
} from "@kickavenue/ui/components"
import { useCallback } from "react"
import {
	ESellerConsignmentStatus,
	TConsignmentTransactionResponse,
} from "types/sellerConsignment.type"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { getConsignmentHistoryStatus } from "@utils/sellerConsignment.utils"

import { TConsignmentHistoryAction } from "./consignmentHistory.type"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"

const { ViewDetails } = TConsignmentHistoryAction

const {
	Completed,
	EnRouteToBuyer,
	ApprovedAsOnlineListing,
	Returned,
	Canceled,
	Refunded,
	ConsignmentCanceled,
	ConsignmentRejected,
} = ESellerConsignmentStatus

const STATUS_ACTIONS_MAP: Partial<
	Record<ESellerConsignmentStatus, TConsignmentHistoryAction[]>
> = {
	// Completed
	[Completed]: [ViewDetails],
	[EnRouteToBuyer]: [ViewDetails],
	[ApprovedAsOnlineListing]: [ViewDetails],

	// Returned
	[Returned]: [ViewDetails],

	// Canceled
	[Canceled]: [ViewDetails],
	[ConsignmentCanceled]: [ViewDetails],

	// Failed
	[Refunded]: [ViewDetails],
	[ConsignmentRejected]: [ViewDetails],
}

const { CONSIGNMENT_HISTORY_DETAIL } = ModalConstant.MODAL_IDS

const HistoryTableRowActions = ({
	record,
}: {
	record: TConsignmentTransactionResponse
}) => {
	const { setOpen } = useModalStore()
	const { setSellerListingId } = useConsignmentModalStore()

	const handleOnItemSelect = () => {
		setSellerListingId(record.sellerListingStock.id)
		setOpen(true, CONSIGNMENT_HISTORY_DETAIL)
	}

	const isAllowedStatus = useCallback(
		(action: TConsignmentHistoryAction) =>
			STATUS_ACTIONS_MAP[
				getConsignmentHistoryStatus(record) as ESellerConsignmentStatus
			]?.includes(action),
		[record],
	)

	const options = [
		{
			text: "View Details",
			value: ViewDetails,
			iconLeading: <IconViewDetailsOutline width={16} height={16} />,
			show: () => isAllowedStatus(ViewDetails),
		},
	].filter((option) => option.show())

	return (
		<div className="flex justify-center gap-xs">
			<DropdownDynamicChild
				options={options}
				placement="rightBottom"
				onItemSelect={handleOnItemSelect}
			>
				<IconKebabMenuVertical />
			</DropdownDynamicChild>
		</div>
	)
}

export default HistoryTableRowActions
