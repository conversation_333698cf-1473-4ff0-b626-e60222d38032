import { Space } from "@kickavenue/ui/dist/src/components"
import { useQueryClient } from "@tanstack/react-query"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import UpdatePriceOptions from "@components/shared/UpdatePriceOptions"
import UpdatePriceInput from "@components/shared/UpdatePriceInput"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import UpdatePriceAlert from "@components/shared/UpdatePriceAlert"
import useBulkUpdatePriceSellerListing from "@components/SellingDashboard/hook/useBulkUpdatePriceSellerListing"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"

import ConsignmentUpdateListingPriceTable from "./ConsignmentUpdateListingPriceTable"
import useConsignmentUpdateListingPriceOptions from "./hook/useConsignmentUpdateListingPriceOptions"

const { CONSIGNMENT_UPDATE_LISTING_PRICE } = ModalConstant.MODAL_IDS

const ConsignmentUpdateListingPriceModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setShowToast } = useToast()
  const queryClient = useQueryClient()

  const { options, handleOptionSelect } =
    useConsignmentUpdateListingPriceOptions()

  const {
    updatePrice,
    updatePriceAction,
    selectedRowKeys,
    setUpdatePrice,
    setUpdatePriceAction,
    setSelectedRowKeys,
  } = useConsignmentActiveStore()

  const { mutate, isPending } = useBulkUpdatePriceSellerListing({
    onSuccess: async () => {
      setOpen(false, CONSIGNMENT_UPDATE_LISTING_PRICE)
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_CONSIGNMENT_ACTIVE],
      })
      setUpdatePrice(null)
      setUpdatePriceAction("")
      setSelectedRowKeys([])
      setShowToast(true, "Update listing price successfully", "success")
    },
    onError: (error) => {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })

  const disabledButton =
    Math.abs((updatePrice || 0) % 10000) !== 0 ||
    !updatePrice ||
    isPending ||
    String(updatePrice) === "-"

  const handleClearState = () => {
    setUpdatePrice(null)
    setUpdatePriceAction("")
  }

  const handleClose = () => {
    setOpen(false, CONSIGNMENT_UPDATE_LISTING_PRICE)
    handleClearState()
  }

  const handleConfirm = () => {
    mutate({
      ids: selectedRowKeys,
      price: Number(updatePrice),
      updatePriceAction,
    })
  }

  if (!open || modalId !== CONSIGNMENT_UPDATE_LISTING_PRICE) {
    return null
  }

  return (
    <Modal
      modalId={CONSIGNMENT_UPDATE_LISTING_PRICE}
      className="sm:!max-w-[1036px]"
    >
      <HeaderModal onClose={handleClose} title="Update Listing Price" />
      <Space size="md" direction="y" type="margin" />
      <div className="h-[667px] overflow-y-auto">
        <UpdatePriceOptions
          options={options}
          handleOptionSelect={handleOptionSelect}
          renderUpdatePriceInput={() => (
            <UpdatePriceInput
              setUpdatePrice={setUpdatePrice}
              updatePriceAction={updatePriceAction}
              updatePrice={updatePrice}
            />
          )}
          renderUpdatePriceAlert={() => (
            <UpdatePriceAlert
              updatePriceAction={updatePriceAction}
              updatePrice={updatePrice}
              selectedRowKeys={selectedRowKeys}
            />
          )}
        />
        <ConsignmentUpdateListingPriceTable />
      </div>
      <ModalCancelConfirm
        disableConfirm={disabledButton}
        onConfirm={handleConfirm}
        onCancel={handleClose}
      />
    </Modal>
  )
}

export default ConsignmentUpdateListingPriceModal
