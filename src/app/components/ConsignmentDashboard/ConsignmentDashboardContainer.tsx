"use client"

import {
  But<PERSON>,
  Heading,
  IconAddOutline,
  Space,
} from "@kickavenue/ui/components"
import AlertViewInDesktop from "@shared/AlertViewInDesktop"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import SellerGuard from "@components/shared/SellerGuard"

import ConsignmentSummaries from "./ConsignmentSummaries"
import ConsignmentDashboardTabs from "./ConsignmentDashboardTabs"
import ConsignmentDashboardTabContent from "./ConsignmentDashboardTabContent"

const ConsignmentDashboardContainer = () => {
  const { goToPage, isLoading } = useChangePage()

  return (
    <SellerGuard>
      <div className="p-sm lg:p-lg">
        <div className="flex flex-wrap items-center justify-between gap-sm">
          <Heading heading="4" textStyle="bold">
            Consignment Dashboard
          </Heading>
          <Button
            size="md"
            variant="secondary"
            IconLeft={IconAddOutline}
            disabled={isLoading?.[PageRouteConstant.SELL_CONSIGNMENT]}
            onClick={() => goToPage(PageRouteConstant.SELL_CONSIGNMENT)}
          >
            Create Consignment Request
          </Button>
        </div>
        <Space size="lg" type="margin" direction="y" />
        <AlertViewInDesktop />
        <ConsignmentSummaries />
        <Space size="lg" type="margin" direction="y" />
        <ConsignmentDashboardTabs />
        <Space size="lg" type="margin" direction="y" />
        <ConsignmentDashboardTabContent />
      </div>
    </SellerGuard>
  )
}

export default ConsignmentDashboardContainer
