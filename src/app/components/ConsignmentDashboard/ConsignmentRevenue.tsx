import { Divider } from "@kickavenue/ui/dist/src/components"
import TTextProps from "@kickavenue/ui/dist/src/components/Text/Text.type"
import ModalSellingSummary from "@components/shared/ModalParts/ModalSellingSummary"
import ModalSummaryItem from "@components/shared/ModalParts/ModalSummaryItem"
import ModalSummaryTotal from "@components/shared/ModalParts/ModalSummaryTotal"
import Spinner from "@components/shared/Spinner"
import {
  calculatePlatformFee,
  getPlatformFeePercentage,
} from "@utils/fee.utils"
import { formatPrice, getStripAmount } from "@utils/misc"
import { TConsignmentSellerStockResponse } from "types/sellerConsignment.type"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

const ConsignmentRevenue = ({
  sellerStock,
}: {
  sellerStock?: TConsignmentSellerStockResponse
}) => {
  const { getMyPlatformFee, isLoading } = useGetPlatformFee({
    categoryId: sellerStock?.sellerListingStock.item.category?.id,
  })

  const sellingPrice = getStripAmount(
    sellerStock?.sellerListingStock?.sellingPrice,
  )
  const calcFee = calculatePlatformFee(sellingPrice, getMyPlatformFee())

  const summaries = [
    {
      text: "Listing Price",
      value: formatPrice(sellingPrice, null, "IDR"),
    },
    {
      text: `Platform Fee (${getPlatformFeePercentage(getMyPlatformFee())}%)`,
      value: formatPrice(calcFee, null, "IDR"),
      isMinus: true,
      valueProps: { state: "danger" } as Partial<TTextProps>,
    },
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center rounded-sm border border-solid border-gray-w-80 p-base">
        <Spinner />
      </div>
    )
  }

  return (
    <div className="">
      <ModalSellingSummary title="Listing Revenue">
        {summaries.map((summary) => (
          <ModalSummaryItem key={summary.text} {...summary} />
        ))}
        <Divider orientation="horizontal" />
        <ModalSummaryTotal
          text="Total Revenue Sales"
          value={formatPrice(sellingPrice - calcFee, null, "IDR")}
        />
      </ModalSellingSummary>
    </div>
  )
}

export default ConsignmentRevenue
