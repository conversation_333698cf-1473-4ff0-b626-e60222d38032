import { Space } from "@kickavenue/ui/components"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import ModalAlert from "@components/shared/ModalParts/ModalAlert"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import { useSellingModalStore } from "stores/sellingModalStore"

import ConsignmentTakeOutTable from "./ConsignmentTakeOutTable"

const { CONSIGNMENT_TAKE_OUT } = ModalConstant.MODAL_IDS

const ConsignmentTakeOutModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setOpenConsignTakeOutConfirm } = useSellingModalStore()
  const { selectedRowKeys } = useConsignmentActiveStore()
  const alertText = `These ${selectedRowKeys?.length} items will be take out from consignment.`

  const handleOnConfirm = () => {
    setOpen(false, CONSIGNMENT_TAKE_OUT)
    setOpenConsignTakeOutConfirm(true)
  }

  if (!open || modalId !== CONSIGNMENT_TAKE_OUT) return null

  return (
    <Modal modalId={CONSIGNMENT_TAKE_OUT} className="sm:!max-w-[1036px]">
      <HeaderModal
        onClose={() => setOpen(false, CONSIGNMENT_TAKE_OUT)}
        title="Take Out Consignment"
      />
      <Space size="md" direction="y" type="margin" />
      <div className="h-[667px] overflow-y-auto">
        <ModalAlert text={alertText} />
        <ConsignmentTakeOutTable />
      </div>
      <ModalCancelConfirm
        onCancel={() => setOpen(false, CONSIGNMENT_TAKE_OUT)}
        onConfirm={handleOnConfirm}
        disableConfirm={false}
      />
    </Modal>
  )
}

export default ConsignmentTakeOutModal
