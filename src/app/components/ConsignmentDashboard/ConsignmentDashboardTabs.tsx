import { Tab } from "@kickavenue/ui/dist/src/components"
import { useRouter, useSearchParams } from "next/navigation"
import { getConsignmentDashboardTabs } from "@utils/misc"
import { TConsignmentDashboardTab } from "types/misc.type"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"

import styles from "./ConsignmentDashboardTabs.module.scss"

const ConsignmentDashboardTabs = () => {
  const search = useSearchParams()
  const router = useRouter()
  const activeTab =
    (search?.get("tab") as TConsignmentDashboardTab) || "pending"
  const isDefaultTab = (key: string) => key === "pending"
  const { setSelectedRowKeys } = useConsignmentActiveStore()
  const handleClick = (item: {
    key: TConsignmentDashboardTab
    value: string
  }) => {
    const url = isDefaultTab(item.key)
      ? "/profile/consignment"
      : `?tab=${item.key}`
    router.push(url)
    if (item.key !== activeTab) {
      setSelectedRowKeys([])
    }
  }
  return (
    <div className={styles.wrapper}>
      <Tab className={styles.tab}>
        {getConsignmentDashboardTabs().map((item) => (
          <button
            type="button"
            key={item.key}
            data-active={activeTab === item.key}
            onClick={() => handleClick(item)}
          >
            {item.value}
          </button>
        ))}
      </Tab>
    </div>
  )
}

export default ConsignmentDashboardTabs
