import { Divider } from "@kickavenue/ui/dist/src/components"
import TTextProps from "@kickavenue/ui/dist/src/components/Text/Text.type"
import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import ModalSummaryTotal from "@components/shared/ModalParts/ModalSummaryTotal"
import ModalUniqueID from "@components/shared/ModalParts/ModalUniqueID"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { formatPrice, getStripAmount } from "@utils/misc"
import {
  calculatePlatformFee,
  getPlatformFeePercentage,
} from "@utils/fee.utils"
import ModalSellingSummary from "@components/shared/ModalParts/ModalSellingSummary"
import ModalSummaryItem from "@components/shared/ModalParts/ModalSummaryItem"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"
import { SellerListing } from "types/sellerListing"

const ConsignmentSellNowModalContent = ({
  listing,
  isLoading,
}: {
  listing: SellerListing
  isLoading: boolean
}) => {
  const { getMyPlatformFee } = useGetPlatformFee({
    categoryId: listing?.item?.category?.id,
  })

  const offerAmount = getStripAmount(listing?.highestOfferAmount) || 0
  const calcFee = calculatePlatformFee(offerAmount, getMyPlatformFee())

  const summaries = [
    {
      text: "Offer Price",
      value: formatPrice(offerAmount, null, "IDR"),
    },
    {
      text: `Platform Fee (${getPlatformFeePercentage(getMyPlatformFee())}%)`,
      value: formatPrice(calcFee, null, "IDR"),
      isMinus: true,
      valueProps: { state: "danger" } as Partial<TTextProps>,
    },
  ]

  if (isLoading) {
    return <SpinnerLoading className="h-[331px]" />
  }

  return (
    <div className="flex h-full flex-col gap-lg overflow-y-auto p-lg">
      <div className="flex flex-col gap-sm">
        <div className="">
          <ModalProduct
            listing={listing as SellerListing}
            itemName={listing?.item?.name}
            size={listing?.size}
          />
        </div>
        <ModalUniqueID
          text="Consignment ID"
          value={listing?.consignmentId || "-"}
          copiedMessage="Consignment ID copied!"
        />
      </div>
      <Divider orientation="horizontal" />
      <div className="">
        <ModalSellingSummary>
          {summaries.map((summary) => (
            <ModalSummaryItem key={summary.text} {...summary} />
          ))}
          <Divider orientation="horizontal" />
          <ModalSummaryTotal
            text="Total Revenue Sales"
            value={formatPrice(offerAmount - calcFee, null, "IDR")}
          />
        </ModalSellingSummary>
      </div>
    </div>
  )
}

export default ConsignmentSellNowModalContent
