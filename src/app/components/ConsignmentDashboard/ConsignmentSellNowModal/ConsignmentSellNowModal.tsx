import HeaderModal from "@shared/HeaderModal"
import Modal from "@shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"

import ConsignmentSellNowModalContent from "./ConsignmentSellNowModalContent"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import { Button } from "@kickavenue/ui/components/Button"
import useFetchSellerListingById from "@app/hooks/useFetchSellerListingById"
import { useSearchParams } from "next/navigation"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import usePostSellNowMutation from "@app/hooks/usePostSellNowMutation"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"
import { SellerListing } from "types/sellerListing"

const { CONSIGNMENT_SELL_NOW } = ModalConstant.MODAL_IDS

const ConsignmentSellNowModal = () => {
  const replaceQueryParams = useReplaceQueryParams()
  const { setShowToast } = useToast()
  const { setOpen, open, modalId } = useModalStore()
  const searchParams = useSearchParams()
  const sellingId = searchParams?.get("sellingId")

  const { data: listing, isLoading } = useFetchSellerListingById(
    sellingId ? Number(sellingId) : undefined,
  )

  const onClose = () => {
    setOpen(false, CONSIGNMENT_SELL_NOW)
    replaceQueryParams({}, ["sellingId", "itemId", "sizeId"])
  }

  const { mutate: submitSellNow, isPending: isPendingSubmitSellNow } =
    usePostSellNowMutation({
      onError(error) {
        setShowToast(true, getApiErrorMessage(error), "danger")
      },
      onSuccess: () => {
        setShowToast(true, "Sell Now success", "success")
        onClose()
      },
    })

  const onClickSellNow = () => {
    submitSellNow(Number(sellingId))
  }

  if (!open || modalId !== CONSIGNMENT_SELL_NOW) return null

  return (
    <Modal modalId={CONSIGNMENT_SELL_NOW} onClose={onClose}>
      <HeaderModal onClose={onClose} title="Sell Now" />
      <ConsignmentSellNowModalContent
        listing={listing as SellerListing}
        isLoading={isLoading}
      />
      <ModalFooter>
        <div className="col-span-12">
          <Button
            size="lg"
            variant="primary"
            disabled={isLoading}
            className="!w-full"
            onClick={onClickSellNow}
          >
            {isPendingSubmitSellNow ? "Selling..." : "Sell Now"}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}

export default ConsignmentSellNowModal
