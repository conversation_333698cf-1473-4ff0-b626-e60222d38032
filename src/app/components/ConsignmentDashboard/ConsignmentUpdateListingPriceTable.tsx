/* eslint-disable @typescript-eslint/naming-convention */

import { Text } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import useUpdateListingPriceTable from "@app/hooks/useUpdateListingPriceTable"
import BulkActionTable from "@shared/BulkActionTable"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { formatPriceMinUnitVal } from "@utils/misc"
import { formatCurrency } from "@utils/separator"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import { TListingItem } from "types/listingItem.type"
import { TTableColumn } from "types/table.type"

const width = {
  consignmentId: 152,
  productDetail: 224,
  SKU: 108,
  size: 64,
  standardLowestAsk: 148,
  highestOffer: 148,
  listingPrice: 148,
  newListingPrice: 148,
  priceChange: 148,
}

// eslint-disable-next-line max-lines-per-function
const ConsignmentUpdateListingPriceTable = () => {
  const {
    updatePrice,
    updatePriceAction,
    selectedRowKeys,
    consignmentAllData,
  } = useConsignmentActiveStore()
  const { getNewListingPriceColumn, getPriceChangeColumn } =
    useUpdateListingPriceTable(Number(updatePrice), updatePriceAction)
  let columns = [
    {
      key: "consignmentId",
      title: "Consignment ID",
      headerClassName: styles["sticky-header-1"],
      contentClassName: styles["sticky-content-1"],
      width: width.consignmentId,
      render: (record: TListingItem) => {
        return (
          <Text size="sm" state="primary" type="regular">
            {record.consignmentId}
          </Text>
        )
      },
      sorter: () => {},
    },
    {
      key: "productDetails",
      title: "Product Name",
      width: width.productDetail,
      headerClassName: cx(styles["sticky-header-2"], {
        "!left-[178px]":
          updatePriceAction === "update-to" || !updatePriceAction,
        "!left-[158px]": updatePriceAction === "update-by",
      }),
      contentClassName: cx(styles["sticky-content-2"], {
        "!left-[178px]":
          updatePriceAction === "update-to" || !updatePriceAction,
        "!left-[158px]": updatePriceAction === "update-by",
      }),
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.item?.name.localeCompare(b?.item?.name),
      defaultSortOrder: "ascend",
      render: (record: TListingItem) => <>{record?.item?.name}</>,
    },
    {
      key: "SKU",
      title: "SKU",
      width: width.SKU,
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.item?.skuCode.localeCompare(b?.item?.skuCode),
      render: (record: TListingItem) => <>{record?.item?.skuCode}</>,
    },
    {
      key: "size",
      title: "Size",
      width: width.size,
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.size?.us?.localeCompare(b?.size?.us),
      render: (record: TListingItem) => <>US {record?.size?.us}</>,
    },
    {
      key: "standardLowestAsk",
      title: "Standard Lowest Ask",
      width: width.standardLowestAsk,
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: () => formatCurrency(0, ",", "IDR"),
    },
    {
      key: "highestOffer",
      title: "Highest Offer",
      width: width.highestOffer,
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TListingItem) =>
        formatCurrency(record.highestOffer || 0, ",", "IDR"),
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.highestOffer - b?.highestOffer,
    },
    {
      key: "listingPrice",
      title: "Old Listing Price",
      width: width.listingPrice,
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TListingItem) =>
        formatCurrency(
          formatPriceMinUnitVal(record?.sellingPrice?.minUnitVal || 0) || 0,
          ",",
          "IDR",
        ),
      sorter: (a: TListingItem, b: TListingItem) =>
        formatPriceMinUnitVal(a?.sellingPrice?.minUnitVal || 0) -
        formatPriceMinUnitVal(b?.sellingPrice?.minUnitVal || 0),
    },
    {
      key: "newListingPrice",
      title: "New Listing price",
      width: width.newListingPrice,
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: getNewListingPriceColumn,
    },
  ] as TTableColumn[]

  if (updatePriceAction === "update-by") {
    const col: TTableColumn = {
      key: "priceChange",
      title: "Price Change",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: getPriceChangeColumn,
      width: width.priceChange,
    }
    const [columnsWithoutLastItem, columnsLastItem] = [
      columns.slice(0, -1),
      columns[columns.length - 1],
    ]
    columns = [...columnsWithoutLastItem, col, columnsLastItem]
  }

  return (
    <BulkActionTable
      columns={columns}
      data={consignmentAllData}
      selectedRowKeys={selectedRowKeys}
      tableClassName="!w-[1340px]"
    />
  )
}

export default ConsignmentUpdateListingPriceTable
