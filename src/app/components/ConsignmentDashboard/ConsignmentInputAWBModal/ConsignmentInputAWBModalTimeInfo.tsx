import { Badge, Text } from "@kickavenue/ui/dist/src/components"
import { getConsignmentDeadlineFromStatus } from "@utils/sellerConsignment.utils"
import {
  TConsignmentSellerStockResponse,
  EConsignmentStatusTrack,
} from "types/sellerConsignment.type"
import { formatDate } from "@utils/misc"
import { IconTimeBold } from "@kickavenue/ui/dist/src/components/icons/IconTimeBold"

const ConsignmentInputAWBModalTimeInfo = ({
  sellerStock,
  status,
}: {
  sellerStock?: TConsignmentSellerStockResponse
  status?: EConsignmentStatusTrack
}) => {
  const { time, deadline } = getConsignmentDeadlineFromStatus(
    sellerStock,
    status,
  )
  const formatDeadline = () => {
    if (!deadline) return "-"
    return `${formatDate(deadline, "DD MMMM YYYY, HH:mm")} WIB`
  }
  return (
    <div className="flex justify-between gap-sm">
      <div className="flex flex-col">
        <Text size="base" type="bold" state="primary">
          Input AWB Detail Before
        </Text>
        <Text size="sm" type="regular" state="secondary">
          {formatDeadline()}
        </Text>
      </div>
      <div className="flex items-center">
        <Badge
          type="negative"
          text={time?.slice(0, -5) || "-"}
          size="md"
          iconLeft={IconTimeBold}
          className="[&>svg]:!text-[#FF2323]"
        />
      </div>
    </div>
  )
}

export default ConsignmentInputAWBModalTimeInfo
