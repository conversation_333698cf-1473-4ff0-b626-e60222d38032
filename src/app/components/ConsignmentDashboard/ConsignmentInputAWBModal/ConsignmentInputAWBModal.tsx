import { Space } from "@kickavenue/ui/components"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"

import ConsignmentInputAWBModalContent from "./ConsignmentInputAWBModalContent"

const { CONSIGNMENT_INPUT_AWB } = ModalConstant.MODAL_IDS

const ConsignmentInputAWBModal = () => {
  const { setOpen, modalId, open } = useModalStore()

  if (!open || modalId !== CONSIGNMENT_INPUT_AWB) return null

  return (
    <Modal modalId={CONSIGNMENT_INPUT_AWB}>
      <HeaderModal
        onClose={() => setOpen(false, CONSIGNMENT_INPUT_AWB)}
        title="Input AWB"
      />
      <Space size="md" direction="y" type="margin" />
      <ConsignmentInputAWBModalContent />
    </Modal>
  )
}

export default ConsignmentInputAWBModal
