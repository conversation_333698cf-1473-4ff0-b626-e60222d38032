import { useCallback } from "react"
import { useForm } from "react-hook-form"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { ConsignmentSubmitAWB } from "@application/usecases/consignmentSubmitAWB"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"
import { TConsignmentInputAWBForm } from "types/sellerConsignment.type"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

const useEnterAWBConsignment = () => {
  const { setOpen } = useModalStore()
  const { sellerStock } = useConsignmentModalStore()
  const queryClient = useQueryClient()

  const form = useForm<TConsignmentInputAWBForm>({
    defaultValues: {
      sellerCourier: null,
      sellerAwb: null,
    },
  })

  const submitAWB = async (data: TConsignmentInputAWBForm) => {
    const r = new SellerConsignmentApiRepository()
    const u = new ConsignmentSubmitAWB(r)
    const res = await u.execute(
      data,
      sellerStock?.sellerConsignment?.id as number,
    )
    return res
  }

  const { mutate, isPending } = useMutation({
    mutationFn: submitAWB,
    onSuccess: () => {
      setOpen(false, ModalConstant.MODAL_IDS.CONSIGNMENT_INPUT_AWB)
      queryClient.resetQueries({
        queryKey: [QueryKeysConstant.GET_SELLER_STOCKS],
      })
    },
  })

  const onFormValid = useCallback(
    (values: TConsignmentInputAWBForm) => {
      mutate(values)
    },
    [mutate],
  )

  return { form, onFormValid, isPending }
}

export default useEnterAWBConsignment
