import React from "react"
import {
  IconConsignmentBulkColor,
  IconMoneys,
} from "@kickavenue/ui/dist/src/components"
import { useSellConsignmentStore } from "stores/sellConsignmentStore"
import { useFormSellConsignmentStore } from "stores/formSellConsignmentStore"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"

import ButtonNext from "./ButtonNext"
import Card from "./Card"

export interface SellWithUsProps {
  handleNext: () => void
}

const SellWithUs = (props: SellWithUsProps) => {
  const { handleNext } = props
  const { member } = useMemberStore()
  const { option, setOption, resetState } = useSellConsignmentStore()
  const { setField } = useFormSellConsignmentStore()
  const handleConsignmentOptionSelect = () => {
    resetState()
    setField("isConsignment", true)
    setOption("consignment")
  }
  const handleSellingOptionSelect = () => {
    resetState()
    setField("isConsignment", false)
    setOption("selling")
  }

  console.log(option, "ini apa")
  return (
    <div className="flex flex-col items-center gap-lg lg:flex-row">
      <button type="button" onClick={handleSellingOptionSelect}>
        <Card
          icon={<IconMoneys className="size-xl scale-100 text-red" />}
          selected={option === "selling"}
          body="Sell on our marketplace. Set your price, earn more."
          heading="Selling"
        />
      </button>
      <button type="button" onClick={handleConsignmentOptionSelect}>
        <Card
          icon={
            <IconConsignmentBulkColor className="size-xl scale-100 text-red" />
          }
          selected={option === "consignment"}
          body="We sell and store it for you. Hassle-free with guaranteed express listings."
          heading="Consignment"
        />
      </button>
      <ButtonNext
        onClick={() => {
          handleNext()
          event({
            action: "product_listing_started",
            params: {
              type: option,
            },
            userId: String(member?.id || ""),
          })
        }}
      >
        Next
      </ButtonNext>
    </div>
  )
}

export default SellWithUs
