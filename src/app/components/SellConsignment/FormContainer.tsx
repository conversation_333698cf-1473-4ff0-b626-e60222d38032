/* eslint-disable max-lines-per-function */
import { Divider, Heading, Space, Text } from "@kickavenue/ui/components"
import { useMemo } from "react"
import { useMutation } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { snakeCase } from "lodash"
import { useSellConsignmentStore } from "@stores/sellConsignmentStore"
import { useFormSellConsignmentStore } from "stores/formSellConsignmentStore"
import { useSizeStore } from "@stores/useSizeStore"
import { SellerListingApiRepository } from "@infrastructure/repositories/sellerListingApiRepository"
import { useSellerListingStore } from "stores/useSellerListing"
import { event } from "@lib/gtag"
import useCheckSellerRegistrationStatus from "@app/hooks/useCheckSellerRegistrationStatus"

import Product from "./Product"
import ButtonNext from "./ButtonNext"
import UsedForm from "./UsedForm"
import NewForm from "./NewForm"
import useGetItemListingPriceSummary from "@app/hooks/useGetItemListingPriceSummary"

const FormContainer = () => {
  const {
    itemCondition,
    selectedProduct,
    option,
    resetState: resetSellConsignment,
  } = useSellConsignmentStore()
  const { overallAppearanceImage, resetState: resetSellerListing } =
    useSellerListingStore()
  const { resetState: resetFormSellConsignment } = useFormSellConsignmentStore()
  const { data: itemSummary } = useGetItemListingPriceSummary(
    selectedProduct.id,
  )

  const itemConditionValue =
    itemCondition === "99% Perfect" ? "BRAND_NEW" : "USED"
  const {
    defectDetail,
    isConsignment,
    isHypequarterDisplay,
    isNewNoDefect,
    isOffline,
    isPreOrder,
    note,
    packagingCondition,
    quantity,
    sellingPrice,
    sizeId,
    status,
  } = useSellerListingStore()
  const { sizes } = useSizeStore()
  const router = useRouter()
  const { data } = useCheckSellerRegistrationStatus()

  const brandNewListings = useMemo(() => {
    return sizes
      .filter(
        (item) =>
          item.price !== 0 && item.quantity !== 0 && item.price % 10000 === 0,
      )
      .map((item) => ({
        quantity: item.quantity,
        sellingPrice: item.price,
        sizeId: Number(item.id),
        isConsignment,
        isHypequarterDisplay,
      }))
  }, [isConsignment, isHypequarterDisplay, sizes])

  const formRender =
    itemCondition === "Used" ||
    (itemCondition === "99% Perfect" && option !== "consignment")
      ? UsedForm
      : NewForm
  const SellerListing = new SellerListingApiRepository()
  const isShowTextHelperSize =
    itemCondition === "Brand New" || option === "consignment"
  const mutation = useMutation({
    mutationKey: ["addSellerListing"],
    mutationFn: async () => {
      if (option === "consignment") {
        return SellerListing.createBulk({
          itemId: selectedProduct.id,
          listings: brandNewListings,
          isHypequarterDisplay,
        }).then((res) => {
          const listingIds = res.data.sellerListingStockIds
          const prices = brandNewListings.flatMap((item: any) =>
            Array(item.quantity).fill(String(item.sellingPrice)),
          )
          const sizes = brandNewListings.flatMap((item: any) =>
            Array(item.quantity).fill(String(item.sizeId)),
          )

          listingIds.forEach((id: number, index: number) => {
            event({
              action: "product_listing_saved",
              params: {
                sku: selectedProduct.skuCode,
                size: sizes[index],
                condition:
                  option === "consignment" ? "Brand New" : itemCondition,
                type: option === "consignment" ? "consignment" : "selling",
                price: prices[index],
                quantity: 1,
                [snakeCase("product_category")]: selectedProduct.category?.name,
                [snakeCase("listing_id")]: String(id),
                [snakeCase("item_id")]: String(selectedProduct.id),
              },
              userId: String(data?.memberId || ""),
            })

            event({
              action: "product_listing_success",
              params: {
                sku: selectedProduct.skuCode,
                size: sizes[index],
                condition:
                  option === "consignment" ? "Brand New" : itemCondition,
                type: option === "consignment" ? "consignment" : "selling",
                price: prices[index],
                quantity: 1,
                [snakeCase("product_category")]: selectedProduct.category?.name,
                [snakeCase("listing_id")]: String(id),
                [snakeCase("item_id")]: String(selectedProduct.id),
              },
              userId: String(data?.memberId || ""),
            })
          })
        })
      }

      if (itemCondition === "Brand New") {
        // Validate that all brand new listings have a price
        if (brandNewListings.some((listing) => !listing.sellingPrice)) {
          throw new Error("Price is required for all brand new items")
        }

        return SellerListing.createBulkBrandNew({
          itemId: selectedProduct.id,
          listings: brandNewListings,
          expiredAt: "30_DAYS",
          isHypequarterDisplay,
        }).then((res) => {
          console.log(res, "ini apa aja")

          res.data.forEach((item: any) => {
            event({
              action: "product_listing_saved",
              params: {
                sku: selectedProduct.skuCode,
                size: String(item.sizeId),
                condition: itemCondition,
                type: "selling",
                price: item.sellingPrice.amountText,
                quantity: String(item.quantity),
                [snakeCase("product_category")]: selectedProduct.category?.name,
                [snakeCase("listing_id")]: String(item.id),
                [snakeCase("item_id")]: String(selectedProduct.id),
              },
              userId: String(data?.memberId || ""),
            })

            event({
              action: "product_listing_success",
              params: {
                sku: selectedProduct.skuCode,
                size: String(item.sizeId),
                condition: itemCondition,
                type: "selling",
                price: String(item.sellingPrice.amountText),
                quantity: String(item.quantity),
                [snakeCase("product_category")]: selectedProduct.category?.name,
                [snakeCase("listing_id")]: String(item.id),
                [snakeCase("item_id")]: String(selectedProduct.id),
              },
              userId: String(data?.memberId || ""),
            })
          })
        })
      }

      // Convert base64 strings to Files, maintaining array structure
      const imageFiles = await Promise.all(
        overallAppearanceImage.map(async (base64String, index) => {
          if (!base64String) return null

          // Extract the mime type from the base64 string
          const mimeMatch = base64String.match(/^data:([^;]+);base64,/)
          const mimeType = mimeMatch ? mimeMatch[1] : "image/jpeg"

          // Get the base64 data without the prefix
          const base64Data = base64String.replace(
            /^data:image\/\w+;base64,/,
            "",
          )

          // Convert base64 to blob
          const byteCharacters = atob(base64Data)
          const byteNumbers = new Array(byteCharacters.length)
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i)
          }
          const byteArray = new Uint8Array(byteNumbers)
          const blob = new Blob([byteArray], { type: mimeType })

          // Create File object
          return new File(
            [blob],
            `image-${index + 1}.${mimeType.split("/")[1]}`,
            { type: mimeType },
          )
        }),
      )

      return SellerListing.create(
        {
          countryId: selectedProduct.countryId,
          isConsignment,
          isNewNoDefect,
          isOffline,
          isPreOrder,
          itemCondition: itemConditionValue,
          itemId: selectedProduct.id,
          packagingCondition,
          quantity,
          sellingPrice,
          sizeId,
          status,
          isHypequarterDisplay,
          defectDetail,
          note,
        },
        imageFiles as File[],
        "30_DAYS",
      ).then((res) => {
        event({
          action: "product_listing_saved",
          params: {
            sku: selectedProduct.skuCode,
            size: String(res.sizeId),
            condition: itemCondition,
            type: "selling",
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            price: String(res.sellingPrice.amountText),
            quantity: String(res.quantity),
            [snakeCase("product_category")]: selectedProduct.category?.name,
            [snakeCase("listing_id")]: String(res.id),
            [snakeCase("item_id")]: String(selectedProduct.id),
          },
          userId: String(data?.memberId),
        })

        event({
          action: "product_listing_success",
          params: {
            sku: selectedProduct.skuCode,
            size: String(res.sizeId),
            condition: itemCondition,
            type: "selling",
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            price: String(Number(res.sellingPrice.amountText)),
            quantity: String(res.quantity),
            [snakeCase("product_category")]: selectedProduct.category?.name,
            [snakeCase("listing_id")]: String(res.id),
            [snakeCase("item_id")]: String(selectedProduct.id),
          },
          userId: String(data?.memberId),
        })
      })
    },
    onSuccess: () => {
      // Reset all states after successful submission
      resetSellConsignment()
      resetSellerListing()
      resetFormSellConsignment()
      // Clear mutation state
      mutation.reset()
      if (option === "consignment") {
        router.push("/sell-consignment/success?consignment=true")
      } else {
        router.push("/sell-consignment/success")
      }
    },
    onError: (error) => {
      console.error("Sell Consignment Error:", error)
      // Reset form states but keep product selection
      resetFormSellConsignment()
      mutation.reset()
      router.push("/sell-consignment/error")
    },
  })

  const getHighestOffer = (sizeId: string) => {
    if (itemCondition === "Brand New") {
      const offer =
        itemSummary?.data.highestOffer.standardBrandNewNoDefect[sizeId]
      return offer ? Number(offer.amount) : 0
    }

    if (itemCondition === "") {
      const offer =
        itemSummary?.data.highestOffer.expressBrandNewNoDefect[sizeId]
      return offer ? Number(offer.amount) : 0
    }

    return 0
  }
  const isDisabledBrandNew =
    brandNewListings.length === 0 ||
    sizes.some(
      (item) =>
        (item.price === 0 && item.quantity > 0) ||
        (item.price % 10000 !== 0 && item.quantity > 0),
    )

  const actualImageCount = overallAppearanceImage.filter(
    (img) => img !== "",
  ).length

  const hasErrorMessage = () => {
    return sizes.some((item) => {
      const highestOffer = Number(getHighestOffer(item.id)) ?? 0
      if (item.price <= highestOffer && item.price > 0 && item.quantity > 0) {
        return true
      }
      return false
    })
  }

  const isDisabledNotBrandNew = !(
    quantity &&
    sellingPrice &&
    sizeId &&
    actualImageCount >= 5 &&
    (sellingPrice as number) % 10000 === 0 &&
    packagingCondition &&
    (itemCondition === "99% Perfect" || itemCondition === "Used"
      ? defectDetail.length > 0
      : true)
  )

  const isDisabled =
    itemCondition === "Brand New" || option === "consignment"
      ? isDisabledBrandNew
      : isDisabledNotBrandNew

  const isConsignmentTitle =
    option === "consignment" ? "Consignment" : "Selling"

  return (
    <div className="h-auto w-full text-center">
      <Heading heading="3" textStyle="bold">
        {`${isConsignmentTitle} Form`}
      </Heading>
      <Space size="lg" direction="y" type="margin" />
      <Product product={selectedProduct} />
      {isShowTextHelperSize && (
        <Text
          size="xs"
          state="secondary"
          type="regular"
          className="mt-base text-left"
        >
          Please select the size you wish to sell.
        </Text>
      )}
      <div className="my-lg">
        <Divider orientation="horizontal" />
      </div>
      {formRender()}
      <ButtonNext
        disabled={isDisabled || mutation.isPending || hasErrorMessage()}
        onClick={() => {
          mutation.mutate()
        }}
      >
        Submit
      </ButtonNext>
    </div>
  )
}

export default FormContainer
