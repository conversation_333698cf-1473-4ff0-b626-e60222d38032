import React from "react"
import { snakeCase } from "lodash"
import {
  SellConsignmentState,
  useSellConsignmentStore,
} from "stores/sellConsignmentStore"
import { useFormSellConsignmentStore } from "stores/formSellConsignmentStore"
import { useSellerListingStore } from "stores/useSellerListing"
import { event } from "@lib/gtag"
import useCheckSellerRegistrationStatus from "@app/hooks/useCheckSellerRegistrationStatus"

import Card from "./Card"
import ButtonNext from "./ButtonNext"
import {
  ItemConditionType,
  LIST_ITEM_CONDITION,
} from "./sell.consignment.utils"

export interface ItemConditionProps {
  handleNext: () => void
}

const ItemCondition = (props: ItemConditionProps) => {
  const { handleNext } = props
  const { itemCondition, setItemCondition } = useSellConsignmentStore()
  const { setField } = useFormSellConsignmentStore()
  const { setField: setFieldSellingPrice } = useSellerListingStore()
  const { data } = useCheckSellerRegistrationStatus()

  const handleItemConditionClick = (item: ItemConditionType) => {
    setItemCondition(item.label as SellConsignmentState["itemCondition"])
    setField("itemCondition", item.value)
    setFieldSellingPrice("sellingPrice", null)

    event({
      action: "product_listing_saved",
      params: {
        [snakeCase("item_condition")]: item.label,
        type: "selling",
      },
      userId: String(data?.memberId),
    })
  }
  return (
    <div className="flex flex-col items-center gap-lg lg:flex-row">
      {LIST_ITEM_CONDITION.map((item) => (
        <button
          key={item.value}
          type="button"
          onClick={() => handleItemConditionClick(item)}
        >
          <Card
            icon={item.icon}
            selected={itemCondition === item.label}
            body={item.body}
            heading={item.heading}
            className="lg:!w-[188px]"
          />
        </button>
      ))}

      <ButtonNext onClick={handleNext} disabled={itemCondition === ""}>
        Next
      </ButtonNext>
    </div>
  )
}

export default ItemCondition
