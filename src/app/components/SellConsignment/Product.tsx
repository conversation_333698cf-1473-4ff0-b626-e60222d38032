import { Button, Text } from "@kickavenue/ui/components"
import Image from "next/image"
import { Product as ProductType } from "types/product.type"
import { useSellConsignmentStore } from "stores/sellConsignmentStore"
import { getProductImageUrl } from "@utils/misc"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"

interface ProductProps {
  handleNext?: () => void
  withButton?: boolean
  product: ProductType
}
const Product = (props: ProductProps) => {
  const { handleNext, withButton = false, product } = props
  const { setSelectedProduct, option } = useSellConsignmentStore()
  const { member } = useMemberStore()

  const brandName = (product?.brands && product?.brands[0]?.name) || ""

  return (
    <div className="text-left">
      <div className="flex w-full justify-between gap-x-sm">
        <Image
          src={getProductImageUrl(product)}
          width={82}
          height={82}
          alt="dummy-image"
          className="flex-none rounded-sm"
        />
        <div className="flex w-full flex-col items-start gap-y-xs text-left">
          <Text size="sm" state="primary" type="bold">
            {brandName}
          </Text>
          <Text size="base" state="primary" type="medium">
            {product.name}
          </Text>
          <Text size="sm" state="secondary" type="regular">
            SKU: {product.skuCode}
          </Text>
        </div>
        {withButton && (
          <div className="flex flex-col justify-center">
            <Button
              onClick={() => {
                if (handleNext) {
                  setSelectedProduct(product)
                  event({
                    action: "product_listing_saved",
                    params: {
                      name: product.name,
                      type: option,
                      sku: product.skuCode,
                    },
                    userId: String(member?.id || ""),
                  })
                  handleNext()
                }
                setSelectedProduct(product)
              }}
              size="md"
              variant="secondary"
            >
              Select
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export default Product
