import { useState } from "react"
import {
  tabItems,
  TabMenu,
} from "@components/InviteAndEarn/utils/inviteandearn.utils"
import InviteContainer from "@components/InviteAndEarn/components/invite/InviteContainer"
import EarnContainer from "@components/InviteAndEarn/components/earn/EarnContainer"

export const useInviteAndEarn = () => {
  const [tab, setTab] = useState<TabMenu>(tabItems[0])

  const renderTabComponent = () => {
    switch (tab.id) {
      case 0:
        return <InviteContainer />
      case 1:
        return <EarnContainer />
    }
  }

  const isTextActive = (item: TabMenu) => (item === tab ? "bold" : "regular")

  return {
    tabItems,
    tab,
    setTab,
    renderTabComponent,
    isTextActive,
  }
}
