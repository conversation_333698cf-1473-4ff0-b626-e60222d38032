import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { GetInviteAndEarn } from "@application/usecases/getInviteAndEarn"
import { ReferralCodeHistoryApiRepository } from "@infrastructure/repositories/referralCodeHistoryApiRepository"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

export const useInvite = () => {
  const memberRepository = new MemberApiRepository()
  const referralCodeHistoryRepository = new ReferralCodeHistoryApiRepository()

  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)

  const { data } = useQuery({
    queryKey: [QueryKeysConstant.GET_INVITED_FRIENDS, page, pageSize],
    queryFn: async () => {
      const usecase = new GetInviteAndEarn(
        memberRepository,
        referralCodeHistoryRepository,
      )
      const result = await usecase.getInvitedFriend(page, pageSize)
      return result
    },
  })

  const handleNextPage = () => {
    setPage(page + 1)
  }

  const handlePrevPage = () => {
    if (page <= 1) return
    setPage(page - 1)
  }

  const handleSelectPage = (page: number) => {
    setPage(page)
  }

  const handleSelectPerPage = (pageSize: number) => {
    setPageSize(pageSize)
  }

  return {
    data,
    handleNextPage,
    handlePrevPage,
    handleSelectPage,
    handleSelectPerPage,
  }
}
