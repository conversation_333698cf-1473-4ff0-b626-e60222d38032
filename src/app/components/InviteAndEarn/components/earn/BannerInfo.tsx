import {
  Divider,
  IconAddFriendBulkGreen,
  IconMoneyAddBulkGreen,
  Text,
} from "@kickavenue/ui/components"
import React from "react"
import { formatNumberWithSeparator } from "@utils/separator"

export interface BannerInfoProps {
  totalReferrals: number
  totalCashback: number
}

export default function BannerInfo(props: BannerInfoProps) {
  const { totalReferrals, totalCashback } = props
  const totalReferralsFormatted = formatNumberWithSeparator(
    totalReferrals || 0,
    ",",
  )
  const totalCashbackFormatted = formatNumberWithSeparator(
    totalCashback || 0,
    ",",
  )
  return (
    <div className="flex w-full items-center justify-evenly rounded-sm bg-gray-w-95 p-sm">
      <div className="flex items-center">
        <IconAddFriendBulkGreen className="mx-4 scale-150" />
        <div className="">
          <Text size="xs" state="secondary" type="regular">
            Total Referals
          </Text>
          <Text size="base" state="primary" type="bold">
            {totalReferralsFormatted}
          </Text>
        </div>
      </div>
      <div className="h-10">
        <Divider type="solid" orientation="vertical" />
      </div>
      <div className="flex items-center">
        <IconMoneyAddBulkGreen className="mx-4 scale-150" />
        <div className="">
          <Text size="xs" state="secondary" type="regular">
            Total Cashback
          </Text>
          <Text size="base" state="primary" type="bold">
            IDR {totalCashbackFormatted}
          </Text>
        </div>
      </div>
    </div>
  )
}
