import { Button, Divider } from "@kickavenue/ui/components"
import React from "react"
import Empty from "@kickavenue/ui/components/Empty"
import { TEarnHistory } from "types/earnHistory.type"
import {
  formatDateWithSeparator,
  formatNumberWithSeparator,
} from "@utils/separator"

import HistoryItem from "./HistoryItem"

export interface HistoryProps {
  list: TEarnHistory[]
}

export default function History({ list }: HistoryProps) {
  return (
    <>
      <h5 className="mt-xxl text-heading-5 font-bold">History</h5>
      <div className="my-sm">
        <Divider type="solid" orientation="horizontal" />
      </div>
      {list.length === 0 && (
        <div className="mt-xl flex h-full items-center justify-center">
          <Empty
            title="Unlock Rewards by Sharing"
            subText="Your cashback history is empty. Share your referral code and earn exciting rewards for both you and your friends. Start inviting now!"
            className="[&>img]:w-[200px]"
            actionButton={<Button variant="primary">Invite Now</Button>}
          />
        </div>
      )}
      {list?.map((item) => {
        const { id, title, createdAt, amount } = item
        return (
          <HistoryItem
            key={id}
            name={title}
            date={formatDateWithSeparator(new Date(createdAt), "/").toString()}
            amount={formatNumberWithSeparator(amount, ",").toString()}
          />
        )
      })}
    </>
  )
}
