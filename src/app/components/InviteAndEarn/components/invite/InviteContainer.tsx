import React from "react"
import Empty from "@kickavenue/ui/components/Empty"
import { Space } from "@kickavenue/ui/components"
import { useInvite } from "@components/InviteAndEarn/hooks/useInvite"
import TablePagination from "@components/shared/Table/TablePagination"

import TopBanner from "./TopBanner"
import Steps from "./HowToInviteSteps"
import Table from "./InvitedFriendsTable"

export default function InviteContainer() {
  const {
    data,
    handleNextPage,
    handlePrevPage,
    handleSelectPage,
    handleSelectPerPage,
  } = useInvite()
  const { member, invitedFriends } = data || {}
  const { content } = invitedFriends || {}
  const { referralCode = "" } = member || {}

  const renderTable = () => {
    if (content && content.length > 0) {
      return (
        <>
          <Table list={content!} />
          <Space direction="y" size="base" type="margin" />
          <div className="flex justify-end">
            <TablePagination
              totalPages={totalPages}
              currentPage={page + 1}
              pageSize={pageSize}
              handleNext={handleNextPage}
              handlePrev={handlePrevPage}
              handlePage={handleSelectPage}
              handlePerPage={handleSelectPerPage}
            />
          </div>
        </>
      )
    } else {
      return (
        <div className="mt-xxl flex h-full items-center justify-center">
          <Empty
            title="Invite More Friends"
            subText="Your referral list is empty. Invite your friends to buy authentic items at KickAvenue and start earning rewards!"
            className="[&>img]:w-[200px]"
          />
        </div>
      )
    }
  }

  const { totalPages = 0, page = 0, pageSize = 10 } = data?.invitedFriends || {}

  return (
    <div className="w-full">
      <TopBanner referralCode={referralCode} />
      <Steps />
      {renderTable()}
    </div>
  )
}
