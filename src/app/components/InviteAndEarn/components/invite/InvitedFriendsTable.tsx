/* eslint-disable no-warning-comments */
import {
  Divider,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kickavenue/ui/components"
import React from "react"
import { formatDateWithSeparator } from "@utils/separator"
import { TReferralCodeHistory } from "types/referralCodeHistory.type"

export interface InvitedFriendsTableProps {
  list: TReferralCodeHistory[]
}

export default function InvitedFrinedsTable({
  list,
}: InvitedFriendsTableProps) {
  return (
    <>
      <h5 className="mt-xxl text-heading-5 font-bold">Invited Friends</h5>
      <div className="my-sm">
        <Divider type="solid" orientation="horizontal" />
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="text-left">Name</TableHead>
            <TableHead className="text-left">Email</TableHead>
            <TableHead className="text-left">Date</TableHead>
            <TableHead className="text-left">Phase</TableHead>
            <TableHead className="text-left">Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {list?.map((item) => {
            const { id, createdAt, status, claimMember } = item
            const {
              firstName = "",
              lastName = "",
              email = "",
            } = claimMember || {}
            const name = `${firstName} ${lastName}`
            const createdAtText = createdAt
              ? formatDateWithSeparator(new Date(createdAt), "/").toString()
              : ""
            return (
              <TableRow key={id}>
                <TableCell withBorder>{name}</TableCell>
                <TableCell withBorder>{email}</TableCell>
                <TableCell withBorder>{createdAtText}</TableCell>
                {/* TODO: update phase to get from api response when endpoint is ready */}
                <TableCell withBorder>-</TableCell>
                <TableCell withBorder>{status}</TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </>
  )
}
