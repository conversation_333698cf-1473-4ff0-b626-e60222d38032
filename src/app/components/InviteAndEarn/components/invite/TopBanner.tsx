import React from "react"
import Text from "@kickavenue/ui/components/Text"
import Image from "next/image"
import CodeWithCopyButton from "@app/components/shared/CodeWithCopyButton"
import {
  inviteTopHeaderText,
  inviteTopBodyText,
} from "@app/components/InviteAndEarn/utils/inviteandearn.utils"

export interface TopBannerProps {
  referralCode: string
}

export default function TopBanner({ referralCode }: TopBannerProps) {
  return (
    <div className="flex gap-x-lg rounded-lg border border-solid border-gray-w-80 p-lg">
      <div className="flex flex-col gap-y-base">
        <h3 className="text-heading-3 font-bold">{inviteTopHeaderText}</h3>
        <Text size="base" state="secondary" type="regular">
          {inviteTopBodyText}
        </Text>
        <div className="flex justify-between gap-xl rounded-base border border-solid border-gray-w-80 px-sm py-base">
          <Text size="sm" state="primary" type="regular">
            Referral Code
          </Text>
          <CodeWithCopyButton code={referralCode} />
        </div>
      </div>
      <Image
        src="/empty-placeholder.png"
        width={500}
        height={100}
        style={{ objectFit: "cover" }}
        alt="image"
        className="hidden md:block"
      />
    </div>
  )
}
