import React from "react"
import { RadioButton } from "@kickavenue/ui/components"
import { paymentInstallmentTypeItems } from "@components/Payment/payment.utils"
import ListItem from "@components/shared/ListItem"

export interface PaymentInstallmentTypeProps {
  setInstallmentType: (value: number) => void
  selectedInstallmentType?: number
}

export default function PaymentInstallmentType(
  props: PaymentInstallmentTypeProps,
) {
  const { setInstallmentType, selectedInstallmentType = 0 } = props
  return (
    <>
      {paymentInstallmentTypeItems.map((item) => {
        const { title, subtitle, description, titleProps, value } = item
        const isChecked = selectedInstallmentType === value
        return (
          <div className="rounded-base border border-gray-w-80" key={title}>
            <ListItem
              title={title}
              titleProps={titleProps}
              withDivider={false}
              onClick={() => setInstallmentType(value)}
              trailingIcon={
                <RadioButton
                  name="installment-type"
                  value={value}
                  checked={isChecked}
                  className="[&>div]:!hidden"
                  onChange={() => setInstallmentType(value)}
                />
              }
              subtitle={subtitle}
              description={description}
              classes={{
                wrapper: "p-sm",
                content: "!ml-0 gap-y-xxs",
              }}
            />
          </div>
        )
      })}
    </>
  )
}
