import React, { useCallback, useState } from "react"
import {
  IconArrowDownOutline,
  IconArrowUpOutline,
  IconInfoCircleBold,
  Text,
  Tooltip,
} from "@kickavenue/ui/components"
import { formatCurrency } from "@utils/separator"
import { formatPrice, isEmptyArray } from "@utils/misc"
import ClickableDiv from "@components/shared/ClickableDiv"

export interface PaymentItemSummaryProps {
  title: string
  value: number
  type?: "addition" | "deduction"
  tooltip?: string
  subitems?: PaymentItemSummaryProps[]
}

export default function PaymentSummaryItem(props: PaymentItemSummaryProps) {
  const { subitems, value, title, type = "addition", tooltip } = props
  const valueText = formatPrice(value, null, "IDR")

  const isEmptySubitems = isEmptyArray(subitems)
  const [showSubitems, setShowSubitems] = useState(false)

  const priceColor = useCallback(
    (type: "addition" | "deduction" | undefined) => {
      return type === "addition" ? "primary" : "success"
    },
    [],
  )

  const renderTooltip = tooltip ? (
    <div className="inline-flex items-center hover:opacity-100">
      <Tooltip
        classNameContent="!min-w-[224px] !text-left pointer-events-none"
        direction="bottom"
        text={tooltip}
      >
        <div className="cursor-help">
          <IconInfoCircleBold width={16} height={16} />
        </div>
      </Tooltip>
    </div>
  ) : null

  return (
    <div className="flex flex-col gap-xs">
      <div className="flex justify-between">
        <Text
          size="sm"
          type="regular"
          state="primary"
          className="flex items-center gap-xxs"
        >
          {title}
          {renderTooltip}
          {!isEmptySubitems && (
            <ClickableDiv
              onClick={() => setShowSubitems(!showSubitems)}
              keyDownHandler={() => setShowSubitems(!showSubitems)}
            >
              {showSubitems && <IconArrowUpOutline />}
              {!showSubitems && <IconArrowDownOutline />}
            </ClickableDiv>
          )}
        </Text>
        <Text size="sm" type="regular" state={priceColor(type)}>
          {valueText}
        </Text>
      </div>
      {!isEmptySubitems &&
        showSubitems &&
        subitems?.map((item) => {
          const { title, value } = item
          const valueText = formatCurrency(value, ",", "IDR")
          return (
            <div key={title} className="mx-xs flex justify-between">
              <Text size="sm" type="regular" state="secondary">
                {title}
              </Text>
              <Text size="sm" type="regular" state={priceColor(item.type)}>
                {valueText}
              </Text>
            </div>
          )
        })}
    </div>
  )
}
