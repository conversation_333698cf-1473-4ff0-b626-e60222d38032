import React from "react"
import { Divider, Text } from "@kickavenue/ui/components"
import { PaymentSummaryItem as TPaymentSummaryItem } from "@components/Checkout/utils/checkout.utils"

import PaymentSummaryItem from "./PaymentSummaryItem"

export default function PaymentSummary({
  totalPaymentText,
  itemPrices,
}: {
  totalPaymentText: string
  itemPrices: TPaymentSummaryItem[]
}) {
  return (
    <div className="rounded-base border border-gray-w-80">
      <div className="flex flex-col gap-xs p-base">
        {itemPrices
          ?.filter((item) => item.value !== 0)
          ?.map((item) => {
            const { title, value, subitems, type, tooltip } = item
            return (
              <PaymentSummaryItem
                key={title}
                title={title}
                value={value}
                type={type}
                subitems={subitems?.filter((item) => item.value !== 0)}
                tooltip={tooltip}
              />
            )
          })}
      </div>
      <Divider orientation="horizontal" state="default" />
      <div className="flex justify-between p-base">
        <Text size="sm" type="bold" state="primary">
          Total Payment
        </Text>
        <Text size="sm" type="bold" state="primary">
          {totalPaymentText}
        </Text>
      </div>
    </div>
  )
}
