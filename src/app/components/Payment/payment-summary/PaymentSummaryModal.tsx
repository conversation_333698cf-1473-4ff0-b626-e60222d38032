import {
  IconCloseOutline,
  Modal as ModalContainer,
} from "@kickavenue/ui/components"
import React from "react"
import PaymentSummary from "@components/Payment/payment-summary/PaymentSummary"
import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"

import { usePaymentSummary } from "../hooks/usePaymentSummary"

export default function PaymentSummaryModal() {
  const { setOpen } = useModalStore()
  const { totalPaymentText, itemPrices } = usePaymentSummary()
  return (
    <Modal modalId="payment-summary-modal">
      <ModalContainer
        title="Payment Summary"
        open
        trailing={
          <IconCloseOutline
            className="cursor-pointer"
            onClick={() => setOpen(false)}
          />
        }
      >
        <PaymentSummary
          totalPaymentText={totalPaymentText}
          itemPrices={itemPrices}
        />
      </ModalContainer>
    </Modal>
  )
}
