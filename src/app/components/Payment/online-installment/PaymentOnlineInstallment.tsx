import { RadioButton } from "@kickavenue/ui/components"
import ListItem from "@components/shared/ListItem"
import { usePaymentStore } from "stores/paymentStore"
import { PaymentType } from "types/payment.typs"
import { getIconFromFinanceProvider } from "@components/Payment/payment.utils"
import { EPaymentMethodType } from "types/paymentMethod.type"

export default function PaymentOnlineInstallment() {
  const { paymentType, setPayment, listPaymentMethod } = usePaymentStore()
  const listInstallment = listPaymentMethod.filter((option) => {
    return option.type === "INSTALLMENTS"
  })
  return (
    <>
      {listInstallment.map((option) => {
        const { id, name } = option
        const isChecked = paymentType?.id === id

        return (
          <ListItem
            key={id}
            leadingIcon={getIconFromFinanceProvider(name)}
            title={name}
            trailingIcon={
              <RadioButton
                name="bank"
                value={id}
                checked={isChecked}
                onChange={() => {
                  setPayment({
                    id,
                    name,
                    paymentMethod: EPaymentMethodType.Installments,
                  } as PaymentType)
                }}
              />
            }
          />
        )
      })}
    </>
  )
}
