import { useCallback, useState } from "react"
import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import useChangePage from "@app/hooks/useChangePage"

const { CHECKOUT_URL_SEARCH_PARAM } = MiscConstant
const { CHECKOUT, PAYMENT } = PageRouteConstant

const usePaymentCheckoutUrl = () => {
  const { getSearchParam } = useURLQuery()
  const { goToPage } = useChangePage()

  const [isGoToCheckoutUrl, setIsGoToCheckoutUrl] = useState(false)

  const checkoutUrl = getSearchParam(CHECKOUT_URL_SEARCH_PARAM) || CHECKOUT

  const goToCheckoutUrl = useCallback(() => {
    goToPage(checkoutUrl)
    setIsGoToCheckoutUrl(true)
  }, [checkoutUrl, goToPage])

  const createPaymentUrl = useCallback((checkoutUrl: string) => {
    return `${PAYMENT}?${CHECKOUT_URL_SEARCH_PARAM}=${checkoutUrl?.replace(
      "?",
      "",
    )}`
  }, [])

  return {
    goToCheckoutUrl,
    createPaymentUrl,
    isGoToCheckoutUrl,
  }
}

export default usePaymentCheckoutUrl
