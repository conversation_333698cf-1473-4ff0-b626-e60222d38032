import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useMutation } from "@tanstack/react-query"
import { useCallback, useMemo, useState } from "react"
import { useForm } from "react-hook-form"
import { FormFieldConstant } from "@constants/formField"
import { ModalConstant } from "@constants/modal"
import { convertToSnakeCase } from "@utils/misc"
import { getPaymentMethodByType } from "@utils/paymentMethod.utils"
import { isCardBinValid } from "@utils/voucher.utils"
import { useCheckoutStore } from "stores/checkoutStore"
import { useModalStore } from "stores/modalStore"
import { usePaymentStore } from "stores/paymentStore"
import { EPaymentMethodType } from "types/paymentMethod.type"
import { usePaymentCardFormStore } from "stores/paymentCardFormStore"
import { BINData } from "types/bin.type"

import {
  PaymentCreditDebitFormValues,
  paymentCreditDebitSchema,
} from "../schema/paymentCreditDebitSchema"

interface Window {
  MidtransNew3ds: {
    getCardToken: (cardData: any, options: any) => Promise<any>
  }
}

const {
  CARD_NUMBER,
  CARD_EXPIRY_MONTH,
  CARD_EXPIRY_YEAR,
  CARD_CVV,
  IS_AGREED,
} = FormFieldConstant.PAYMENT_CREDIT_DEBIT

const { CREDIT_DEBIT_CARD } = ModalConstant.MODAL_IDS

const { DebitCard, CreditCard } = EPaymentMethodType

const CARD_NUMBER_CAN_NOT_BE_USED = "Please enter a valid card number"

export const usePaymentCreditDebitForm = () => {
  const { setPaymentToken, setPayment, listPaymentMethod } = usePaymentStore()
  const [isPending, setIsPending] = useState(false)
  const { setOpen } = useModalStore()
  const { voucher } = useCheckoutStore()
  const { form: paymentCardForm } = usePaymentCardFormStore()

  const form = useForm<PaymentCreditDebitFormValues>({
    defaultValues: {
      [CARD_NUMBER.KEY]: "",
      [CARD_EXPIRY_MONTH.KEY]: "",
      [CARD_EXPIRY_YEAR.KEY]: "",
      [CARD_CVV.KEY]: "",
      [IS_AGREED.KEY]: false,
    },
    values: paymentCardForm,
    resolver: zodResolver(paymentCreditDebitSchema),
  })

  const validateCardNumber = useCallback(
    (value: string, isError: boolean) => {
      if (isError) return CARD_NUMBER_CAN_NOT_BE_USED

      const isBinValid = isCardBinValid(value, voucher)
      if (!isBinValid) {
        return `Card bin not eligible for voucher code ${voucher?.code}`
      }

      return true
    },
    [voucher],
  )

  const submitGetCardToken = async (
    data: PaymentCreditDebitFormValues & BINData,
  ) => {
    setIsPending(true)

    const cardNumber = String(data.cardNumber)?.replace(/\s+/g, "")
    const cardExpYear = String(data.cardExpYear)?.replace(/\s+/g, "")

    const cardData = convertToSnakeCase({
      cardNumber: Number(cardNumber),
      cardExpMonth: data.cardExpMonth,
      cardExpYear: Number(
        `${String(new Date().getFullYear()).slice(0, 2)}${cardExpYear}`,
      ),
      cardCvv: data.cardCvv,
    })

    const cardPayments = getPaymentMethodByType(listPaymentMethod, [
      DebitCard,
      CreditCard,
    ])

    const selectedCardPayment = cardPayments.find((payment) => {
      return payment.type.toLowerCase().startsWith(data.binType.toLowerCase())
    })

    const options = {
      onSuccess(response: any) {
        const tokenId = response.token_id
        setOpen(false, CREDIT_DEBIT_CARD)
        setIsPending(false)
        setPaymentToken(tokenId)
        setPayment({
          id: selectedCardPayment?.id ?? 0,
          name: selectedCardPayment?.name ?? "",
          cardInformation: {
            cardNumber,
            expiryDate: `${data.cardExpMonth}/${data.cardExpYear}`,
            cvv: String(data.cardCvv),
          },
        })
        form.reset()
      },
      onFailure(response: any) {
        // eslint-disable-next-line no-console
        console.error("Fail to get card token_id, response:", response)
        setIsPending(false)
      },
    }

    const w = window as unknown as Window
    w.MidtransNew3ds.getCardToken(cardData, options)
  }

  const { mutate } = useMutation({
    mutationFn: submitGetCardToken,
  })

  const onFormValid = useCallback(
    (values: PaymentCreditDebitFormValues & BINData) => {
      mutate(values)
    },
    [mutate],
  )

  const submitDisabled = useMemo(() => {
    return !form.formState.isValid || isPending
  }, [form.formState.isValid, isPending])

  return {
    form,
    onFormValid,
    submitDisabled,
    validateCardNumber,
  }
}
