import { useMemo } from "react"
import { calculateBillPayment } from "@components/Checkout/utils/checkout.utils"
import { useCheckoutStore } from "stores/checkoutStore"
import { usePaymentStore } from "stores/paymentStore"
import { getStripAmount } from "@utils/misc"

const useDisabledUsePayment = () => {
  const { paymentType, isCreditBalance, isKickPoints, userBalance } =
    usePaymentStore()

  const creditBalance =
    getStripAmount(userBalance?.sellerCredit) +
    getStripAmount(userBalance?.kickCredit)

  const kickPoints = getStripAmount(userBalance?.kickPoint)

  const { itemPrices } = useCheckoutStore()
  const totalPayment = calculateBillPayment(itemPrices)

  const isDisabled = useMemo(() => {
    if (isCreditBalance && !isKickPoints && creditBalance < totalPayment) {
      return true
    }

    if (!isCreditBalance && isKickPoints && kickPoints < totalPayment) {
      return true
    }

    if (
      isCreditBalance &&
      isKickPoints &&
      creditBalance &&
      kickPoints &&
      creditBalance + kickPoints < totalPayment
    ) {
      return true
    }

    return (
      paymentType === null &&
      isCreditBalance === false &&
      isKickPoints === false
    )
  }, [
    paymentType,
    isCreditBalance,
    isKickPoints,
    creditBalance,
    kickPoints,
    totalPayment,
  ])

  return isDisabled
}

export default useDisabledUsePayment
