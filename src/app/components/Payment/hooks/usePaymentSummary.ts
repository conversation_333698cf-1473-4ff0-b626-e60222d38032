/* eslint-disable no-warning-comments */
import { useEffect } from "react"
import { useCheckoutStore } from "stores/checkoutStore"
import { formatPrice, getStripAmount } from "@utils/misc"
import {
  calculateAddonSummary,
  calculateTotalPayment,
  getWalletUsageSummary,
  getVoucherSummary,
  PaymentSummaryItem,
  getFeeRateItems,
} from "@components/Checkout/utils/checkout.utils"
import useFetchFeeRates from "@app/hooks/useFetchFeeRates"
import { useCheckoutAddress } from "@components/Checkout/hooks/useCheckoutAddress"
import useFetchFeeById from "@app/hooks/useFetchFeeById"
import { getFeeAmount } from "@utils/fee.utils"
import { usePaymentStore } from "stores/paymentStore"

// eslint-disable-next-line max-lines-per-function
export const usePaymentSummary = () => {
  const {
    setItemPrices,
    itemPrices,
    listing,
    addon,
    voucher,
    setShippingFee,
    setProcessingFee,
  } = useCheckoutStore()

  const { userBalance, selectedPayment } = usePaymentStore()
  const isCreditBalance = selectedPayment?.isCreditBalance || false
  const isKickPoints = selectedPayment?.isKickPoints || false

  const { selectedAddress } = useCheckoutAddress()
  const sellingPrice = getStripAmount(listing?.sellingPrice)

  const { rate } = useFetchFeeRates({
    body: {
      items: getFeeRateItems({
        listings: listing ? [listing, ...addon] : [],
      }),
      postalCode: selectedAddress?.zipCode
        ? Number(selectedAddress?.zipCode)
        : 0,
    },
    enabled: Boolean(listing?.id) && Boolean(selectedAddress?.zipCode),
    onSuccess: (rate) => {
      setShippingFee(rate)
    },
  })

  const { data: feeData } = useFetchFeeById({
    // TODO: ensure this will work on prd
    id: 1,
    onSuccess: (fee) => {
      setProcessingFee(getFeeAmount(fee, sellingPrice))
    },
  })

  const stringifiedAddon = JSON.stringify(addon)

  useEffect(() => {
    const { totalValue: totalAddOn, subitems } = calculateAddonSummary(addon)
    const shippingFee = rate || 0

    const voucherSummary = getVoucherSummary({
      voucher,
      sellingPrice,
      shippingFee,
      processingFee: 0,
      totalAddOnPrice: totalAddOn,
    })

    const processingFee = getFeeAmount(feeData, sellingPrice + totalAddOn)

    const items = [
      {
        title: "Product Price",
        type: "addition",
        value: sellingPrice,
      },
      {
        title: "Add On Product",
        type: "addition",
        value: totalAddOn,
        subitems,
      },
      {
        title: "Processing Fee",
        type: "addition",
        value: processingFee,
        tooltip:
          "Ensuring your order arrives safely, covering bubble wrap for weather protection, double-boxing for shipment security, and insurance for added peace of mind.",
      },
      {
        title: "Shipping Fee",
        type: "addition",
        value: shippingFee,
      },
      voucherSummary,
    ] as PaymentSummaryItem[]

    if (isCreditBalance || isKickPoints) {
      items.push(
        ...getWalletUsageSummary({
          userBalance,
          isCreditBalance,
          isKickPoints,
          totalPayment: calculateTotalPayment(items),
        }),
      )
    }

    setItemPrices(items)
  }, [
    setItemPrices,
    sellingPrice,
    addon,
    stringifiedAddon,
    rate,
    voucher,
    feeData,
    userBalance,
    isCreditBalance,
    isKickPoints,
  ])

  const totalPayment = calculateTotalPayment(itemPrices)

  const totalPaymentText = formatPrice(totalPayment, null, "IDR", "IDR 0")

  return {
    totalPayment,
    totalPaymentText,
    itemPrices,
  }
}
