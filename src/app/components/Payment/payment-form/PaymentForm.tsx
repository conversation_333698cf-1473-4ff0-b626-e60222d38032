"use client"

import React, { useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconArrowLeftOutline,
  Text,
} from "@kickavenue/ui/components"
import { snakeCase } from "lodash"
import { usePaymentStore } from "stores/paymentStore"
import PaymentMethod from "@components/Payment/payment-method/PaymentMethod"
import PaymentCreditDebit from "@components/Payment/credit-debit/PaymentCreditDebit"
import PaymentVirtualAccount from "@components/Payment/virtual-account/PaymentVirtualAccount"
import PaymentOnlineInstallment from "@components/Payment/online-installment/PaymentOnlineInstallment"
import useFetchPaymentMethods from "@app/hooks/useFetchPaymentMethods"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { useCheckoutStore } from "stores/checkoutStore"
import { isEmptyArray } from "@utils/misc"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"

import useDisabledUsePayment from "../hooks/useDisabledUsePayment"
import usePaymentMethods from "../hooks/usePaymentMethods"
import usePaymentCheckoutUrl from "../hooks/usePaymentCheckoutUrl"

import styles from "./PaymentForm.module.scss"

// eslint-disable-next-line max-lines-per-function
export default function PaymentForm() {
  const {
    paymentType,
    isCreditBalance,
    isKickPoints,
    paymentToken,
    selectedPayment,
    setIsCreditBalance,
    resetPaymentState,
    isEmptySelectedPayment,
    setListPaymentMethod,
    setSelectedPayment,
    setPaymentToken,
    setIsKickPoints,
    setPayment,
  } = usePaymentStore()

  const { listing } = useCheckoutStore()
  const { member } = useMemberStore()

  const { isLoading } = useFetchPaymentMethods({
    enabled: true,
    onSuccess: (response) => {
      setListPaymentMethod(response)
    },
  })

  const isDisabled = useDisabledUsePayment()
  const { VAPayments, InstallmentPayments, walletPayments, cardPayments } =
    usePaymentMethods()

  const { goToCheckoutUrl, isGoToCheckoutUrl } = usePaymentCheckoutUrl()

  const generatePaymentName = () => {
    if (isKickPoints && isCreditBalance) return "Kick Points + Credit Balance"
    if (isKickPoints) return "Kick Points"
    if (isCreditBalance) return "Credit Balance"
    if (paymentType) return paymentType?.name

    return "No Payment Method Selected"
  }

  const handleConfirmPayment = () => {
    event({
      action: "product_checkout_payment",
      params: {
        [snakeCase("payment_method")]: generatePaymentName(),
        [snakeCase("listing_id")]: String(listing?.id || ""),
      },
      userId: String(member?.id || ""),
    })

    setSelectedPayment({
      isCreditBalance,
      isKickPoints,
      paymentType,
      paymentToken,
      listingId: listing?.id,
    })
    goToCheckoutUrl()
  }

  useEffect(() => {
    if (isEmptySelectedPayment()) {
      resetPaymentState()
    }
  }, [isEmptySelectedPayment, resetPaymentState])

  useEffect(() => {
    if (listing?.id !== selectedPayment?.listingId) return

    setIsCreditBalance(selectedPayment?.isCreditBalance ?? false)
    setIsKickPoints(selectedPayment?.isKickPoints ?? false)
    setPayment(selectedPayment?.paymentType ?? null)
    setPaymentToken(selectedPayment?.paymentToken ?? null)
  }, [
    listing,
    selectedPayment?.listingId,
    selectedPayment?.isCreditBalance,
    selectedPayment?.isKickPoints,
    selectedPayment?.paymentType,
    selectedPayment?.paymentToken,
    setIsCreditBalance,
    setIsKickPoints,
    setPayment,
    setPaymentToken,
  ])

  if (isLoading) {
    return (
      <div className={styles["payment-form"]}>
        <div className="bg-gray-w-95 p-xxl pt-lg md:flex md:justify-center">
          <SpinnerLoading />
        </div>
      </div>
    )
  }

  return (
    <div className={styles["payment-form"]}>
      <div className="flex justify-center bg-gray-w-95 px-base pb-xl pt-lg">
        <div className="rounded-base bg-white md:w-[564px]">
          <div className="flex flex-col gap-lg p-lg">
            <Alert
              style={{ width: "100%" }}
              subTitle="Your payment  will be verified by the payment gateway"
              isIcon
            />

            {walletPayments.length > 0 && (
              <>
                <Text size="sm" type="bold" state="primary">
                  Wallet
                </Text>
                <PaymentMethod />
              </>
            )}

            {(!isEmptyArray(cardPayments) || !isEmptyArray(VAPayments)) && (
              <Divider orientation="horizontal" state="default" />
            )}

            {cardPayments.length > 0 && (
              <>
                <Text size="sm" type="bold" state="primary">
                  Credit/Debit Card
                </Text>
                <PaymentCreditDebit />
              </>
            )}

            {VAPayments.length > 0 && (
              <>
                <Text size="sm" type="bold" state="primary">
                  Virtual Account
                </Text>
                <PaymentVirtualAccount />
              </>
            )}

            {InstallmentPayments.length > 0 && (
              <>
                <Text size="sm" type="bold" state="primary">
                  Online Installment
                </Text>
                <PaymentOnlineInstallment />
              </>
            )}
          </div>

          <Divider orientation="horizontal" />

          <div className="flex w-full justify-center gap-base p-lg">
            <Button
              size="lg"
              variant="secondary"
              IconLeft={IconArrowLeftOutline}
              style={{ width: "100%" }}
              disabled={isGoToCheckoutUrl}
              onClick={() => {
                goToCheckoutUrl()
              }}
            >
              Back to Checkout
            </Button>
            <Button
              size="lg"
              variant="primary"
              disabled={isDisabled || isGoToCheckoutUrl}
              style={{ width: "100%" }}
              onClick={handleConfirmPayment}
            >
              Confirm Payment
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
