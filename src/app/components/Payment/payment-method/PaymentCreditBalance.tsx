import {
  IconQuestionmarkCircleBold,
  Text,
  IconKickCreditBulkColor,
  Switch,
} from "@kickavenue/ui/components"
import React, { useMemo } from "react"
import Link from "next/link"
import { formatCurrency } from "@utils/separator"
import { usePaymentStore } from "stores/paymentStore"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { useCheckoutStore } from "stores/checkoutStore"
import { calculateBillPayment } from "@components/Checkout/utils/checkout.utils"
import { getStripAmount } from "@utils/misc"

export default function PaymentCreditBalance() {
  const { userBalance } = usePaymentStore()

  const creditBalance =
    getStripAmount(userBalance?.sellerCredit) +
    getStripAmount(userBalance?.kickCredit)

  const kickPoints = getStripAmount(userBalance?.kickPoint)

  const balanceText = formatCurrency(creditBalance, ",", "IDR")

  const {
    isCreditBalance,
    setIsCreditBalance,
    setCreditBalance,
    resetPaymentTypeAndToken,
  } = usePaymentStore()

  const { itemPrices } = useCheckoutStore()

  const totalPayment = calculateBillPayment(itemPrices)

  const isDisabled = useMemo(() => {
    if (creditBalance + kickPoints < totalPayment) {
      return true
    }

    return false
  }, [kickPoints, totalPayment, creditBalance])

  const handleCreditBalanceChange = (newIsCreditBalance: boolean) => {
    setIsCreditBalance(newIsCreditBalance)
    setCreditBalance(newIsCreditBalance ? creditBalance : null)

    if (newIsCreditBalance) {
      resetPaymentTypeAndToken()
    }
  }

  return (
    <div className="rounded-base border border-gray-w-80 p-base">
      <div className="flex justify-between">
        <div className="flex flex-row items-center gap-sm">
          <IconKickCreditBulkColor className="size-lg" />
          <div className="">
            <div className="flex items-center gap-xs">
              <Text size="sm" type="bold" state="primary">
                Credit Balance
              </Text>
              <IconQuestionmarkCircleBold />
            </div>
            <Text size="sm" type="regular" state="secondary">
              {balanceText}
            </Text>
            {totalPayment > creditBalance && (
              <div className="flex items-center gap-xxs">
                <Text size="sm" type="regular" state="danger">
                  Low Balance
                </Text>
                <Link href={PageRouteConstant.TOPUP_CONFIRMATION}>
                  <div className="text-sm font-bold text-green">
                    Top Up Balance
                  </div>
                </Link>
              </div>
            )}
          </div>
        </div>
        <Switch
          checked={isCreditBalance}
          onChange={() => handleCreditBalanceChange(!isCreditBalance)}
          disabled={isDisabled}
        />
      </div>
    </div>
  )
}
