import {
  Text,
  Switch,
  IconKickPointsBulkColor,
} from "@kickavenue/ui/components"
import React, { useMemo } from "react"
import { formatCurrency } from "@utils/separator"
import { usePaymentStore } from "stores/paymentStore"
import { useCheckoutStore } from "stores/checkoutStore"
import { calculateTotalPayment } from "@components/Checkout/utils/checkout.utils"
import { getStripAmount } from "@utils/misc"

export default function PaymentKickPoints() {
  const {
    isKickPoints,
    userBalance,
    setIsKickPoints,
    setKickPoints,
    resetPaymentTypeAndToken,
  } = usePaymentStore()

  const kickPoints = getStripAmount(userBalance?.kickPoint)
  const creditBalance =
    getStripAmount(userBalance?.sellerCredit) +
    getStripAmount(userBalance?.kickCredit)

  const pointsText = formatCurrency(kickPoints, ",", "IDR")

  const { itemPrices } = useCheckoutStore()

  const totalPayment = calculateTotalPayment(itemPrices)

  const isDisabled = useMemo(() => {
    if (creditBalance + kickPoints < totalPayment) {
      return true
    }

    return false
  }, [totalPayment, kickPoints, creditBalance])

  const handleKickPointsChange = (newIsKickPoints: boolean) => {
    setIsKickPoints(newIsKickPoints)
    setKickPoints(newIsKickPoints ? kickPoints : null)

    if (newIsKickPoints) {
      resetPaymentTypeAndToken()
    }
  }
  return (
    <div className="rounded-base border border-gray-w-80 p-base">
      <div className="flex justify-between">
        <div className="flex flex-row items-center gap-sm">
          <IconKickPointsBulkColor className="size-lg" />
          <div className="">
            <Text size="sm" type="bold" state="primary">
              Kick Points
            </Text>
            <Text size="sm" type="regular" state="secondary">
              {pointsText}
            </Text>
          </div>
        </div>
        <Switch
          checked={isKickPoints}
          onChange={() => handleKickPointsChange(!isKickPoints)}
          disabled={isDisabled}
        />
      </div>
    </div>
  )
}
