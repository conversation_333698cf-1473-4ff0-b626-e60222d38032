import React from "react"
import {
  Divider,
  IconArrowLeftOutline,
  IconShieldTickOutline,
  <PERSON><PERSON> as ModalContainer,
} from "@kickavenue/ui/components"
import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

import PaymentCreditDebitForm from "./PaymentCreditDebitForm"

const { CREDIT_DEBIT_CARD } = ModalConstant.MODAL_IDS

export default function PaymentCreditDebitModal() {
  const { setOpen } = useModalStore()

  return (
    <Modal modalId={CREDIT_DEBIT_CARD}>
      <ModalContainer
        title="Credit/Debit Card"
        open
        leading={
          <IconArrowLeftOutline
            className="!h-6 !w-6 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        }
        trailing={<IconShieldTickOutline className="!h-6 !w-6" />}
        classNameModalContent="!text-left"
        classNameModalChild="!p-0"
      >
        <Divider orientation="horizontal" />
        <PaymentCreditDebitForm />
      </ModalContainer>
    </Modal>
  )
}
