import {
  But<PERSON>,
  Divider,
  IconArrowRightOutline,
  Space,
  Text,
} from "@kickavenue/ui/components"
import React from "react"
import CodeWithCopyButton from "@components/shared/CodeWithCopyButton"
import { formatPrice, capitalizeFirstLatter } from "@utils/misc"
import { TPaymentMethodData } from "types/paymentMethod.type"
import { TTransactionResponse } from "types/transaction.type"
import { useModalStore } from "stores/modalStore"

interface ReceivedPaymentDetailsProps {
  paymentMethods?: TPaymentMethodData
  transactionData: TTransactionResponse
  onViewOrder: () => void
}

export default function ReceivedPaymentDetails({
  paymentMethods,
  transactionData,
  onViewOrder,
}: ReceivedPaymentDetailsProps) {
  const { setOpen } = useModalStore()

  return (
    <div className="items w-full rounded-base border border-gray-w-80">
      <div className="border-b border-gray-w-80 px-3 py-2">
        <Text size="sm" type="medium" state="primary">
          Payment Details
        </Text>
      </div>
      <>
        <div className="border-b border-gray-w-80">
          <div className="mt-2 flex items-center justify-between gap-xs px-3">
            <Text size="sm" type="regular" state="primary">
              Invoice Number
            </Text>
            <div className="flex items-center gap-xs">
              <CodeWithCopyButton code={transactionData?.orderId || ""} />
            </div>
          </div>
        </div>
        <div className="px-3 py-2">
          <div className="flex justify-between">
            <Text size="sm" type="regular" state="primary">
              Total Payment
            </Text>
            <Text size="sm" type="medium" state="primary">
              {formatPrice(
                transactionData?.totalAmount?.minUnitVal || 0,
                null,
                "IDR",
              )}
            </Text>
          </div>
          <div className="flex justify-between">
            <Text size="sm" type="regular" state="primary">
              Payment Method
            </Text>
            <Text size="sm" type="medium" state="primary">
              {capitalizeFirstLatter(paymentMethods?.name || "")}
            </Text>
          </div>
        </div>
      </>
      <Divider orientation="horizontal" state="default" />
      <Button
        variant="link"
        style={{ width: "100%", padding: "0" }}
        onClick={() => setOpen(true, "payment-summary-modal")}
      >
        <div className="flex w-full cursor-pointer justify-between p-base">
          <Text size="sm" type="bold" state="primary">
            View Payment Summary
          </Text>
          <IconArrowRightOutline className="text-gray-w-40" />
        </div>
      </Button>
      <Space direction="y" size="xs" type="margin" />
      <div className="px-3 pb-3">
        <Button variant="primary" className="!w-full" onClick={onViewOrder}>
          View Order
        </Button>
      </div>
    </div>
  )
}
