import { RadioButton } from "@kickavenue/ui/components"
import { useCallback } from "react"
import ListItem from "@components/shared/ListItem"
import { usePaymentStore } from "stores/paymentStore"
import { getIconFromFinanceProvider } from "@components/Payment/payment.utils"
import { getVAPaymentMethod } from "@components/Checkout/utils/checkout.utils"
import { EPaymentMethodType, TPaymentMethod } from "types/paymentMethod.type"
import { capitalizeFirstLetterForBank } from "@utils/misc"

export default function PaymentVirtualAccount() {
  const {
    paymentType,
    setPayment,
    listPaymentMethod,
    setIsCreditBalance,
    setIsKickPoints,
  } = usePaymentStore()

  const listVA = getVAPaymentMethod(listPaymentMethod)

  const handleSelectVa = useCallback(
    (option: TPaymentMethod) => {
      setIsCreditBalance(false)
      setIsKickPoints(false)
      setPayment({
        id: option.id,
        name: option.name,
        paymentMethod: EPaymentMethodType.VirtualAccount,
      })
    },
    [setPayment, setIsCreditBalance, setIsKickPoints],
  )

  return (
    <>
      {listVA.map((option, index) => {
        const { id, name } = option
        const isChecked = paymentType?.id === id

        return (
          <ListItem
            key={id}
            title={capitalizeFirstLetterForBank(name)}
            leadingIcon={getIconFromFinanceProvider(name)}
            withDivider={index !== listVA.length - 1}
            classes={{ wrapper: "!p-0" }}
            trailingIcon={
              <RadioButton
                name="bank"
                value={id}
                checked={isChecked}
                onChange={() => handleSelectVa(option)}
              />
            }
          />
        )
      })}
    </>
  )
}
