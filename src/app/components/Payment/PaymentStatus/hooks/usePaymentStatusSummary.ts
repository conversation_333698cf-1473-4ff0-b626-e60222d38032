import { useMemo } from "react"
import { compact } from "lodash"
import {
  calculateAddonSummary,
  calculateTotalPayment,
  getVoucherSummary,
  PaymentSummaryItem,
} from "@components/Checkout/utils/checkout.utils"
import { TTransactionDetail } from "types/transactionDetail.type"
import { TTransaction } from "types/transaction.type"
import { formatPrice, getStripAmount } from "@utils/misc"
import { TVoucher } from "types/voucher.type"

import {
  getAddonsFromTxDetails,
  getListingFromTxDetails,
  getSummaryItemCreditUsage,
  getSummaryItemKickPointsUsage,
  getSummaryItemProductPrice,
} from "../paymentStatus.utils"

const usePaymentStatusSummary = ({
  txDetails,
  transaction,
  voucher,
}: {
  txDetails?: TTransactionDetail[] | null
  transaction?: TTransaction | null
  voucher?: TVoucher | null
}) => {
  const summaryItems: PaymentSummaryItem[] = useMemo(() => {
    const listing = getListingFromTxDetails(txDetails)

    const addons = getAddonsFromTxDetails(txDetails)

    const { totalValue: totalAddOn, subitems } = calculateAddonSummary(addons)

    const items = compact([
      getSummaryItemProductPrice(txDetails),
      {
        title: "Add On Product",
        type: "addition",
        value: totalAddOn,
        subitems,
      },
      {
        title: "Processing Fee",
        type: "addition",
        value: getStripAmount(transaction?.totalProcessingFee),
        tooltip:
          "Ensuring your order arrives safely, covering bubble wrap for weather protection, double-boxing for shipment security, and insurance for added peace of mind.",
      },
      {
        title: "Shipping Fee",
        type: "addition",
        value: getStripAmount(transaction?.totalShippingFee),
      },
      voucher
        ? getVoucherSummary({
            voucher,
            sellingPrice: getStripAmount(listing?.sellingPrice),
            shippingFee: getStripAmount(transaction?.totalShippingFee),
            processingFee: getStripAmount(transaction?.totalProcessingFee),
            totalAddOnPrice: totalAddOn,
          })
        : null,
      ...getSummaryItemCreditUsage(transaction),
      ...getSummaryItemKickPointsUsage(transaction),
    ]) as PaymentSummaryItem[]

    return items
  }, [txDetails, transaction, voucher])

  const totalPayment = calculateTotalPayment(summaryItems)

  const totalPaymentText = formatPrice(totalPayment, null, "IDR", "IDR 0")

  return {
    summaryItems,
    totalPayment,
    totalPaymentText,
  }
}

export default usePaymentStatusSummary
