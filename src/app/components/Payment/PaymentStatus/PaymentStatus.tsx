"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconAwaitingPaymentBulk,
  IconSuccessPaymentBulk,
  Text,
} from "@kickavenue/ui/components/index"
import { useParams } from "next/navigation"
import { useEffect, useMemo, useState } from "react"
import { snakeCase } from "lodash"
import useChangePage from "@app/hooks/useChangePage"
import useGetTransactionDetail from "@app/hooks/useGetTransactionDetail"
import { IconFailedPaymentBulk } from "@kickavenue/ui/dist/src/components/icons/IconFailedPaymentBulk"
import Spinner from "@components/shared/Spinner"
import { MiscConstant } from "@constants/misc"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import useGetTransaction from "@hooks/useGetTransaction"
import { EPaymentMethodType } from "types/paymentMethod.type"
import { TTransactionPaymentStatus } from "types/transaction.type"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"
import { shippingMethodMap } from "@components/Checkout/checkout-form/checkoutform.utils"
import { ShippingMethodType } from "types/shippingMethod.type"
import { useCheckoutStore } from "stores/checkoutStore"

import PaymentStatusActions from "./PaymentStatusActions"
import PaymentStatusAwaitingPaymentHeading from "./PaymentStatusAwaitingHeading"
import PaymentStatusCard from "./PaymentStatusCard"
import PaymentStatusHeading from "./PaymentStatusHeading"
import PaymentStatusInstruction from "./PaymentStatusInstruction"
import PaymentStatusOrderDetails from "./PaymentStatusOrderDetails"
import PaymentStatusSummary from "./PaymentStatusSummary"
import PaymentStatusSummaryModal from "./PaymentStatusSummaryModal/PaymentStatusSummaryModal"
import {
  checkTxPaymentType,
  getPaymentAwaitingOkText,
} from "./paymentStatus.utils"

const { Processed, WaitingForPayment, Failed } = TTransactionPaymentStatus
const { VirtualAccount } = EPaymentMethodType

const { PAGE, MAX_PAGE_SIZE } = MiscConstant.PAGING_DEFAULT
const { SEARCH, PROFILE_BUYING } = PageRouteConstant

// eslint-disable-next-line max-lines-per-function
const PaymentStatus = () => {
  const params = useParams<{ id: string }>()
  const id = params?.id
  const txId = id ? Number(id) : 0
  const [hasTracked, setHasTracked] = useState(false)

  const { member } = useMemberStore()
  const { goToPage, isLoading: isLoadingChangePage } = useChangePage()

  const { PreOrder, Express, Standard } = ShippingMethodType
  const { listing } = useCheckoutStore()

  const {
    data: transaction,
    refetch: refetchTransaction,
    isLoadingTx,
  } = useGetTransaction({ id: txId })

  const { txDetails, isLoadingTxDetails } = useGetTransactionDetail({
    filter: {
      transactionId: txId,
      page: PAGE,
      pageSize: MAX_PAGE_SIZE,
      sort: [],
    },
  })

  const awaitingOkAction = useMemo(() => {
    const isVAPayment = checkTxPaymentType(VirtualAccount, transaction)

    if (isVAPayment) {
      return refetchTransaction
    }

    return () => {}
  }, [transaction, refetchTransaction])

  const optionOnListing = useMemo(() => {
    if (listing?.isConsignment || listing?.isConsigment) {
      return shippingMethodMap[Express]
    }
    if (listing?.isPreOrder) return shippingMethodMap[PreOrder]
    return shippingMethodMap[Standard]
  }, [Express, PreOrder, Standard, listing])

  useEffect(() => {
    const allDataAvailable =
      txDetails &&
      transaction &&
      member &&
      listing &&
      optionOnListing &&
      !hasTracked

    if (!allDataAvailable) return

    event({
      action: "product_checkout_completed",
      params: {
        [snakeCase("transaction_id")]: String(txDetails?.[0].transactionId),
        [snakeCase("invoice_number")]: txDetails?.[0].invoiceNumber,
        [snakeCase("payment_type")]:
          transaction?.paymentData?.paymentMethod?.type,
        [snakeCase("payment_method")]:
          transaction?.paymentData?.paymentMethod?.name,
        [snakeCase("payment_amount")]: String(
          transaction?.paymentData?.amount || "",
        ),
        [snakeCase("item_id")]: String(txDetails?.[0].listingItem.itemId || ""),
        [snakeCase("shipping_method")]: optionOnListing.title,
        email: member.email,
        brand: listing?.item.brands?.map((brand) => brand.name).join(" | "),
        [snakeCase("display_name")]: listing?.item.name || "",
        size: `${listing?.size.us} us`,
        [snakeCase("product_category")]: listing?.item.category?.name || "",
        sku: listing?.item.skuCode,
        [snakeCase("listing_id")]: String(listing?.id || ""),
      },
      userId: String(member?.id) || "",
    })

    if (transaction?.status === Processed) {
      event({
        action: "product_paid",
        params: {
          [snakeCase("transaction_id")]: String(txDetails?.[0].transactionId),
          [snakeCase("invoice_number")]: String(txDetails?.[0].invoiceNumber),
          [snakeCase("payment_type")]:
            transaction?.paymentData?.paymentMethod?.type,
          [snakeCase("payment_method")]: String(
            transaction?.paymentData?.paymentMethod?.name,
          ),
          [snakeCase("payment_amount")]: String(
            transaction?.paymentData?.amount || "",
          ),
          [snakeCase("listing_id")]: String(listing?.id || ""),
        },
        userId: String(member?.id) || "",
      })
    }

    setHasTracked(true)
  }, [txDetails, transaction, member, listing, optionOnListing, hasTracked])

  if (isLoadingTx) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-w-95">
        <Spinner />
      </div>
    )
  }

  if (transaction?.status === Processed) {
    return (
      <PaymentStatusCard>
        <div className="flex flex-col gap-lg rounded-base bg-white md:w-[564px]">
          <div className="p-sm pb-0 md:p-lg md:pb-0">
            <div className="flex flex-col items-center gap-lg">
              <IconSuccessPaymentBulk className="size-[80px] text-success" />
              <PaymentStatusHeading
                transaction={transaction}
                title="Payment Received"
                subTitle="We're getting your order ready. Track your order status in the Buying Dashboard."
              />
              <PaymentStatusOrderDetails
                transaction={transaction}
                txDetails={txDetails}
                isLoadingTxDetails={isLoadingTxDetails}
              />
              <PaymentStatusSummary
                transaction={transaction}
                isLoadingTxDetails={isLoadingTxDetails}
              />
              <PaymentStatusSummaryModal
                transaction={transaction}
                txDetails={txDetails}
              />
              <Alert
                style={{ width: "100%" }}
                subTitle="If you have any question about your order, please contact our team by <NAME_EMAIL>. Please put your invoice number into your subject email."
                isIcon
              />
            </div>
          </div>
          <Divider orientation="horizontal" state="default" />
          <PaymentStatusActions
            cancelText="Back to Market"
            okText="Buying Dashboard"
            disabledCancel={isLoadingChangePage?.[SEARCH]}
            disabledOk={isLoadingChangePage?.[PROFILE_BUYING]}
            cancelAction={() => goToPage(SEARCH)}
            okAction={() => goToPage(PROFILE_BUYING)}
          />
        </div>
      </PaymentStatusCard>
    )
  }

  if (transaction?.status === WaitingForPayment) {
    return (
      <PaymentStatusCard className="md:!pb-lg">
        <div className="flex flex-col gap-lg">
          <div className="flex flex-col gap-lg rounded-base bg-white md:w-[564px]">
            <div className="p-sm pb-0 md:p-lg md:pb-0">
              <div className="flex flex-col items-center gap-lg">
                <IconAwaitingPaymentBulk className="size-[80px] text-warning" />
                <PaymentStatusAwaitingPaymentHeading
                  transaction={transaction}
                />
                <PaymentStatusOrderDetails
                  transaction={transaction}
                  txDetails={txDetails}
                  isLoadingTxDetails={isLoadingTxDetails}
                />
                <PaymentStatusSummary transaction={transaction} />
                <PaymentStatusSummaryModal
                  transaction={transaction}
                  txDetails={txDetails}
                />
                <Alert
                  style={{ width: "100%" }}
                  subTitle="If you have any question about your order, please contact our team by <NAME_EMAIL>. Please put your invoice number into your subject email."
                  isIcon
                />
              </div>
            </div>
            <Divider orientation="horizontal" state="default" />
            <PaymentStatusActions
              cancelText="Back to Market"
              okText={getPaymentAwaitingOkText(transaction)}
              disabledCancel={isLoadingChangePage?.[SEARCH]}
              disabledOk={isLoadingChangePage?.[PROFILE_BUYING]}
              cancelAction={() => goToPage(SEARCH)}
              okAction={awaitingOkAction}
            />
          </div>
          <PaymentStatusInstruction />
        </div>
      </PaymentStatusCard>
    )
  }

  if (transaction?.status === Failed) {
    return (
      <PaymentStatusCard>
        <div className="flex flex-col gap-lg">
          <div className="flex flex-col gap-lg rounded-base bg-white md:w-[612px]">
            <div className="p-sm pb-0 md:p-lg md:pb-0">
              <div className="flex flex-col items-center gap-lg">
                <IconFailedPaymentBulk className="size-[80px] text-danger" />
                <PaymentStatusHeading
                  transaction={transaction}
                  title="Payment Failed"
                  subTitle="Your payment was unsuccessful or the transaction was not completed before the deadline. Please place a new order."
                  subTitleClassName="md:w-[554px]"
                />
                <PaymentStatusOrderDetails
                  transaction={transaction}
                  txDetails={txDetails}
                  isLoadingTxDetails={isLoadingTxDetails}
                />
                <PaymentStatusSummary transaction={transaction} />
                <PaymentStatusSummaryModal
                  transaction={transaction}
                  txDetails={txDetails}
                />
                <Alert
                  style={{ width: "100%" }}
                  subTitle="If you have any question about your order, please contact our team by <NAME_EMAIL>. Please put your invoice number into your subject email."
                  isIcon
                />
              </div>
            </div>
            <Divider orientation="horizontal" state="default" />
            <PaymentStatusActions
              cancelText="Back to Market"
              okText="Buying Dashboard"
              disabledCancel={isLoadingChangePage?.[SEARCH]}
              disabledOk={isLoadingChangePage?.[PROFILE_BUYING]}
              cancelAction={() => goToPage(SEARCH)}
              okAction={() => goToPage(PROFILE_BUYING)}
            />
          </div>
        </div>
      </PaymentStatusCard>
    )
  }

  return (
    <div className="flex min-h-[calc(100vh-100px)] items-center justify-center bg-gray-w-95 p-sm">
      <Text size="base" type="bold" state="primary">
        No data found
      </Text>
    </div>
  )
}

export default PaymentStatus
