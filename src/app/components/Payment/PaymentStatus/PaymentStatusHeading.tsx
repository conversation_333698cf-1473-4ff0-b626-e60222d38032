import { Heading, Text } from "@kickavenue/ui/components/index"
import { cx } from "class-variance-authority"
import { formatPrice } from "@utils/misc"
import { TTransaction } from "types/transaction.type"

const PaymentStatusHeading = ({
  transaction,
  title,
  subTitle,
  subTitleClassName,
}: {
  transaction?: TTransaction
  title?: string
  subTitle?: string
  subTitleClassName?: string
}) => {
  return (
    <div className="flex flex-col items-center">
      <Heading heading="5" textStyle="bold" className="mb-base">
        {title}
      </Heading>
      <Text
        size="base"
        type="regular"
        state="secondary"
        className={cx("mb-xs text-center md:w-[316px]", subTitleClassName)}
      >
        {subTitle}
      </Text>
      <Text size="base" type="bold" state="primary">
        {formatPrice(transaction?.paymentData?.amount as number, null, "IDR")}
      </Text>
    </div>
  )
}

export default PaymentStatusHeading
