import { compact } from "lodash"
import moment, { Moment } from "moment"
import { PaymentSummaryItem } from "@components/Checkout/utils/checkout.utils"
import { MiscConstant } from "@constants/misc"
import { getStripAmount } from "@utils/misc"
import { TAddon } from "types/addonn.type"
import { TListingItem } from "types/listingItem.type"
import { TTransaction, TTransactionPaymentStatus } from "types/transaction.type"
import { TTransactionDetail } from "types/transactionDetail.type"
import { TPaymentInstruction } from "types/payment.typs"
import { EPaymentMethodType } from "types/paymentMethod.type"

const {
  SUMMARY_KICK_POINTS_TITLE,
  SUMMARY_CREDIT_USAGE_TITLE,
  SUMMARY_SELLER_CREDIT_TITLE,
  SUMMARY_KICK_CREDIT_TITLE,
} = MiscConstant

export function getListingFromTxDetails(
  txDetails?: TTransactionDetail[] | null,
): TListingItem | null {
  const nonAddonTxDetails = txDetails?.filter(
    (txDetail) => !txDetail.listingItem?.item?.isAddOn,
  )?.[0]

  if (!nonAddonTxDetails) {
    return null
  }

  return nonAddonTxDetails.listingItem
}

export function getSummaryItemProductPrice(
  txDetails?: TTransactionDetail[] | null,
): PaymentSummaryItem {
  const defaultItem: PaymentSummaryItem = {
    title: "Product Price",
    value: 0,
  }

  if (!txDetails) {
    return defaultItem
  }

  const listing = getListingFromTxDetails(txDetails)

  if (!listing) {
    return defaultItem
  }

  return {
    title: "Product Price",
    value: getStripAmount(listing.sellingPrice),
  }
}

export function getAddonsFromTxDetails(
  txDetails?: TTransactionDetail[] | null,
): TAddon[] {
  const addonListings = txDetails
    ?.filter((txDetail) => txDetail.listingItem?.item?.isAddOn)
    ?.map((txDetail) => txDetail.listingItem)

  const addons = addonListings?.map((listing) => ({
    ...listing,
    selectedQuantity: listing.quantity,
  })) as TAddon[]

  return addons
}

export function getSummaryItemKickPointsUsage(
  transaction?: TTransaction | null,
): PaymentSummaryItem[] {
  const usages: PaymentSummaryItem[] = []

  if (!transaction?.kickPointAmount) {
    return usages
  }

  return [
    {
      title: SUMMARY_KICK_POINTS_TITLE,
      type: "deduction",
      value: -getStripAmount(transaction.kickPointAmount),
      subitems: [],
    },
  ]
}

export function getSummaryItemCreditUsage(
  transaction?: TTransaction | null,
): PaymentSummaryItem[] {
  const title = SUMMARY_CREDIT_USAGE_TITLE
  const usages: PaymentSummaryItem[] = []

  const kickCreditAmount = getStripAmount(transaction?.kickCreditAmount)
  const sellerCreditAmount = getStripAmount(transaction?.sellerCreditAmount)

  const isUsingCredit = kickCreditAmount > 0 || sellerCreditAmount > 0

  if (!isUsingCredit) {
    return usages
  }

  return [
    {
      title,
      value: -(kickCreditAmount + sellerCreditAmount),
      type: "deduction",
      subitems: compact([
        sellerCreditAmount > 0
          ? {
              title: SUMMARY_SELLER_CREDIT_TITLE,
              type: "deduction",
              value: -sellerCreditAmount,
            }
          : null,
        kickCreditAmount > 0
          ? {
              title: SUMMARY_KICK_CREDIT_TITLE,
              type: "deduction",
              value: -kickCreditAmount,
            }
          : null,
      ]) as unknown as PaymentSummaryItem[],
    },
  ]
}

export function isAllowedPaymentStatus(
  transaction?: TTransaction | null,
): boolean {
  const { Processed, WaitingForPayment } = TTransactionPaymentStatus

  return [Processed, WaitingForPayment].includes(
    transaction?.status as TTransactionPaymentStatus,
  )
}

export function getAwaitingPaymentDeadline(
  transaction?: TTransaction | null,
): Moment | undefined | null {
  if (!transaction) {
    return null
  }

  const txCreatedAtJktTime = moment.utc(transaction.createdAt).add(7, "hours")
  if (!txCreatedAtJktTime) {
    return null
  }

  return txCreatedAtJktTime.add(MiscConstant.PAYMENT_DEADLINE_IN_DAYS, "day")
}

export function getAwaitingPaymentDeadlineTimer(
  transaction?: TTransaction | null,
): {
  time: string
  deadline: Moment | undefined
  desc: string
} {
  if (!transaction) {
    return { time: "-", deadline: undefined, desc: "transaction is empty" }
  }

  const deadline = getAwaitingPaymentDeadline(transaction)
  if (!deadline) {
    return { time: "-", deadline: undefined, desc: "deadline is empty" }
  }

  const now = moment().utc().add(7, "hours")

  if (now.isAfter(deadline)) {
    return { time: "00 : 00 : 00", deadline: now, desc: "deadline after now" }
  }

  const duration = moment.duration(deadline.diff(now))
  const hours = Math.floor(duration.asHours()).toString().padStart(2, "0")
  const minutes = duration.minutes().toString().padStart(2, "0")
  const seconds = duration.seconds().toString().padStart(2, "0")

  return {
    time: `${hours} : ${minutes} : ${seconds}`,
    deadline,
    desc: "success",
  }
}

export function getPaymentInstructions() {
  return [
    {
      title: "BCA ATM",
      items: [
        "Insert your BCA ATM Card & PIN",
        'Select "Other Transactions" > "Transfer" > "To BCA Virtual Account"',
        "Enter the 5-digit company code for Kick Avenue (12345) and the phone number registered in your Kick Avenue account (Example: ***************)",
        "On the confirmation page, make sure the payment details are correct, such as VA number, Name, Company/Product, and Total Bill",
        "Enter the Transfer Amount according to the Total Bill",
        "Follow the instructions to complete the transaction",
        "Save the transaction receipt as proof of payment",
      ],
    },
    {
      title: "m-BCA (BCA Mobile)",
      items: [
        "Log in to the BCA Mobile application",
        "Select the m-BCA menu, then enter the m-BCA access code",
        "Select m-Transfer > BCA Virtual Account",
        "Choose from the Transfer List, or enter the 5-digit company code for Kick Avenue (12345) and the phone number registered in your Kick Avenue account (Example: ***************)",
        "Enter your m-BCA pin",
        "Payment is complete. Save the notification that appears as proof of payment",
      ],
    },
    {
      title: "BCA Internet Banking",
      items: [
        "Log in to BCA Internet Banking (https://klikbca.com)",
        'Select the "Bill Payment" menu > "Payment" > "BCA Virtual Account"',
        "In the payment code column, enter the 5-digit company code for Kick Avenue (12345) and the phone number registered in your Tokopedia account (Example: ***************)",
        "On the confirmation page, ensure the payment details are correct, such as the BCA Virtual Account Number, Customer Name, and Payment Amount",
        "Enter your password and mToken",
        "Print/save the BCA Virtual Account payment receipt as proof of payment",
      ],
    },
    {
      title: "BCA Bank Office",
      items: [
        "Take a queue number for Teller transactions and fill out the deposit slip.",
        "Submit the slip and deposit amount to the BCA Teller.",
        "The BCA Teller will validate the transaction.",
        "Keep the validated deposit slip as proof of payment.",
      ],
    },
  ] as TPaymentInstruction[]
}

export function checkTxPaymentType(
  paymentType: EPaymentMethodType | EPaymentMethodType[],
  transaction?: TTransaction | null,
): boolean {
  if (Array.isArray(paymentType)) {
    return paymentType.includes(
      transaction?.paymentData?.paymentMethod?.type as EPaymentMethodType,
    )
  }

  return transaction?.paymentData?.paymentMethod?.type === paymentType
}

export function getPaymentAwaitingOkText(
  transaction?: TTransaction | null,
): string {
  const { VirtualAccount, CreditCard, DebitCard } = EPaymentMethodType

  const isVAPayment = checkTxPaymentType(VirtualAccount, transaction)

  const isCardPayment = checkTxPaymentType([CreditCard, DebitCard], transaction)

  if (isVAPayment) {
    return "Check Payment Status"
  }

  if (isCardPayment) {
    return "Pay Now"
  }

  return "-"
}
