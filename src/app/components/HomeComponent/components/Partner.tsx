"use client"

import { Suspense } from "react"
import DataCarousel from "@app/components/DataCarousel"
import CardPartner from "@app/components/CardPartner"

export interface DataItem {
  id: string
  [key: string]: any
}

const partnersData: DataItem[] = [
  { id: "1", name: "Partner 1" },
  { id: "2", name: "Partner 2" },
  { id: "3", name: "Partner 3" },
  { id: "1", name: "Partner 1" },
  { id: "2", name: "Partner 2" },
  { id: "3", name: "Partner 3" },
]

const renderPartner = () => {
  return <CardPartner />
}

export default function PartnersPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="mx-auto w-full max-w-[1440px] md:px-xxl">
        <DataCarousel data={partnersData} renderItem={renderPartner} />
      </div>
    </Suspense>
  )
}
