import { useRouter } from "next/navigation"
import React, { useCallback } from "react"
import DataCarousel from "@app/components/DataCarousel"
import WishlistSaveModal from "@components/Wishlist/WishlistSaveModal"
import { ModalConstant } from "@constants/modal"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import useProductWishlist from "@shared/ProductList/hook/useProductWishlist"
import { useSearchStore } from "stores/searchStore"
import { Product } from "types/product.type"
import { IMenuWizardSection } from "types/menuWizard"

import CollectionContent from "./CollectionContent"
import CollectionHeader from "./CollectionHeader"
import CollectionItem from "./CollectionItem"
import useAuthSession from "@app/hooks/useAuthSession"

const { WISHLIST_SAVE_PRODUCT_LIST } = ModalConstant.MODAL_IDS

export interface CollectionProps {
  data: IMenuWizardSection
}

const Collection: React.FC<CollectionProps> = ({ data }) => {
  const router = useRouter()
  const { isUnauthenticated } = useAuthSession()
  const [selectedProduct, setSelectedProduct] = React.useState<Product | null>(
    null,
  )

  const sectionContent = data.sectionContent?.[0]

  // Create unique modal ID for this collection
  const modalId = `${WISHLIST_SAVE_PRODUCT_LIST}_${sectionContent?.id}`
  const { productWishlist, setProductWishlist } = useSearchStore()
  const { handleWishlistClick: wishlistClick } = useProductWishlist(
    modalId,
    (_, product) => {
      setProductWishlist(product.id, 0)
    },
  )

  const handleWishlistClick = useCallback(
    (product: any) => {
      const transformedProduct: Product = {
        id: product.id,
        name: product.name,
        skuCode: product.skuCode || "",
        brandIds: [],
        sizeChartId: product.sizeChartId,
        images: product.images || [],
        wishlistId: productWishlist?.[product.id] || 0,
        category: product.category.name || "",
      }
      if (isUnauthenticated) {
        router.push(PageRouteConstant.LOGIN)
        return
      }
      setSelectedProduct(transformedProduct)
      wishlistClick(transformedProduct, productWishlist)
    },
    [isUnauthenticated, router, wishlistClick, productWishlist],
  )

  const renderItem = useCallback(
    (item: any) => (
      <CollectionItem
        key={item.id}
        item={item}
        productWishlist={productWishlist}
        onWishlistClick={handleWishlistClick}
      />
    ),
    [handleWishlistClick, productWishlist],
  )

  // Add params based on collection type and value
  const getCollectionUrl = () => {
    const baseUrl = `/collection/${sectionContent?.slug}`
    const type = sectionContent?.type?.toLowerCase()
    const value = sectionContent?.value
    const defaultSort = sectionContent?.defaultSort

    const searchParams = new URLSearchParams()

    if (sectionContent?.slug) {
      searchParams.set("collectionSlug", sectionContent.slug)
    }

    if (type === "brand" && value) {
      searchParams.set("brandID", value)
    }

    if (type === "keyword" && value) {
      searchParams.set("keyword", value)
    }

    if (defaultSort) {
      searchParams.set("sortBy", defaultSort)
    }

    // Return URL with params if any
    const params = searchParams.toString()

    return params ? `${baseUrl}?${params}` : baseUrl
  }

  if (!sectionContent?.items) return null

  return (
    <>
      <div className="w-full">
        <CollectionHeader
          redirectionUrl={getCollectionUrl()}
          sectionContent={sectionContent}
        />
        <div className="mx-auto flex w-full max-w-[1440px] flex-col gap-lg px-base py-xl md:px-24">
          <CollectionContent
            title={data.title}
            description={data.description}
            redirectionUrl={getCollectionUrl()}
            actionTitle={data.actionTitle}
          />
          <DataCarousel
            data={data.sectionContent?.[0].items || []}
            renderItem={renderItem}
            itemsPerSlide={5}
          />
        </div>
      </div>
      <WishlistSaveModal
        modalId={modalId}
        product={selectedProduct || ({} as Product)}
        onWishlistAdded={(wishlistIds, product) => {
          setProductWishlist(product.id, wishlistIds?.[0] || 0)
        }}
      />
    </>
  )
}

export default Collection
