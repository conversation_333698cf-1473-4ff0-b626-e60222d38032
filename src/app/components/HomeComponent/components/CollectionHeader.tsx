import Image from "next/image"
import Link from "next/link"
import React from "react"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { ISectionContent } from "types/menuWizard"

interface CollectionHeaderProps {
  redirectionUrl: string
  sectionContent?: ISectionContent
}

const CollectionHeader: React.FC<CollectionHeaderProps> = ({
  redirectionUrl,
  sectionContent,
}) => {
  if (!sectionContent) return null

  return (
    <Link href={redirectionUrl}>
      <div className="relative aspect-[1/1] w-full sm:aspect-[16/5]">
        {sectionContent?.imagePortrait && (
          <Image
            src={convertS3UrlToCloudFront(sectionContent.imagePortrait)}
            fill
            style={{ objectFit: "cover" }}
            alt={sectionContent.title ?? `Collection ${sectionContent.id}`}
            className="block md:hidden"
          />
        )}
        {sectionContent?.imageLandscape && (
          <Image
            src={convertS3UrlToCloudFront(sectionContent.imageLandscape)}
            fill
            style={{ objectFit: "cover" }}
            alt={sectionContent.title ?? `Collection ${sectionContent.id}`}
            className="hidden md:block"
          />
        )}
      </div>
    </Link>
  )
}

export default CollectionHeader
