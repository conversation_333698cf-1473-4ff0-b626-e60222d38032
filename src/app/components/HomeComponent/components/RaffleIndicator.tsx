"use client"

import CarouselIndicator from "@kickavenue/ui/components/Carousel/CarouselIndicator"
import { IMenuWizardSection } from "types/menuWizard"

interface RaffleIndicatorProps {
  activeRaffles: IMenuWizardSection[]
  activeIndex: number
  handleGoToIndex: (index: number) => void
}

const RaffleIndicator = ({
  activeRaffles,
  activeIndex,
  handleGoToIndex,
}: RaffleIndicatorProps) => {
  if (activeRaffles.length <= 1) return null

  return (
    <div className="mt-4 flex justify-center gap-2">
      <CarouselIndicator
        activeIndex={activeIndex}
        onClickIndicator={handleGoToIndex}
        className="!h-2 !w-5 !rounded-full transition-colors duration-200"
      >
        {activeRaffles.map((_, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index} className="h-2 w-5 rounded-full bg-gray-b-65" />
        ))}
      </CarouselIndicator>
    </div>
  )
}

export default RaffleIndicator
