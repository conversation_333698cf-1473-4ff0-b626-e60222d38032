"use client"

import { useCarousel } from "@kickavenue/ui/components/Carousel/useCarousel"
import { useCarouselNextPrev } from "@kickavenue/ui/components/Carousel/useCarouselNextPrev"
import { useMemo, useRef, useState } from "react"
import { IMenuWizardSection } from "types/menuWizard"

import RaffleCarousel from "./RaffleCarousel"
import RaffleContent from "./RaffleContent"
import RaffleIndicator from "./RaffleIndicator"

const Raffle = ({ data }: { data: IMenuWizardSection[] }) => {
  const itemRef = useRef<HTMLDivElement>(null)
  const [activeIndex, setActiveIndex] = useState(0)

  const { handleNext, handlePrev } = useCarouselNextPrev({
    itemRef,
    activeIndex,
  })

  const { handleGoToIndex } = useCarousel({
    itemRef,
    ref: null,
    activeIndex,
    setActiveIndex,
    afterChange: () => {},
    handleNext,
    handlePrev,
  })

  const activeRaffles = useMemo(
    () =>
      data?.filter(
        (item) =>
          item.sectionContent?.[0].isActive &&
          new Date(item.sectionContent?.[0].startTime) <= new Date() &&
          new Date(item.sectionContent?.[0].endTime) >= new Date(),
      ),
    [data],
  )

  if (!activeRaffles?.length) return null

  const isSingleRaffle = activeRaffles.length === 1
  const currentRaffle = activeRaffles[activeIndex]

  return (
    <div className="w-full bg-gray-w-95">
      <div className="mx-auto max-w-[1440px] px-6 py-base sm:px-12 sm:py-lg md:px-24 md:py-xl">
        <RaffleCarousel
          isSingleRaffle={isSingleRaffle}
          activeRaffles={activeRaffles}
          itemRef={itemRef}
          handleNext={handleNext}
          handlePrev={handlePrev}
          activeIndex={activeIndex}
        />
        <RaffleContent raffle={currentRaffle} />
        <RaffleIndicator
          activeRaffles={activeRaffles}
          activeIndex={activeIndex}
          handleGoToIndex={handleGoToIndex}
        />
      </div>
    </div>
  )
}

export default Raffle
