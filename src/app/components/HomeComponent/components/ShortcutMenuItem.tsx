import Spinner from "@components/shared/Spinner"
import { Watermark } from "@kickavenue/ui/components/index"
import Text from "@kickavenue/ui/components/Text"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { cx } from "class-variance-authority"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"

interface ShortcutProps {
  text: string
  redirectUrl: string
  imageWeb?: string
}

const ShortcutMenuItem: React.FC<ShortcutProps> = ({
  text,
  redirectUrl,
  imageWeb,
}) => {
  const [imgOnLoad, setImgOnLoad] = useState(false)
  const [imgErr, setImgErr] = useState(false)

  return (
    <Link
      href={redirectUrl}
      className="flex w-full flex-col items-center gap-xs"
    >
      <div className="flex h-[135px] w-full items-center justify-center overflow-hidden rounded-xl bg-gray-w-95">
        <Image
          src={imageWeb ? convertS3UrlToCloudFront(imageWeb) : ""}
          alt="ShortcutMenuItem Image"
          width={100}
          height={100}
          className={cx("h-auto !w-full", imgErr && "hidden")}
          onError={() => setImgErr(true)}
          onLoad={() => setImgOnLoad(true)}
        />

        {!imgOnLoad && !imgErr && (
          <div className="flex size-full items-center justify-center">
            <Spinner />
          </div>
        )}

        {imgErr && (
          <div className="flex size-full items-center justify-center">
            <Watermark className="size-full" logoClassName="!bg-gray-w-95" />
          </div>
        )}
      </div>
      <Text className="" size="base" type="regular" state="secondary">
        {text}
      </Text>
    </Link>
  )
}

export default ShortcutMenuItem
