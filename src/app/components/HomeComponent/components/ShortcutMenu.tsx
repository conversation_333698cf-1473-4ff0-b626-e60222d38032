import ShortcutMenuItem from "./ShortcutMenuItem"
import { IMenuWizardSection } from "types/menuWizard"

interface IShortcutMenuProps {
  data: IMenuWizardSection
}

const ShortcutMenu: React.FC<IShortcutMenuProps> = ({ data }) => {
  const shortcutData =
    data.sectionContent?.map((item) => ({
      id: item.id,
      text: item.name,
      imageWeb: item.imageWeb,
      redirectUrl: item.redirectUrl || "#",
    })) || []

  return (
    <div className="mx-auto w-full max-w-[1440px] px-6 py-base sm:px-12 sm:py-lg md:px-24 md:py-xl">
      <div className="grid gap-lg md:grid-cols-4">
        {shortcutData.map((item) => (
          <ShortcutMenuItem
            key={item.id}
            imageWeb={item.imageWeb}
            text={item.text}
            redirectUrl={item.redirectUrl}
          />
        ))}
      </div>
    </div>
  )
}

export default ShortcutMenu
