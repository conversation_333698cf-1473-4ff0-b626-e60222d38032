/* eslint-disable @typescript-eslint/naming-convention */

import React from "react"
import ProductCard from "@components/shared/ProductCard"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { ISectionContentItem } from "types/menuWizard"

interface CollectionItemProps {
  item: ISectionContentItem
  productWishlist: any
  onWishlistClick: (item: any) => void
}

const CollectionItem: React.FC<CollectionItemProps> = ({
  item,
  productWishlist,
  onWishlistClick,
}) => {
  const firstImage =
    Array.isArray(item.images) && item.images.length > 0 ? item.images[0] : null
  const cloudfrontImage =
    firstImage && typeof firstImage === "string" && firstImage.trim() !== ""
      ? convertS3UrlToCloudFront(firstImage)
      : ""
  const validImageUrl =
    cloudfrontImage && cloudfrontImage.trim() !== ""
      ? cloudfrontImage
      : "/empty-placeholder.png"

  const strikeThroughPrice =
    item.underRetail && item.retailPrice
      ? `Rp ${parseFloat(item.retailPrice.amount).toLocaleString()}`
      : undefined
  const isWishlistActive = productWishlist
    ? Boolean(productWishlist[item.id])
    : false

  return (
    <ProductCard
      cardContainer={{
        className: "w-full cursor-pointer",
        style: {
          width: "100%",
          "@media (minWidth: 768px)": { width: "230.4px" },
        } as any,
      }}
      imageProps={{
        src: validImageUrl,
        width: 160,
        height: 230,
        alt: item.name,
        className:
          "w-full h-auto object-cover !flex !justify-center !items-center",
      }}
      brandName={item.brand.name}
      itemName={item.name}
      itemId={item.id}
      price={`Rp ${parseFloat(item.lowestPrice.amount).toLocaleString()}`}
      isWishlistActive={isWishlistActive}
      strikeThroughPrice={strikeThroughPrice}
      onWishlistClick={() => onWishlistClick(item)}
    />
  )
}

export default CollectionItem
