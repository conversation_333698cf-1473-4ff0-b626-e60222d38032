"use client"

import { snakeCase } from "lodash"
import Image from "next/image"
import { event } from "@lib/gtag"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { useMemberStore } from "stores/memberStore"
import { IMenuWizardSection } from "types/menuWizard"
import { IRedirectionRule } from "types/misc.type"
import { useRouter } from "next/navigation"
import {
  InfiniteCarousel,
  CarouselSlide,
} from "@kickavenue/ui/components/InfiniteCarousel"

interface BannerProps {
  data: IMenuWizardSection
}

const getBannerRedirectionUrl = (
  redirectionRule?: IRedirectionRule,
): string => {
  let redirectionUrl = "#"
  if (!redirectionRule) return redirectionUrl

  const redirection: IRedirectionRule =
    typeof redirectionRule === "string"
      ? JSON.parse(redirectionRule)
      : (redirectionRule as IRedirectionRule)

  let url = ""
  switch (redirection.type) {
    case "COLLECTION":
      redirectionUrl = `/collection/${redirection.keywords}`
      break
    case "ITEM":
      // Redirect to search page with item filter
      redirectionUrl = `/search?keyword=${encodeURIComponent(redirection.keywords)}`
      break
    case "RAFFLE":
      redirectionUrl = `/raffle/${redirection.keywords}`
      break
    case "BRAND":
      // Redirect to search page with brand filter
      redirectionUrl = `/search?brandId=${encodeURIComponent(redirection.keywords)}`
      break
    case "SEARCH":
      redirectionUrl = `/search?keyword=${encodeURIComponent(redirection.keywords)}`
      break
    case "CUSTOM":
      url = redirection.keywords
      redirectionUrl =
        !url.startsWith("http://") && !url.startsWith("https://")
          ? `https://${url}`
          : url
      break
    default:
      redirectionUrl = "#"
      break
  }
  return redirectionUrl
}

const BannerHome = ({ data }: BannerProps) => {
  const router = useRouter()
  const { member } = useMemberStore()

  if (!data) return null

  const transformSectionContent = (data.sectionContent || [])?.map(
    (sectionContent) => {
      const redirectionUrl = getBannerRedirectionUrl(
        sectionContent?.redirectionRule,
      )

      return {
        ...sectionContent,
        redirectionUrl,
        imagePortrait: convertS3UrlToCloudFront(sectionContent.imagePortrait),
        imageLandscape: convertS3UrlToCloudFront(sectionContent.imageLandscape),
        eventClick: () => {
          console.log("redirectionUrl ", redirectionUrl)

          event({
            action: "banner_clicked",
            params: {
              [snakeCase("userId")]: String(member?.id) || "",
              [snakeCase("userPlatform")]: window.navigator.userAgent || "",
              [snakeCase("pageUrl")]: redirectionUrl,
            },
          })

          router.push(redirectionUrl)
        },
      }
    },
  )

  const carouselSlides: CarouselSlide[] = transformSectionContent.map(
    (sectionContent) => {
      const sectionContentId = crypto.randomUUID()
      return {
        id: sectionContentId,
        content: (
          <div
            className={`relative aspect-[1/1] w-full cursor-pointer sm:aspect-[16/5]`}
            onClick={sectionContent.eventClick}
          >
            <Image
              alt={sectionContent.title || `Banner ${sectionContentId}`}
              src={sectionContent.imagePortrait}
              fill
              className="block size-full !object-cover sm:hidden"
              priority
            />
            <Image
              alt={sectionContent.title || `Banner ${sectionContentId}`}
              src={sectionContent.imageLandscape}
              fill
              className="hidden size-full !object-cover sm:block"
              priority
            />
          </div>
        ),
      }
    },
  )

  return (
    <div
      className="relative w-full"
      style={{ backgroundColor: data.backgroundColor }}
    >
      <InfiniteCarousel
        slides={carouselSlides}
        autoplay={true}
        autoplayInterval={5000}
        showNavigation={true}
        showIndicators={true}
      />
    </div>
  )
}

export default BannerHome
