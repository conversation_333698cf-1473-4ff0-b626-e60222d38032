"use client"

import { But<PERSON>, Heading, Text } from "@kickavenue/ui/components"
import Link from "next/link"
import { IMenuWizardSection } from "types/menuWizard"

interface RaffleContentProps {
  raffle: IMenuWizardSection
}

const RaffleContent = ({ raffle }: RaffleContentProps) => {
  return (
    <div className="flex items-center justify-between rounded-b-lg bg-black px-6">
      <div className="flex flex-col gap-xs">
        <Heading heading="5" textStyle="bold" className="!text-white">
          {raffle.title}
        </Heading>
        <Text
          size="base"
          type="regular"
          state="disabled"
          className="!text-white"
        >
          {raffle.sectionContent?.[0]?.description}
        </Text>
      </div>
      <div className="my-6">
        <Link href={`/raffle/${raffle.sectionContent?.[0]?.id ?? "#"}`}>
          <Button size="lg" variant="secondary">
            {raffle.actionTitle}
          </Button>
        </Link>
      </div>
    </div>
  )
}

export default RaffleContent
