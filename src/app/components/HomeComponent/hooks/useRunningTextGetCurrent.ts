import { useQuery } from "@tanstack/react-query"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { runningTextApi } from "@infrastructure/api/runningTextApi"
import { TPaginatedData } from "types/apiResponse.type"
import { IRunningText } from "types/runningText"

const useRunningTextGetCurrent = () => {
  const fetchRunningText = async () => {
    const res = await runningTextApi.getCurrent()
    return res.data as TPaginatedData<IRunningText>
  }

  return useQuery({
    queryKey: [QueryKeysConstant.GET_RUNNING_TEXT_CURRENT],
    queryFn: fetchRunningText,
  })
}

export default useRunningTextGetCurrent
