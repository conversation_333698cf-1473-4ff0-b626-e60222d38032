"use client"

import { snakeCase } from "lodash"
import { useEffect } from "react"
import useFetchMyWishlist from "@components/Wishlist/hooks/useFetchMyWishlist"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"
import { useSearchStore } from "stores/searchStore"
import { EMenuWizardContentType, IMenuWizardSection } from "types/menuWizard"
import { ELocationPageType } from "types/misc.type"
import { Text } from "@kickavenue/ui/components"

import Banner from "./components/Banner"
import Collection from "./components/Collection"
import useMenuWizard from "./hooks/useMenuWizard"
import ShortcutMenu from "./components/ShortcutMenu"
import LoadingPageSkeleton from "../shared/Skeleton/LoadingPageSkeleton"

const HomeComponent = () => {
  const {
    data: menuWizard,
    isLoading,
    error,
  } = useMenuWizard(ELocationPageType.HomePage)
  const { setProductWishlist } = useSearchStore()
  const { member } = useMemberStore()

  const { data: wishlistData } = useFetchMyWishlist()

  useEffect(() => {
    if (wishlistData?.items) {
      wishlistData.items.forEach((item: any) => {
        if (item.id && item.itemId) {
          setProductWishlist(item.itemId, item.id)
        }
      })
    }
  }, [wishlistData, setProductWishlist])

  const renderSection = (section: IMenuWizardSection) => {
    switch (section.contentType) {
      case EMenuWizardContentType.Banner:
        return <Banner key={section.id} data={section} />
      case EMenuWizardContentType.Collection:
        return <Collection key={section.id} data={section} />
      case EMenuWizardContentType.ShortcutMenu:
        return <ShortcutMenu key={section.id} data={section} />
      default:
        return null
    }
  }

  useEffect(() => {
    event({
      action: "page_viewed",
      params: {
        [snakeCase("member_id")]: String(member?.id) || "",
        email: member?.email || "",
        [snakeCase("page_title")]: menuWizard?.title || "Home",
        [snakeCase("page_url")]: window.location.href,
      },
    })
  }, [member?.email, member?.id, menuWizard?.title])

  if (isLoading) {
    return <LoadingPageSkeleton />
  }

  if (error) {
    return (
      <div className="flex min-h-40 flex-col items-center justify-center">
        <Text size="base" state="secondary" type="regular">
          Error loading menu wizard: {error.message}
        </Text>
      </div>
    )
  }

  // Check if menu wizard data exists and has sections
  if (
    !menuWizard ||
    !menuWizard.menuWizardSections ||
    !Array.isArray(menuWizard.menuWizardSections)
  ) {
    return (
      <div className="flex min-h-40 flex-col items-center justify-center">
        <Text size="base" state="secondary" type="regular">
          No menu wizard data available
        </Text>
      </div>
    )
  }

  return (
    <>
      {menuWizard.menuWizardSections.map((section) => renderSection(section))}
    </>
  )
}

export default HomeComponent
