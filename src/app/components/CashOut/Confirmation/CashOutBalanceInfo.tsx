"use client"

import {
  Text,
  IconQuestionmarkCircleBold,
  IconKickCreditBulkColor,
  IconSellerCreditBulkColor,
} from "@kickavenue/ui/components"
import useBalanceType from "@hooks/useBalanceType"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import useGetCurrentBalanceAndFee from "@hooks/useGetCurrentBalanceAndFee"

const CashOutBalanceInfo = () => {
  const { isKickCredit } = useBalanceType()
  const { currentBalance } = useGetCurrentBalanceAndFee()

  const { setOpen } = useModalStore()
  const { KICK_CREDIT_INFO, SELLER_CREDIT_INFO } = ModalConstant.MODAL_IDS

  const handleOpenCreditInfo = () => {
    if (isKickCredit) {
      setOpen(true, KICK_CREDIT_INFO)
    } else {
      setOpen(true, SELLER_CREDIT_INFO)
    }
  }

  const textContents = {
    title: isKickCredit ? "Kick Credit" : "Seller Credit",
    creditBalance: isKickCredit
      ? "Kick Credit Balance"
      : "Seller Credit Balance",
    icon: isKickCredit ? (
      <IconKickCreditBulkColor className="size-6" />
    ) : (
      <IconSellerCreditBulkColor className="size-6" />
    ),
  }

  return (
    <div className="flex flex-col">
      <Text size="base" type="bold" state="primary">
        {textContents.title}
      </Text>
      <div className="mt-3 flex flex-col gap-2 rounded-sm border border-solid border-gray-w-80 p-sm">
        <div className="flex items-center gap-3">
          <div className="text-[#0B7A68]">{textContents.icon}</div>
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-1">
              <Text size="sm" state="primary" type="bold">
                {textContents.creditBalance}
              </Text>
              <IconQuestionmarkCircleBold
                className="size-4 cursor-pointer"
                onClick={handleOpenCreditInfo}
              />
            </div>
            <Text size="sm" state="secondary" type="regular">
              {currentBalance}
            </Text>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CashOutBalanceInfo
