"use client"

import { Text } from "@kickavenue/ui/components"
import { useFormContext } from "react-hook-form"
import useBalanceType from "@app/hooks/useBalanceType"
import { formatPrice } from "@utils/misc"
import useGetCurrentBalanceAndFee from "@hooks/useGetCurrentBalanceAndFee"
import { MiscConstant } from "@constants/misc"

const DEFAULT_IDR_ZERO = MiscConstant.DEFAULT_IDR_ZERO

const CashOutSummary = () => {
  const { isSellerCredit } = useBalanceType()
  const { currentFee, currentFeeNumber } = useGetCurrentBalanceAndFee()
  const { watch } = useFormContext()

  const amount = Number(watch("amount"))

  const cashOutAmount = amount
    ? formatPrice(amount, null, "IDR", DEFAULT_IDR_ZERO)
    : DEFAULT_IDR_ZERO

  const totalAmount = isSellerCredit
    ? formatPrice(
        Math.max(0, amount - currentFeeNumber),
        null,
        "IDR",
        DEFAULT_IDR_ZERO,
      )
    : cashOutAmount

  return (
    <div className="flex flex-col">
      <Text size="sm" type="bold" state="primary">
        Cash Out Summary
      </Text>
      <div className="mt-3 rounded-t-xl border border-gray-w-80 p-3 text-sm text-gray-b-65">
        <div className="flex justify-between">
          <Text size="sm" type="regular" state="primary">
            Cash Out Amount
          </Text>
          <Text size="sm" type="regular" state="primary">
            {cashOutAmount}
          </Text>
        </div>
        {isSellerCredit && (
          <div className="mt-3 flex justify-between">
            <Text size="sm" type="regular" state="primary">
              Cash Out Fee
            </Text>
            <Text size="sm" type="regular" state="danger">
              - {currentFee}
            </Text>
          </div>
        )}
      </div>
      <div className="flex items-center justify-between rounded-b-xl border border-t-0 border-gray-w-80 px-3 py-[14px]">
        <Text size="sm" type="bold" state="primary">
          Total Cash Out
        </Text>
        <Text size="sm" type="bold" state="primary">
          {totalAmount}
        </Text>
      </div>
    </div>
  )
}

export default CashOutSummary
