import { <PERSON>ton, RadioButton, Text } from "@kickavenue/ui/dist/src/components"
import { useFormContext } from "react-hook-form"
import useGetAllMyBankAccount from "@app/hooks/useGetAllMyBankAccount"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { Bank } from "types/bank.type"
import useGetAllBankInfo from "@app/hooks/useGetAllBankInfo"
import { getBankIcon } from "@components/Profile/bank-account/BankAccountCard"

const { BANK_SELECTION } = ModalConstant.MODAL_IDS

const BankSelectionModal = () => {
  const { setOpen } = useModalStore()
  const { bankData } = useGetAllMyBankAccount()

  const { setValue, trigger, watch } = useFormContext()

  const selectedBank = watch("selectedBank")

  const handleSelectBank = (bank: Bank) => {
    setValue("bankId", bank.id)
    setValue("selectedBank", bank)
    trigger("selectedBank")
  }

  const handleClose = () => {
    setOpen(false, BANK_SELECTION)
  }

  const handleConfirm = () => {
    setOpen(false, BANK_SELECTION)
  }

  const { data: allBankInfo } = useGetAllBankInfo({
    ids: bankData?.map((bank) => bank.bankId) || [],
    page: 0,
    pageSize: bankData.length,
  })

  const getBankInfo = (bankId: number) => {
    return allBankInfo?.content.find((bank) => bank.id === bankId)
  }

  return (
    <Modal modalId={BANK_SELECTION}>
      <HeaderModal title="Cash Out To" onClose={handleClose} />
      <div className="flex flex-col gap-base p-lg">
        {bankData?.map((bank) => (
          <div
            key={bank.id}
            className="flex items-center justify-between gap-2 rounded-xl border border-gray-w-80 p-4"
          >
            <div className="flex w-full items-center gap-2">
              {getBankIcon(getBankInfo(bank.bankId)?.bankCode as string)}
              <div className="flex flex-col">
                <Text size="base" state="primary" type="bold">
                  {bank.accountHolder}
                </Text>
                <Text size="sm" state="secondary" type="regular">
                  {getBankInfo(bank.bankId)?.bankName}
                  <span className="mx-1 font-bold">·</span> {bank.accountNumber}
                </Text>
              </div>
            </div>
            <RadioButton
              checked={selectedBank?.id === bank.id}
              onChange={() => handleSelectBank(bank)}
            />
          </div>
        ))}
      </div>
      <ModalFooter>
        <div className="col-span-12">
          <Button
            variant="primary"
            className="!w-full"
            size="lg"
            onClick={handleConfirm}
            disabled={!selectedBank}
          >
            Confirm
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}

export default BankSelectionModal
