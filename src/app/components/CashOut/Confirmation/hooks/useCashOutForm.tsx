import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"

import { createCashOutSchema, CashOutFormValues } from "../schema/cashOutSchema"

const useCashOutForm = () => {
  const { currentBalanceNumber, currentBalance } = useGetCurrentBalanceAndFee()

  const form = useForm<CashOutFormValues>({
    defaultValues: {
      amount: "",
      bankId: null,
      selectedBank: null,
      password: "",
    },
    resolver: zodResolver(
      createCashOutSchema(currentBalanceNumber, currentBalance),
    ),
  })

  return {
    form,
  }
}

export default useCashOutForm
