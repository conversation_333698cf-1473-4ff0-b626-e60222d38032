"use client"

import { Divider } from "@kickavenue/ui/components"
import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { snakeCase } from "lodash"
import CenterWrapper from "@components/shared/CenterWrapper"
import useSearchProductCollection from "@app/hooks/useSearchProductCollection"
import { MiscConstant } from "@constants/misc"
import { useCollection } from "@app/hooks/useCollection"
import SearchLoading from "@components/ExpandedSearch/SearchLoading"
import { TProductFilterKey } from "types/product.type"
import SearchResultAndSort from "@components/Search/SearchResultAndSort"
import { useMiscStore } from "stores/miscStore"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"

import FilterContent from "../Search/FilterContent"
import FilterDrawer from "../Search/FilterDrawer"
import SearchChips from "../Search/SearchChips"

import CollectionBreadcrumb from "./CollectionBreadcrumb"
import CollectionResult from "./CollectionResult"
import { CollectionHeader } from "./CollectionHeader"

interface CollectionContainerProps {
  slug: string
}

const { SORT_BY_OPTIONS } = MiscConstant

const getSortValue = (defaultSort: string) => {
  switch (defaultSort) {
    case "LOW_TO_HIGH":
      return SORT_BY_OPTIONS.PRICE_LOW_TO_HIGH.value
    case "HIGH_TO_LOW":
      return SORT_BY_OPTIONS.PRICE_HIGH_TO_LOW.value
    case "RECENTLY_ADDED":
      return SORT_BY_OPTIONS.RECENTLY_ADDED.value
    default:
      return defaultSort
  }
}

const CollectionContainer = ({ slug }: CollectionContainerProps) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setSearchKeyword } = useMiscStore()
  const { data: collection, isLoading } = useCollection({ slug })
  const [initialized, setInitialized] = useState(false)
  const { member } = useMemberStore()

  // Initialize params only if they don't exist
  useEffect(() => {
    if (!collection || initialized || searchParams?.toString()) {
      return
    }

    const params = new URLSearchParams()

    // Add sort parameter
    if (collection.defaultSort) {
      params.set(TProductFilterKey.SortBy, getSortValue(collection.defaultSort))
    }

    params.set("collectionSlug", slug)

    if (params.toString()) {
      router.replace(`?${params.toString()}`)
    }
    setInitialized(true)
  }, [collection, initialized, router, searchParams, slug])

  // Clear search keyword on unmount
  useEffect(() => {
    return () => {
      setSearchKeyword("")
    }
  }, [setSearchKeyword])

  const { fetchNextPage, hasNextPage, isFetchingNextPage } =
    useSearchProductCollection()

  useEffect(() => {
    event({
      action: "collection_viewed",
      params: {
        [snakeCase("member_id")]: String(member?.id) || "",
        [snakeCase("user_platform")]: window.navigator.userAgent || "",
        [snakeCase("collection_name")]: collection?.name || "",
      },
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  if (isLoading) return <SearchLoading />

  return (
    <div className="min-h-screen">
      <CollectionHeader collection={collection || undefined} />

      <Divider orientation="horizontal" />

      <div className="px-base py-xl">
        <CollectionBreadcrumb collectionName={collection?.name || ""} />
      </div>

      <CenterWrapper className="!mb-xl md:!gap-lg md:!px-xxl md:!py-0">
        <div className="sticky top-[150px] hidden overflow-y-auto md:col-span-3 md:block">
          <FilterContent />
        </div>
        <div className="col-span-12 min-h-[50vh] md:col-span-9">
          <SearchResultAndSort />
          <SearchChips />
          <div className="mb-base md:mb-xl" />
          <CollectionResult
            fetchNextPage={fetchNextPage}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
          />
        </div>
      </CenterWrapper>
      <FilterDrawer />
    </div>
  )
}

export default CollectionContainer
