"use client"

import {
  BadgeCount,
  Breadcrumb,
  IconFilterOutline,
} from "@kickavenue/ui/components"
import SlashSeperator from "@shared/SlashSeparator"
import CenterWrapper from "@shared/CenterWrapper"
import { useMiscStore } from "stores/miscStore"
import useSearchFilter from "@hooks/useSearchFilter"

const CollectionBreadcrumb = ({
  collectionName,
}: {
  collectionName: string
}) => {
  const { setShowFilterDrawer } = useMiscStore()
  const { filterCount } = useSearchFilter()
  const list = [
    { name: "Home", path: "/" },
    { name: "Search", path: "/search" },
    { name: collectionName, path: "" },
  ]

  const renderBadgeCount = () => {
    if (!filterCount) {
      return null
    }
    return (
      <BadgeCount
        type="information"
        count={filterCount}
        size="md"
        className="absolute right-[7px] top-[7px]"
      />
    )
  }

  return (
    <div className="relative pl-0 pt-base md:pt-md">
      <CenterWrapper className="items-center !px-5 !py-0">
        <div className="col-span-2 md:col-span-6">
          <Breadcrumb listItem={list} separator={<SlashSeperator />} />
        </div>
        <div className="col-span-2 flex justify-end md:hidden">
          <IconFilterOutline
            width={24}
            height={24}
            onClick={() => setShowFilterDrawer(true)}
          />
          {renderBadgeCount()}
        </div>
      </CenterWrapper>
    </div>
  )
}

export default CollectionBreadcrumb
