import { useEffect, useRef, useState } from "react"
import { useInfiniteQuery } from "@tanstack/react-query"
import { debounce } from "lodash"
import { TVoucher } from "types/voucher.type"
import { VoucherApiRepository } from "@infrastructure/repositories/voucherApiRepository"
import { GetVouchers } from "@application/usecases/getVouchers"
import { TPaginatedData } from "types/apiResponse.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

export const useMyVoucherActive = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const voucherRepository = new VoucherApiRepository()
  const [searchQuery, setSearchQuery] = useState<string | undefined>(undefined)

  const query = useInfiniteQuery({
    queryKey: [QueryKeysConstant.GET_VOUCHER_ACTIVE, searchQuery],
    initialPageParam: 0,
    queryFn: async ({ pageParam }: { pageParam: number }) => {
      const usecase = new GetVouchers(voucherRepository)
      const result = await usecase.getAllVoucherActive({
        search: searchQuery,
        page: pageParam,
        pageSize: 10,
      })
      return result ?? []
    },
    getNextPageParam: (lastPage: { page: number; totalPages: number }) => {
      if (lastPage.page < lastPage.totalPages - 1) {
        return lastPage.page + 1
      }
      return null
    },
  })
  const mappedData = query.data?.pages
    .map((page: TPaginatedData<TVoucher>) => page.content)
    .flat()
  useEffect(() => {
    const handleScroll = () => {
      const container = scrollContainerRef.current
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container
        if (
          scrollTop + clientHeight >= scrollHeight - 2 &&
          query.hasNextPage &&
          !query.isFetchingNextPage
        ) {
          query.fetchNextPage()
        }
      }
    }

    const container = scrollContainerRef.current
    container?.addEventListener("scroll", handleScroll)
    return () => container?.removeEventListener("scroll", handleScroll)
  }, [query, query.hasNextPage, query.isFetchingNextPage])

  const debounceSearchVoucher = debounce((value: string) => {
    setSearchQuery(value)
  }, 300)

  const searchVoucher = (value: string) => {
    debounceSearchVoucher(value)
  }

  return {
    items: mappedData,
    searchVoucher,
    isSuccess: query.isSuccess,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    scrollContainerRef,
    searchQuery,
  }
}
