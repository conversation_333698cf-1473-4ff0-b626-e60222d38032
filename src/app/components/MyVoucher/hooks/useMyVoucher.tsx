import { useCallback, useState } from "react"
import { tabItems } from "@components/MyVoucher/utils/myvoucher.utils"
import { TVoucher } from "types/voucher.type"
import { TabMenu } from "@components/shared/TabBar/TabBar.utils"
import ListVoucherActive from "@components/MyVoucher/list/ListVoucherActive"
import ListVoucherHistory from "@components/MyVoucher/list/ListVoucherHistory"

export const useMyVoucher = () => {
  const [tab, setTab] = useState(tabItems[0])

  const [selectedVoucher, setSelectedVoucher] = useState<TVoucher>(
    {} as TVoucher,
  )

  const handleChangeTab = useCallback((newtab: TabMenu) => {
    setTab(newtab)
  }, [])

  const renderTabComponent = () => {
    switch (tab.id) {
      case 0:
        return <ListVoucherActive />
      case 1:
        return <ListVoucherHistory />
    }
  }

  return {
    tab,
    handleChangeTab,
    renderTabComponent,
    selectedVoucher,
    setSelectedVoucher,
  }
}
