import React from "react"
import Empty from "@kickavenue/ui/components/Empty"
import { Button } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { useRouter } from "next/navigation"
import MyVoucherCard from "@components/MyVoucher/voucher-card/MyVoucherCard"
import { TVoucher } from "types/voucher.type"
import { formatStripePrice } from "@utils/misc"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import VoucherSkeleton from "@components/shared/VoucherSkeleton"
import Loading from "@components/shared/Spinner"

import SearchVoucher from "./SearchVoucher"

export interface ListVoucherProps {
  items: any
  type: "active" | "history"
  onModalOpen: (item: TVoucher) => void
  singleGrid?: boolean
  searchVoucher: (value: string) => void
  isSuccess: boolean
  isLoading: boolean
  isFetchingNextPage?: boolean
  scrollContainerRef: React.RefObject<HTMLDivElement | null>
  searchQuery?: string
}

export default function ListVoucher(props: ListVoucherProps) {
  const {
    type,
    items,
    searchVoucher,
    onModalOpen,
    singleGrid = false,
    isSuccess,
    isLoading,
    isFetchingNextPage,
    scrollContainerRef,
    searchQuery,
  } = props

  const router = useRouter()
  const handleRedirect = () => {
    router.push(PageRouteConstant.SEARCH)
  }

  const gridSize = singleGrid ? "md:grid-cols-1" : "md:grid-cols-2"
  const renderEmpty = () => {
    if (searchQuery) {
      return (
        <Empty
          title="Voucher not found"
          subText="Please check the search query and try again."
          actionButton={<></>}
          className="[&>img]:w-[200px]"
        />
      )
    }
    return (
      <Empty
        title="No Vouchers Available"
        subText="You currently don't have any vouchers. Check back later for exciting offers!"
        actionButton={<Button onClick={handleRedirect}>Explore Deals</Button>}
        className="[&>img]:w-[200px]"
      />
    )
  }

  const renderSkeletonLoading = () => {
    if (isLoading) {
      return (
        <>
          {Array.from({ length: 4 }, (_, index) => (
            <VoucherSkeleton key={`skeleton-${index}`} />
          ))}
        </>
      )
    }
    return null
  }

  return (
    <div className="flex h-full flex-col gap-y-md">
      {(items?.length !== 0 || searchQuery) && (
        <SearchVoucher onChange={(value) => searchVoucher(value)} />
      )}
      {isSuccess && items?.length === 0 && (
        <div className="mt-xxl flex h-full items-center justify-center">
          {renderEmpty()}
        </div>
      )}
      <div className="flex-1 overflow-hidden">
        <div
          ref={scrollContainerRef}
          className={cx(
            "grid h-full grid-cols-1 gap-md overflow-y-auto pb-[100px]",
            gridSize,
          )}
        >
          {renderSkeletonLoading()}
          {isSuccess &&
            items?.map((item: any) => {
              const { id, title, code, minimumPurchase, endDate } =
                type === "active" ? item : item.voucher
              return (
                <div className="h-fit" key={id}>
                  <MyVoucherCard
                    title={title}
                    validUntil={endDate}
                    minPurchaseAmount={formatStripePrice(minimumPurchase)}
                    code={code}
                    type={type}
                    item={item}
                    onHint={onModalOpen}
                  />
                </div>
              )
            })}
        </div>
      </div>
      {isFetchingNextPage && (
        <div className="flex items-center justify-center">
          <Loading />
        </div>
      )}
    </div>
  )
}
