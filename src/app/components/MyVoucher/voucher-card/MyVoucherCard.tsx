import VoucherCard from "@kickavenue/ui/components/Card/VoucherCard"
import React from "react"
import { formatDateFull } from "@utils/misc"
import { useModalStore } from "stores/modalStore"
import { TVoucher } from "types/voucher.type"
import useToast from "@app/hooks/useToast"
import { ModalConstant } from "@constants/modal"

import styles from "./MyVoucherCard.module.css"

export interface MyVoucherCardProps {
  item: TVoucher
  title: string
  code: string
  minPurchaseAmount: string
  validUntil: string
  type: "active" | "history" | "pressed"
  onHint?: (item: TVoucher) => void
  onClick?: () => void
}

export default function MyVoucherCard(props: MyVoucherCardProps) {
  const {
    title,
    code,
    minPurchaseAmount,
    validUntil,
    onHint = () => {},
    item,
    onClick = () => {},
  } = props

  const { setShowToast } = useToast()

  const handleCopy = async () => {
    await navigator.clipboard.writeText(code)
    setShowToast(true, "Voucher successfully copied!")
  }

  const handleHint = () => {
    onHint(item)
    setOpen(true, ModalConstant.MODAL_IDS.VOUCHER_DETAIL, item)
  }

  const { setOpen } = useModalStore()

  const validUntilDate = `Valid until ${formatDateFull(validUntil)}`
  const minPurchaseFormatted = `Minimum Purchase of ${minPurchaseAmount}`

  const cardState = () => {
    if (props.type === "pressed") {
      return "pressed"
    } else if (props.type === "history") {
      return "disabled"
    }
    return "default"
  }

  return (
    <VoucherCard
      title={title}
      state={cardState()}
      validUntil={validUntilDate}
      minumunPurchase={minPurchaseFormatted}
      code={code}
      onCopy={handleCopy}
      onHint={handleHint}
      className={styles["voucher-card"]}
      onClick={onClick}
    />
  )
}
