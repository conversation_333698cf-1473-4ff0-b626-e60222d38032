import {
  Divider,
  IconAddFriendBulkGreen,
  IconMoneyAddBulkGreen,
  Text,
} from "@kickavenue/ui/components"
import React from "react"
import { formatDateFull } from "@utils/misc"

export interface VoucherBannerInfoProps {
  minPurchaseAmount: string
  validUntil: string
}

export default function VoucherBannerInfo(props: VoucherBannerInfoProps) {
  const { minPurchaseAmount, validUntil } = props
  const validUntilDate = `${formatDateFull(validUntil || "")}`

  return (
    <div className="flex w-full items-center justify-evenly rounded-sm bg-gray-w-95 p-sm">
      <div className="flex items-center">
        <IconAddFriendBulkGreen className="mx-4 scale-150" />
        <div className="flex flex-col items-start">
          <Text size="xs" state="secondary" type="regular">
            Minimum Purchase
          </Text>
          <Text size="base" state="primary" type="bold">
            {minPurchaseAmount}
          </Text>
        </div>
      </div>
      <div className="h-10">
        <Divider type="solid" orientation="vertical" />
      </div>
      <div className="flex items-center">
        <IconMoneyAddBulkGreen className="mx-4 scale-150" />
        <div className="flex flex-col items-start">
          <Text size="xs" state="secondary" type="regular">
            Valid Until
          </Text>
          <Text size="base" state="primary" type="bold">
            {validUntilDate}
          </Text>
        </div>
      </div>
    </div>
  )
}
