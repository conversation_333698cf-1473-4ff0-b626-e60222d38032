import React from "react"
import {
  Modal as ModalContainer,
  IconCloseOutline,
} from "@kickavenue/ui/components"
import Image from "next/image"
import Modal from "@shared/Modal"
import { useModalStore } from "stores/modalStore"
import { TVoucher } from "types/voucher.type"
import { ModalConstant } from "@constants/modal"
import { convertS3UrlToCloudFront, formatStripePrice } from "@utils/misc"

import VoucherBannerInfo from "./VoucherBannerInfo"
import VoucherCode from "./VoucherCode"
import VoucherDescription from "./VoucherDescription"

const { CHECKOUT_VOUCHER } = ModalConstant.MODAL_IDS

export default function VoucherModal() {
  const { setOpen, modalData, prevModal } = useModalStore()
  const {
    title,
    code,
    description,
    minimumPurchase,
    endDate,
    imageUrl,
    termAndConditions,
  } = (modalData as TVoucher) || {}
  const modalId = ModalConstant.MODAL_IDS.VOUCHER_DETAIL

  const handleClose = () => {
    if (prevModal?.modalId === CHECKOUT_VOUCHER) {
      setOpen(true, CHECKOUT_VOUCHER)
    } else {
      setOpen(false)
    }
  }

  return (
    <Modal modalId={modalId}>
      <ModalContainer
        title={title}
        trailing={
          <IconCloseOutline className="cursor-pointer" onClick={handleClose} />
        }
        open
      >
        <div className="flex flex-col items-center justify-center gap-y-sm">
          {imageUrl && (
            <div
              style={{ position: "relative", width: "100%", height: "140px" }}
            >
              <Image
                src={convertS3UrlToCloudFront(imageUrl)}
                alt="banner"
                fill
                className="rounded-base"
                style={{ objectFit: "cover" }}
              />
            </div>
          )}
          <VoucherBannerInfo
            validUntil={endDate}
            minPurchaseAmount={formatStripePrice(minimumPurchase)}
          />
          <VoucherCode code={code} />
          <VoucherDescription
            description={description}
            termAndConditions={termAndConditions}
          />
        </div>
      </ModalContainer>
    </Modal>
  )
}
