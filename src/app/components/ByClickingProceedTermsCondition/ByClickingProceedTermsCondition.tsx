import { Text, Button } from "@kickavenue/ui/components"
import Link from "next/link"
import { PageRouteConstant } from "@constants/pageRoute.constant"

const ByClickingProceedTermsCondition = () => {
  return (
    <div className="flex items-center justify-center">
      <Text size="sm" type="regular" state="secondary">
        By clicking proceed, you agree to our
      </Text>
      <Link href={PageRouteConstant.TERMS_CONDITIONS} className="ml-2 flex">
        <Button size="md" variant="link" style={{ padding: "0" }}>
          Terms & Conditions
        </Button>
      </Link>
    </div>
  )
}

export default ByClickingProceedTermsCondition
