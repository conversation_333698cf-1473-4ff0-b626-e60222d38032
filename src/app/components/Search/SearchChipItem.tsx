import { Fragment } from "react"
import { Chip, IconCloseOutline, Text } from "@kickavenue/ui/components"
import ClickableDiv from "@components/shared/ClickableDiv"

export interface SearchChipItemProps {
  filter: Record<string, string>
  handleRemoveItemFilter: (item: Record<string, string>) => void
  handleRemoveAllFilter: () => void
}

const SearchChipItem = ({
  filter,
  handleRemoveItemFilter,
  handleRemoveAllFilter,
}: SearchChipItemProps) => {
  if (filter.key === "clear-all") {
    return (
      <ClickableDiv
        keyDownHandler={handleRemoveAllFilter}
        onClick={handleRemoveAllFilter}
      >
        <Chip size="sm">{filter.value}</Chip>
      </ClickableDiv>
    )
  }
  return (
    <>
      <Chip size="sm">
        <Text size="sm" type="regular" state="primary">
          {filter.value}
        </Text>
        <IconCloseOutline
          className="cursor-pointer"
          onClick={() => handleRemoveItemFilter(filter)}
        />
      </Chip>
    </>
  )
}

export default SearchChipItem
