import { useCallback, useEffect } from "react"
import { ItemConstant } from "@constants/item"
import useSearchFilter from "@hooks/useSearchFilter"
import { getAppliedFilters } from "@utils/search"

import SearchChipItem from "./SearchChipItem"
import { useGetQueryParams } from "@app/hooks/useGetQueryParams"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { MiscConstant } from "@constants/misc"
import { TProductFilterKey } from "types/product.type"

const { FILTER_KEYS } = ItemConstant
const {
  SizeId,
  Size,
  Category,
  CategoryId,
  SubCategory,
  SubCategoryId,
  Brand,
  BrandId,
} = TProductFilterKey
const { CATEGORY_IS_SNEAKERS_OR_APPAREL } = MiscConstant

const SearchChips = () => {
  const queryParams = useGetQueryParams()
  const replaceQueryParams = useReplaceQueryParams()

  const { filter } = useSearchFilter()
  const appliedFilters = getAppliedFilters(filter)

  // handle remove checkbox filter
  // for now used for sub category, brand and size
  const handleRemoveCheckboxFilter = useCallback(
    (item: Record<string, string>, keys: string[]) => {
      const labelItems = queryParams.all?.[keys[0]]
      const valueItems = queryParams.all?.[keys[1]]

      if (Array.isArray(labelItems)) {
        const findIndex = labelItems?.findIndex(
          (value: string) => value === item.value,
        )

        if (findIndex !== -1) {
          labelItems.splice(findIndex, 1)
          valueItems.splice(findIndex, 1)
        }

        replaceQueryParams({
          [keys[1]]: valueItems,
          [keys[0]]: labelItems,
        })
      } else {
        replaceQueryParams({}, keys)
      }
    },
    [replaceQueryParams, queryParams.all],
  )

  const handleRemoveItemFilter = useCallback(
    (item: Record<string, string>) => {
      console.log("item handleRemoveItemFilter ", item)

      // handle remove category filter
      if (item.key === Category) {
        const keys = [Category, CategoryId, SubCategory, SubCategoryId]

        if (
          CATEGORY_IS_SNEAKERS_OR_APPAREL.includes(item.value.toLowerCase())
        ) {
          keys.push(Size, SizeId)
        }

        replaceQueryParams({}, keys)
        return
      }

      // handle remove sub category filter
      if (item.key === SubCategory) {
        handleRemoveCheckboxFilter(item, [SubCategory, SubCategoryId])
        return
      }

      // handle remove brand filter
      if (item.key === Brand) {
        handleRemoveCheckboxFilter(item, [Brand, BrandId])
        return
      }

      // handle remove size filter
      if (item.key === Size) {
        handleRemoveCheckboxFilter(item, [Size, SizeId])
        return
      }

      const params = new URLSearchParams(queryParams.all)

      const pairedKeys: Record<string, string> = {
        brand: "brandID",
        category: "categoryID",
        subcategory: "subcategoryID",
        size: "sizeID",
      }

      for (const [key, value] of Object.entries(filter)) {
        const values = (value as string)?.split(",")
        let filterValue = ""

        const cleaned = item?.value.replace(/[^\d-]/g, "")
        const [startStr, endStr] = cleaned.split("-")
        const start = parseInt(startStr, 10)
        const end = parseInt(endStr, 10)
        const result = start === 0 ? `-${end}` : cleaned

        switch (key) {
          case FILTER_KEYS.PRICE_RANGE:
            filterValue = result
            break
          case FILTER_KEYS.KEYWORD:
            filterValue = item?.value?.replace(/Search: /g, "")
            break
          default:
            filterValue = item?.value
            break
        }

        if (key === item.key) {
          const pairedIDKey = pairedKeys[key]
          if (pairedIDKey && params.has(pairedIDKey)) {
            const labelList = params.get(key)?.split(",") || []
            const idList = params.get(pairedIDKey)?.split(",") || []

            const indexToRemove = labelList.indexOf(filterValue)
            if (indexToRemove !== -1) {
              labelList.splice(indexToRemove, 1)
              idList.splice(indexToRemove, 1)

              if (labelList.length > 0) {
                params.set(key, labelList.join(","))
                params.set(pairedIDKey, idList.join(","))
              } else {
                params.delete(key)
                params.delete(pairedIDKey)
              }
            }
            continue
          }

          const updatedValues = values.filter((v) => v !== filterValue)
          if (updatedValues.length > 0) {
            params.set(key, updatedValues.join(","))
          } else {
            params.delete(key)
          }
        } else {
          params.set(key, value as string)
        }
      }

      replaceQueryParams({
        ...params,
      })
    },
    [filter, replaceQueryParams, queryParams.all, handleRemoveCheckboxFilter],
  )

  const handleRemoveAllFilter = useCallback(() => {
    replaceQueryParams({}).removeAll()
  }, [replaceQueryParams])

  useEffect(() => {
    console.log("queryParams.all ", queryParams.all)
  }, [queryParams.all])

  if (!appliedFilters.length) return null

  return (
    <>
      <div className="mb-base" />
      <div className="flex justify-center">
        <div className="w-full max-w-[1440px]">
          <div className="flex w-full max-w-[1440px] flex-wrap gap-xs">
            {appliedFilters.map((filter) => (
              <SearchChipItem
                key={filter.value}
                filter={filter}
                handleRemoveItemFilter={handleRemoveItemFilter}
                handleRemoveAllFilter={handleRemoveAllFilter}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  )
}

export default SearchChips
