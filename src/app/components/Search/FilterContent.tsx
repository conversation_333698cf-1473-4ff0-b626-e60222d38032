import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "@kickavenue/ui/components"
import { usePathname, useRouter } from "next/navigation"
import { useCallback } from "react"
import FilterGroup from "@components/Filter"
import { condition, gender, shippingMethod } from "@utils/filterData"
import useSearchFilter from "@app/hooks/useSearchFilter"
import FilterCategory from "@components/Filter/components/FilterCategory"
import FilterBrand from "@components/Filter/components/FilterBrand"
import FilterSubCategory from "@components/Filter/components/FilterSubCategory"
import FilterSize from "@components/Filter/components/FilterSize"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { PARAMS_SEARCH } from "@constants/params.constant"
import { createValueManager } from "@utils/string.utils"
import { useMiscStore } from "stores/miscStore"
import { ESizeGender } from "types/sizeChart.type"

const FilterContent = () => {
  const router = useRouter()
  const pathname = usePathname()
  const replaceQueryParams = useReplaceQueryParams()
  const { setSearchKeyword } = useMiscStore()
  const { isFilterEmpty } = useSearchFilter()

  const isCollectionPage = pathname?.includes(PageRouteConstant.COLLECTION)

  const onResetClick = useCallback(() => {
    const uniqueParamsSearch = createValueManager(
      Array.from(new Set(Object.values(PARAMS_SEARCH).flat())),
    ).removeValues(["sortBy"])

    if (isCollectionPage) {
      setSearchKeyword("")
      replaceQueryParams({}, uniqueParamsSearch)
    } else {
      router.push(PageRouteConstant.SEARCH)
    }
  }, [isCollectionPage, replaceQueryParams, router, setSearchKeyword])

  return (
    <>
      <div className="flex justify-between">
        <div className="text-lg font-semibold">Filter</div>
        <Button
          size="lg"
          variant="link"
          className="!p-0 !text-base"
          onClick={onResetClick}
          disabled={isFilterEmpty}
        >
          Reset
        </Button>
      </div>
      <Space type="margin" direction="y" size="base" />
      <Divider type="solid" orientation="horizontal" />
      <FilterCategory />
      <FilterSubCategory />
      <FilterBrand />
      <FilterGroup
        title="Shipping Method"
        type="default"
        items={shippingMethod}
      />
      <FilterGroup title="Gender" type="default" items={gender} />
      <FilterSize title="Size Men" gender={ESizeGender.MEN} />
      <FilterSize title="Size Women" gender={ESizeGender.WOMEN} />
      <FilterSize title="Size Kid" gender={ESizeGender.KID} />
      <FilterGroup title="Condition" type="default" items={condition} />
      <FilterGroup
        title="Price Range"
        type="priceRange"
        currency="IDR"
        onPriceChange={() => {}}
        className="!mb-0"
      />
    </>
  )
}

export default FilterContent
