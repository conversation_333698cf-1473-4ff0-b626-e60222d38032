/* eslint-disable @typescript-eslint/no-extraneous-class */

import {
  EBuyingOfferFilterContentName,
  EBuyingOfferFilterTabName,
} from "types/buyingOffer.type"

const { Condition } = EBuyingOfferFilterContentName
const { SingleCondition } = EBuyingOfferFilterTabName

export class BuyingDashboardConstant {
  static readonly FILTER_FIELDS = {
    TAB: "tab",
    PAGE: "page",
    PAGE_SIZE: "pageSize",
    SEARCH: "search",
    SIZE_ID: "sizeId",
    CATEGORY_ID: "categoryId",
    BRAND_ID: "brandId",
    SORT_BY: "sortBy",
    SINGLE_CONDITION: "singlecondition",
    STATUS: "status",
    PURCHASE_AMOUNT_MIN: "purchaseamountMin",
    PURCHASE_AMOUNT_MAX: "purchaseamountMax",
    PURCHASE_DATE_START: "purchasedateStart",
    PURCHASE_DATE_END: "purchasedateEnd",
    CONDITION: "condition",
    SIZE: "size",
    PRICE_FROM: "offerpriceMin",
    PRICE_TO: "offerpriceMax",
    CREATED_DATE_START: "createddateStart",
    CREATED_DATE_END: "createddateEnd",
  }

  static readonly MAPPED_FILTER_TAB_CONTENT = {
    [SingleCondition]: Condition,
  }
}
