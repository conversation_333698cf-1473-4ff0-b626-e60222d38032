import { useSearchParams } from "next/navigation"

import { EBuyingDashboardTab } from "./buyingDashboard.type"
import BuyingDashboardOffer from "./BuyingDashboardOffer"
import { BuyingDashboardConstant } from "./buyingDashboard.contant"
import BuyingDashboardPending from "./BuyingDashboardPending"
import BuyingDashboardInProgress from "./BuyingDashboardInProgress"
import BuyingDashboardHistory from "./BuyingDashboardHistory"

const { Offers, BuyingPending, BuyingInProgress, BuyingHistory } =
  EBuyingDashboardTab

const { TAB } = BuyingDashboardConstant.FILTER_FIELDS

const BuyingDashboardTabContent = () => {
  const search = useSearchParams()
  const activeTab = (search?.get(TAB) as EBuyingDashboardTab) || Offers
  const renderTabContent = () => {
    switch (activeTab) {
      case Offers:
        return <BuyingDashboardOffer />
      case BuyingPending:
        return <BuyingDashboardPending />
      case BuyingInProgress:
        return <BuyingDashboardInProgress />
      case BuyingHistory:
        return <BuyingDashboardHistory />
    }
  }
  return renderTabContent()
}

export default BuyingDashboardTabContent
