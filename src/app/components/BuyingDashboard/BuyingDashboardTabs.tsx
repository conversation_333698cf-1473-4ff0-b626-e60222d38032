import { useRouter, useSearchParams } from "next/navigation"
import Tab from "@kickavenue/ui/components/Tab"
import { useBuyingOfferStore } from "stores/buyingOfferStore"

import { EBuyingDashboardTab } from "./buyingDashboard.type"
import { getBuyingDashboardTabs } from "./buyingDashboard.utils"

const { Offers } = EBuyingDashboardTab

const BuyingDashboardTabs = () => {
  const search = useSearchParams()
  const router = useRouter()
  const activeTab = (search?.get("tab") as EBuyingDashboardTab) || Offers
  const isDefaultTab = (key: string) => key === Offers
  const { setSelectedRowKeys } = useBuyingOfferStore()
  return (
    <Tab className="flex !gap-xxl !border-none">
      {getBuyingDashboardTabs().map((item) => (
        <button
          type="button"
          className="whitespace-nowrap text-gray-b-65"
          key={item.value}
          data-active={activeTab === item.value}
          onClick={() => {
            const url = isDefaultTab(item.value)
              ? "/profile/buying"
              : `?tab=${item.value}`
            router.push(url)
            if (item.value !== activeTab) {
              setSelectedRowKeys([])
            }
          }}
        >
          {item.label}
        </button>
      ))}
    </Tab>
  )
}

export default BuyingDashboardTabs
