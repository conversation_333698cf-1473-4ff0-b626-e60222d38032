import Text from "@kickavenue/ui/components/Text"
import { Badge } from "@kickavenue/ui/components/Badge"
import Divider from "@kickavenue/ui/components/Divider"
import { Button } from "@kickavenue/ui/components/Button"
import { useState } from "react"
import { useBuyingPendingStore } from "stores/buyingPendingStore"
import useFetchSaleDetail from "@hooks/useFetchSaleDetail"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { getTxDetailBuyerStatusMap } from "@utils/txDetail.utils"
import ModalUniqueID from "@components/shared/ModalParts/ModalUniqueID"
import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import useGetTransaction from "@app/hooks/useGetTransaction"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"

import BuyingDashboardDetailModalRecipient from "../../BuyingDashboardDetailModalRecipient"
import BuyingPaymentSummary from "@components/BuyingDashboard/components/BuyingPaymentSummary"

const BuyingDashboardPendingDetailModalContent = () => {
  const { selectedRowKeys } = useBuyingPendingStore()
  const txDetailId = selectedRowKeys?.[0]

  const [btnClicked, setBtnClicked] = useState(false)
  const { goToPage } = useChangePage()

  const { data: txDetail, isLoading } = useFetchSaleDetail({
    txDetailId,
  })

  const { data: transaction } = useGetTransaction({
    id: txDetail?.transactionId as number,
  })

  const handleCompletePayment = () => {
    setBtnClicked(true)
    goToPage(`${PageRouteConstant.PAYMENT_STATUS}/${transaction?.id}`)
  }

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  const txDetailStatusMap = getTxDetailBuyerStatusMap(txDetail?.status)

  return (
    <>
      <div className="flex max-h-[451px] flex-col gap-lg overflow-y-auto p-lg">
        <div className="flex justify-between">
          <Text size="base" type="bold" state="primary">
            Pending Status
          </Text>
          <Badge
            text={txDetailStatusMap?.text}
            type={txDetailStatusMap?.badgeType}
          />
        </div>
        <Divider orientation="horizontal" />
        <div className="flex flex-col gap-sm">
          <div className="">
            <ModalProduct listing={txDetail?.listingItem} />
          </div>
          <ModalUniqueID
            text="Invoice Number"
            value={txDetail?.invoiceNumber as string}
          />
          <BuyingPaymentSummary txDetail={txDetail} isLoading={isLoading} />
        </div>
        <Divider orientation="horizontal" />
        <BuyingDashboardDetailModalRecipient txDetail={txDetail} />
      </div>
      <ModalFooter>
        <div className="col-span-12">
          <Button
            size="lg"
            variant="primary"
            disabled={isLoading || btnClicked}
            onClick={handleCompletePayment}
            className="!w-full"
            type="submit"
          >
            Complete Payment
          </Button>
        </div>
      </ModalFooter>
    </>
  )
}

export default BuyingDashboardPendingDetailModalContent
