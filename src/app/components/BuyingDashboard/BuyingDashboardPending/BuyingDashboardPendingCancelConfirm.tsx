import { Button } from "@kickavenue/ui/components/Button"
import { useQueryClient } from "@tanstack/react-query"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import ModalConfirm from "@components/shared/ModalConfirm"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"
import { useBuyingPendingStore } from "stores/buyingPendingStore"

import useBuyingPendingCancelTx from "./hooks/useBuyingPendingCancelTx"

const { CANCEL_PAYMENT } = ModalConstant.MODAL_IDS

const BuyingDashboardPendingCancelConfirm = () => {
  const { open, modalId, setOpen } = useModalStore()
  const queryClient = useQueryClient()
  const { setShowToast } = useToast()
  const { selectedPendingBuying } = useBuyingPendingStore()

  const { mutate: cancelTx, isPending } = useBuyingPendingCancelTx({
    onSuccess: () => {
      setOpen(false, CANCEL_PAYMENT)
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_MY_PENDING_BUYING],
      })
      setShowToast(true, "Payment cancelled successfully", "success")
    },
    onError: (error) => {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })
  if (!open || modalId !== CANCEL_PAYMENT) return null

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, CANCEL_PAYMENT)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button
        size="md"
        variant="danger"
        disabled={isPending}
        onClick={() =>
          cancelTx({
            id: selectedPendingBuying?.transactionDetailId as number,
            orderId: selectedPendingBuying?.orderId as string,
          })
        }
      >
        Cancel Payment
      </Button>
    </>
  )

  return (
    <ModalConfirm
      open={open}
      onClose={() => setOpen(false, CANCEL_PAYMENT)}
      title="Are you sure you want to cancel the payment?"
      subtitle="This item may have limited availability and is not reserved until your transaction is complete. Canceling now may result in losing this great deal"
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default BuyingDashboardPendingCancelConfirm
