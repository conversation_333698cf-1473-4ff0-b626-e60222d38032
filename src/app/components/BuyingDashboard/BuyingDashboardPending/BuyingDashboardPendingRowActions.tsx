import { useCallback } from "react"
import { DropdownItemProps } from "@kickavenue/ui/components/Dropdown"
import {
  IconCloseOutline,
  IconKebabMenuVertical,
  IconTickOutline,
  IconViewDetailsOutline,
} from "@kickavenue/ui/components/icons"
import {
  TMyPendingBuying,
  TTransactionDetailStatus,
} from "types/transactionDetail.type"
import { useBuyingPendingStore } from "stores/buyingPendingStore"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"

import { EBuyingDashboardAction } from "../buyingDashboard.type"

const { CompletePayment, CancelPayment, ViewDetails } = EBuyingDashboardAction
const { BUYING_PENDING_DETAIL, CANCEL_PAYMENT } = ModalConstant.MODAL_IDS

const { WaitingForPayment } = TTransactionDetailStatus

const STATUS_ACTIONS_MAP: Partial<
  Record<TTransactionDetailStatus, EBuyingDashboardAction[]>
> = {
  [WaitingForPayment]: [CompletePayment, CancelPayment, ViewDetails],
}

const BuyingDashboardPendingRowActions = ({
  record,
}: {
  record: TMyPendingBuying
}) => {
  const { setSelectedRowKeys, setSelectedPendingBuying } =
    useBuyingPendingStore()
  const { setOpen } = useModalStore()
  const { goToPage } = useChangePage()

  const handleOnItemSelect = useCallback(
    (option: DropdownItemProps) => {
      switch (option.value) {
        case CompletePayment:
          goToPage(
            `${PageRouteConstant.PAYMENT_STATUS}/${record.transactionId}`,
          )
          break
        case CancelPayment:
          setSelectedRowKeys([record.transactionDetailId])
          setSelectedPendingBuying(record)
          setOpen(true, CANCEL_PAYMENT)
          break
        case ViewDetails:
          setSelectedRowKeys([record.transactionDetailId])
          setOpen(true, BUYING_PENDING_DETAIL)
          break
      }
    },
    [setSelectedRowKeys, setOpen, goToPage, setSelectedPendingBuying, record],
  )

  const isAllowedStatus = useCallback(
    (action: EBuyingDashboardAction) =>
      STATUS_ACTIONS_MAP[
        record.detailStatus as TTransactionDetailStatus
      ]?.includes(action),
    [record.detailStatus],
  )

  const options = [
    {
      text: "View Details",
      value: ViewDetails,
      iconLeading: <IconViewDetailsOutline width={16} height={16} />,
      show: () => isAllowedStatus(ViewDetails),
    },
    {
      text: "Complete Payment",
      value: CompletePayment,
      iconLeading: <IconTickOutline width={16} height={16} />,
      show: () => isAllowedStatus(CompletePayment),
    },
    {
      text: "Cancel Payment",
      value: CancelPayment,
      type: "danger",
      iconLeading: <IconCloseOutline width={16} height={16} color="#FF2323" />,
      show: () => isAllowedStatus(CancelPayment),
    },
  ].filter((item) => item.show()) as unknown as DropdownItemProps[]

  return (
    <div className="flex justify-center gap-xs">
      <DropdownDynamicChild
        options={options}
        placement="rightBottom"
        onItemSelect={handleOnItemSelect}
      >
        <IconKebabMenuVertical />
      </DropdownDynamicChild>
    </div>
  )
}

export default BuyingDashboardPendingRowActions
