import { useMutation } from "@tanstack/react-query"
import { TransactionDetailApiRepository } from "@infrastructure/repositories/transactionDetailApiRepository"
import { TOrderIDDetailParams } from "types/transactionDetail.type"

const useBuyingPendingCancelTx = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void
  onError?: (error: Error) => void
}) => {
  const submitCancelTx = async (payload: TOrderIDDetailParams) => {
    const r = new TransactionDetailApiRepository()
    const res = await r.cancelTransaction(payload.id)
    return res
  }

  return useMutation({
    mutationFn: submitCancelTx,
    onSuccess,
    onError,
  })
}

export default useBuyingPendingCancelTx
