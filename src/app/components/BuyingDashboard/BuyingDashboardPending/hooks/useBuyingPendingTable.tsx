import moment from "moment"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"
import { MiscConstant } from "@constants/misc"
import { formatCurrencyStripe, formatDate } from "@utils/misc"
import { getTxPaymentDeadline } from "@utils/transactions.utils"
import { TItemConditionEnum, TPackagingConditionEnum } from "types/listing.type"
import { TTableColumn } from "types/table.type"
import {
	TMyPendingBuying,
	TTransactionDetailStatus,
} from "types/transactionDetail.type"

import BuyingDashboardPendingRowActions from "../BuyingDashboardPendingRowActions"
import BuyingDashboardPendingStatus from "../BuyingDashboardPendingStatus"
import classes from '../BuyingDashboardPendingTable.module.scss'

const width = {
	orderId: 150,
	productDetails: 300,
	size: 64,
	conditions: 108,
	totalPayment: 148,
	paymentDeadline: 140,
	paymentMethod: 140,
	status: 128,
	action: 67,
}

const useBuyingPendingTable = () => {
	const columns = [
		{
			key: "invoiceNumber",
			title: "Invoice Number",
			headerClassName: classes['sticky-header-1'],
			contentClassName: classes['sticky-content-1'],
			width: width.orderId,
			render: (record: TMyPendingBuying) => <>{record.invoiceNumber || "-"}</>,
		},
		{
			key: "productDetails",
			title: "Product Details",
			headerClassName: classes['sticky-header-2'],
			contentClassName: classes['sticky-content-2'],
			width: width.productDetails,
			render: (record: TMyPendingBuying) => (
				<ProductDetailColumn listingItem={record.sellerListingDetails} />
			),
		},
		{
			key: "size",
			dataIndex: "size",
			title: "Size",
			width: width.size,
			render: (record: TMyPendingBuying) => (
				<TitleSubTitleColumn
					title="US"
					subtitle={record?.sellerListingDetails?.size?.us || "-"}
				/>
			),
		},
		{
			key: "itemCondition",
			title: "Conditions",
			width: width.conditions,
			render: (record: TMyPendingBuying) => (
				<ConditionColumn
					itemCondition={
						record?.sellerListingDetails?.itemCondition as TItemConditionEnum
					}
					packagingCondition={
						record?.sellerListingDetails
							?.packagingCondition as TPackagingConditionEnum
					}
				/>
			),
		},
		{
			key: "totalPayment",
			title: "Total Payment",
			width: width.totalPayment,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (record: TMyPendingBuying) => {
				const totalAmount = record?.totalAmount
				if (!totalAmount || !totalAmount?.minUnitVal) return "-"

				return formatCurrencyStripe({ price: totalAmount })
			},
		},
		{
			key: "paymentDeadline",
			title: "Payment Deadline",
			width: width.paymentDeadline,
			render: (record: TMyPendingBuying) => (
				<TitleSubTitleColumn
					title={`${moment(getTxPaymentDeadline(record.transactionDate)).format(
						"HH:mm",
					)} WIB`}
					subtitle={formatDate(
						getTxPaymentDeadline(record.transactionDate),
						MiscConstant.DATE_FORMAT,
					)}
				/>
			),
		},
		{
			key: "paymentMethod",
			title: "Payment Method",
			width: width.paymentMethod,
			render: (record: TMyPendingBuying) => record.paymentMethodName || "-",
		},
		{
			key: "status",
			title: "Status",
			width: width.status,
			render: (record: TMyPendingBuying) => (
				<BuyingDashboardPendingStatus
					status={record.detailStatus as TTransactionDetailStatus}
				/>
			),
		},
		{
			key: "action",
			title: "Action",
			headerClassName: `${classes['sticky-right-header-1']} [&>div]:justify-center`,
			contentClassName: `${classes['sticky-right-content-1']} [&>div]:justify-center dropdown-options`,
			width: width.action,
			render: (record: TMyPendingBuying) => (
				<BuyingDashboardPendingRowActions record={record} />
			),
		},
	] as TTableColumn[]
	return { columns }
}

export default useBuyingPendingTable
