import { useSearchParams } from "next/navigation"
import { useCallback, useEffect } from "react"
import { useBuyingPendingStore } from "@stores/buyingPendingStore"
import {
  TTransactionDetailFilter,
  TTransactionDetailStatus,
} from "types/transactionDetail.type"
import { MiscConstant } from "@constants/misc"
import { enumerizeTxDetailStatus } from "@utils/txDetail.utils"

import { EBuyingDashboardTab } from "../../buyingDashboard.type"
import { BuyingDashboardConstant } from "../../buyingDashboard.contant"
import {
  getBuyingPendingDefaultStatus,
  stripInvalidStatus,
} from "../buyingDashboardPending.utils"

const { Offers, BuyingPending } = EBuyingDashboardTab
const { TAB, PAGE, PAGE_SIZE, STATUS, SEARCH } =
  BuyingDashboardConstant.FILTER_FIELDS

const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const useBuyingPendingSearchParams = () => {
  const searchParams = useSearchParams()
  const { setFilter } = useBuyingPendingStore()

  const mapStatusQuery = useCallback((status?: string | null) => {
    if (!status) return undefined
    if (status.includes(",")) {
      const qStatus = status.split(",")
      const statusEnum = qStatus.map((s) => enumerizeTxDetailStatus(s))
      return statusEnum
    }
    return [enumerizeTxDetailStatus(status)] as string[]
  }, [])

  useEffect(() => {
    const tab = searchParams?.get(TAB) || Offers
    if (tab !== BuyingPending || !searchParams?.toString()) {
      return
    }

    const page = searchParams?.get(PAGE)
    const pageSize = searchParams?.get(PAGE_SIZE)
    const search = searchParams?.get(SEARCH)
    const status = stripInvalidStatus(
      (mapStatusQuery(searchParams?.get(STATUS)) ||
        getBuyingPendingDefaultStatus()) as TTransactionDetailStatus[],
    )

    const filterPages = {
      page: page ? Number(page) : PAGE_DEFAULT,
      pageSize: pageSize ? Number(pageSize) : PAGE_SIZE_DEFAULT,
      status,
      totalPages: 0,
      search,
    } as TTransactionDetailFilter

    setFilter({ ...filterPages })
  }, [mapStatusQuery, searchParams, setFilter])

  useEffect(() => {
    return () => {
      setFilter(null)
    }
  }, [setFilter])
}

export default useBuyingPendingSearchParams
