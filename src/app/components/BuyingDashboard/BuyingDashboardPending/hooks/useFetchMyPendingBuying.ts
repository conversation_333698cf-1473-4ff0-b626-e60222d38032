import { useQuery } from "@tanstack/react-query"
import { TPaginatedData } from "@domain/interfaces/common/response"
import { TransactionDetailApiRepository } from "@infrastructure/repositories/transactionDetailApiRepository"
import {
  TMyPendingBuying,
  TTransactionDetailFilter,
} from "types/transactionDetail.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { isQueryLoading } from "@utils/network"

const useFetchMyPendingBuying = ({
  onSuccess,
  enabled = true,
  filter,
}: {
  onSuccess?: (data: TPaginatedData<TMyPendingBuying>) => void
  enabled?: boolean
  filter?: TTransactionDetailFilter
}) => {
  const getMyPendingBuying = async () => {
    const r = new TransactionDetailApiRepository()
    const res = await r.getMyPendingBuying(filter)
    onSuccess?.(res)
    return res
  }

  const query = useQuery({
    queryKey: [
      QueryKeysConstant.GET_MY_PENDING_BUYING,
      ...Object.entries(filter || {}).map(([key, value]) => `${key}-${value}`),
    ],
    queryFn: getMyPendingBuying,
    enabled,
    staleTime: 1,
    retry: false,
  })

  return {
    ...query,
    isLoading: isQueryLoading(query),
  }
}

export default useFetchMyPendingBuying
