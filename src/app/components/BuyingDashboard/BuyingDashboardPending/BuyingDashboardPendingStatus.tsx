import Text from "@kickavenue/ui/components/Text"
import { formatStringWithSpaces } from "@utils/misc"
import { getTxDetailBuyerStatusMap } from "@utils/txDetail.utils"
import { TTransactionDetailStatus } from "types/transactionDetail.type"

const BuyingDashboardPendingStatus = ({
  status,
}: {
  status: TTransactionDetailStatus
}) => {
  const map = getTxDetailBuyerStatusMap(status)

  if (!map) return formatStringWithSpaces(status)

  return (
    <Text size="sm" state={map?.textState} type="regular">
      {map?.text}
    </Text>
  )
}

export default BuyingDashboardPendingStatus
