import { useSearchParams } from "next/navigation"
import { useEffect, useMemo, useState } from "react"
import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import FilterDashboard from "@components/FilterDashboard"
import { formatTxDetailStatus } from "@utils/txDetail.utils"

import { EBuyingDashboardTab } from "../buyingDashboard.type"
import { BuyingDashboardConstant } from "../buyingDashboard.contant"

import useBuyingPendingSearchParams from "./hooks/useBuyingPendingSearchParams"
import BuyingDashboardPendingTable from "./BuyingDashboardPendingTable"
import BuyingDashboardPendingDetailModal from "./BuyingDashboardPendingDetailModal"
import BuyingDashboardPendingCancelConfirm from "./BuyingDashboardPendingCancelConfirm"
import { getBuyingPendingDefaultStatus } from "./buyingDashboardPending.utils"

const { Offers, BuyingPending } = EBuyingDashboardTab
const { TAB, PAGE, PAGE_SIZE, STATUS } = BuyingDashboardConstant.FILTER_FIELDS

const BuyingDashboardPending = () => {
  const searchParams = useSearchParams()
  const { handleBulkChangeQuery } = useURLQuery()
  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams?.get(TAB) || Offers

  const status = useMemo(
    () => formatTxDetailStatus(getBuyingPendingDefaultStatus()),
    [],
  )

  const renderFilterDashboard =
    openFilter && selectedTab === BuyingPending ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  useBuyingPendingSearchParams()

  useEffect(() => {
    if (
      searchParams?.get(PAGE) &&
      searchParams?.get(PAGE_SIZE) &&
      searchParams?.get(STATUS)
    ) {
      return
    }
    handleBulkChangeQuery({
      [PAGE]: MiscConstant.PAGING_DEFAULT.PAGE,
      [PAGE_SIZE]: MiscConstant.PAGING_DEFAULT.PAGE_SIZE,
      [STATUS]: (status as string[])?.join(","),
    })
  }, [handleBulkChangeQuery, searchParams, status])

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <BuyingDashboardPendingTable />
      <BuyingDashboardPendingDetailModal />
      <BuyingDashboardPendingCancelConfirm />
      {renderFilterDashboard}
    </>
  )
}

export default BuyingDashboardPending
