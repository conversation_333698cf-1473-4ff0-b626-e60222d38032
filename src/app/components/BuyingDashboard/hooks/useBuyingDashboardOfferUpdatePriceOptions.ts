import { useCallback } from "react"
import { useBuyingOfferStore } from "stores/buyingOfferStore"

const useBuyingDashboardOfferUpdatePriceOptions = () => {
  const { setUpdatePrice, setUpdatePriceAction } = useBuyingOfferStore()
  const options = [
    { text: "Update Offer Price to", value: "update-to" },
    { text: "Update Offer Price by", value: "update-by" },
  ]
  const handleOptionSelect = useCallback(
    (val: string) => {
      setUpdatePrice(null)
      switch (val) {
        case "update-to":
          setUpdatePriceAction("update-to")
          break
        case "update-by":
          setUpdatePriceAction("update-by")
          break
        default:
          setUpdatePriceAction("")
          break
      }
    },
    [setUpdatePrice, setUpdatePriceAction],
  )
  return { options, handleOptionSelect }
}

export default useBuyingDashboardOfferUpdatePriceOptions
