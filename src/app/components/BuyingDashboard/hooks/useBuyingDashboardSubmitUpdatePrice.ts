import { useForm } from "react-hook-form"
import { useEffect, useMemo } from "react"
import { FormFieldConstant } from "@constants/formField"
import { TOffer } from "types/offer.type"
import { getGlobalPriceSummary } from "@utils/selling"
import { OfferConstant } from "@constants/offer.constant"
import { getStripAmount } from "@utils/misc"
import useBulkUpdateOfferPrice from "@app/hooks/useBulkUpdateOfferPrice"
import { useBuyingOfferStore } from "stores/buyingOfferStore"

const { OFFER_PRICE } = FormFieldConstant.CHECKOUT_PREVIEW_MAKE_OFFER
const { OFFER_PRICE_MULTIPLIER } = OfferConstant

const useBuyingDashboardSubmitUpdatePrice = (offer?: TOffer) => {
  const form = useForm()
  const { setSelectedRowKeys, setUpdatePrice, setUpdatePriceAction } =
    useBuyingOfferStore()

  const { lowestAsk } = getGlobalPriceSummary({
    summary: offer?.item?.itemSummary,
    sizeId: offer?.size?.id as number,
  })

  const offerPrice = form.watch(OFFER_PRICE.KEY)

  const { handleBulkUpdatePrice, isPending } = useBulkUpdateOfferPrice()

  const onFormValid = () => {
    handleBulkUpdatePrice()
  }

  useEffect(() => {
    if (!offer) return

    form.setValue(OFFER_PRICE.KEY, getStripAmount(offer?.amount))
    setUpdatePriceAction("update-to")
    setSelectedRowKeys([offer?.id as number])

    return () => {
      setSelectedRowKeys([])
      setUpdatePrice(null)
      setUpdatePriceAction("")
      form.reset()
    }
  }, [offer, form, setSelectedRowKeys, setUpdatePriceAction, setUpdatePrice])

  useEffect(() => {
    setUpdatePrice(Number(offerPrice))
  }, [offerPrice, setUpdatePrice])

  const disabled = useMemo(() => {
    if (isPending) return true
    if (!form.formState.isValid) return true
    if (lowestAsk && lowestAsk > 0 && offerPrice > lowestAsk) return true
    if (offerPrice % OFFER_PRICE_MULTIPLIER !== 0) return true
    return false
  }, [form.formState.isValid, lowestAsk, offerPrice, isPending])

  return { form, onFormValid, disabled }
}

export default useBuyingDashboardSubmitUpdatePrice
