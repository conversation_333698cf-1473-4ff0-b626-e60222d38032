import { getStripAmount } from "@utils/misc"
import { formatCurrency, formatNumberWithSeparator } from "@utils/separator"
import { TUpdatePriceAction } from "types/misc.type"
import { TOfferV2 } from "types/offer.type"

const useBuyingDashboardOfferUpdatePriceTable = (
  updatePrice: number | null,
  updatePriceAction: TUpdatePriceAction,
) => {
  const getNewOfferAmount = (record: TOfferV2) => {
    if (!record || !updatePrice) {
      return 0
    }
    if (updatePriceAction === "update-to") {
      return Number(updatePrice)
    }
    if (updatePriceAction === "update-by") {
      return Number(getStripAmount(record?.amount)) + updatePrice
    }

    return 0
  }
  const getNewOfferPriceColumn = (record: TOfferV2) => {
    if (!updatePrice) {
      return "-"
    }
    if (updatePriceAction === "update-to") {
      return formatCurrency(updatePrice, ",", "IDR")
    }
    if (updatePriceAction === "update-by") {
      return formatCurrency(
        Number(getStripAmount(record?.amount)) + updatePrice,
        ",",
        "IDR",
      )
    }
    return <>-</>
  }

  const getPriceChangeColumn = () => {
    if (updatePrice) {
      const leading = updatePrice > 0 ? "+" : ""
      return (
        <>
          IDR {leading}
          {formatNumberWithSeparator(updatePrice, ",")}
        </>
      )
    }
    return <>-</>
  }

  return { getNewOfferPriceColumn, getPriceChangeColumn, getNewOfferAmount }
}

export default useBuyingDashboardOfferUpdatePriceTable
