import { useSearchParams } from "next/navigation"
import { useEffect } from "react"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { MiscConstant } from "@constants/misc"
import { EStatusOffer, TOfferFilter } from "types/offer.type"
import { stringToNumberArray } from "@utils/string.utils"

import { EBuyingDashboardTab } from "../buyingDashboard.type"
import { BuyingDashboardConstant } from "../buyingDashboard.contant"

const { Offers } = EBuyingDashboardTab

const {
  TAB,
  PAGE,
  PAGE_SIZE,
  SEARCH,
  SIZE_ID,
  CATEGORY_ID,
  SINGLE_CONDITION,
  CREATED_DATE_END,
  CREATED_DATE_START,
  PRICE_FROM,
  PRICE_TO,
  BRAND_ID,
} = BuyingDashboardConstant.FILTER_FIELDS

const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const useBuyingDashboardOfferSearchParams = () => {
  const searchParams = useSearchParams()
  const { setBuyingOfferFilter } = useBuyingOfferStore()

  useEffect(() => {
    const tab = searchParams?.get(TAB) || Offers
    if (tab !== Offers || !searchParams?.toString()) {
      return
    }

    const page = searchParams?.get(PAGE)
    const pageSize = searchParams?.get(PAGE_SIZE)
    const search = searchParams?.get(SEARCH)
    const sizeId = searchParams?.get(SIZE_ID)
    const categoryId = searchParams?.get(CATEGORY_ID)
    const condition = searchParams?.get(SINGLE_CONDITION)
    const createdDateStart = searchParams?.get(CREATED_DATE_START)
    const createdDateEnd = searchParams?.get(CREATED_DATE_END)
    const priceFrom = searchParams?.get(PRICE_FROM)
    const priceTo = searchParams?.get(PRICE_TO)
    const brandId = searchParams?.get(BRAND_ID)

    const filter = {
      page: page ? Number(page) : PAGE_DEFAULT,
      pageSize: pageSize ? Number(pageSize) : PAGE_SIZE_DEFAULT,
      sortBy: [{ sortBy: "created_at", sortOrder: "desc" }],
      status: EStatusOffer.Pending,
      sizeId: stringToNumberArray(sizeId),
      categoryId: stringToNumberArray(categoryId),
      brandId: stringToNumberArray(brandId),
      createddateStart: createdDateStart,
      createddateEnd: createdDateEnd,
      totalPages: 0,
      search,
      condition,
      priceFrom,
      priceTo,
    } as TOfferFilter

    setBuyingOfferFilter(filter)
  }, [searchParams, setBuyingOfferFilter])

  useEffect(() => {
    return () => {
      setBuyingOfferFilter(null)
    }
  }, [setBuyingOfferFilter])
}

export default useBuyingDashboardOfferSearchParams
