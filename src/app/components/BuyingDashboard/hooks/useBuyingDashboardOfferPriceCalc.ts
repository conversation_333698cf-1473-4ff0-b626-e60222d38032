import { TOfferV2 } from "../../../../types/offer.type"
import { TUpdatePriceAction } from "../../../../types/misc.type"

interface ValidationResult {
  status: "valid" | "warning" | "error"
  message: string
}

export const useBuyingDashboardOfferPriceCalc = (
  updatePrice: number | null = null,
  updatePriceAction: TUpdatePriceAction = "update-to",
) => {
  const calculateOfferPrice = (previousPrice: number): number => {
    if (updatePrice === null) return previousPrice

    return updatePriceAction === "update-to"
      ? updatePrice
      : previousPrice + updatePrice
  }

  const getErrorMessage = (record: TOfferV2): ValidationResult => {
    const highestOfferPrice = Number(
      record.highestOffer.amount || record.highestOffer.minUnitVal || 0,
    )
    const lowestAskPrice = Number(
      record.lowestAsk?.amount || record.lowestAsk?.minUnitVal || 0,
    )
    const previousOfferPrice = Number(record.amount?.amount || 0)
    const offerPrice = calculateOfferPrice(previousOfferPrice)

    if (offerPrice === previousOfferPrice) {
      return { status: "valid", message: "" }
    }

    if (offerPrice % 10000 !== 0) {
      return {
        status: "error",
        message: "Offer price must be in increments of 10,000",
      }
    }

    if (!highestOfferPrice) {
      return { status: "valid", message: "You have the highest offer" }
    }

    if (!(lowestAskPrice || highestOfferPrice)) {
      return { status: "valid", message: "" }
    }

    if (highestOfferPrice > 0 && offerPrice && offerPrice < highestOfferPrice) {
      return {
        status: "warning",
        message: "Offer price is lower than the highest offer.",
      }
    }

    if (lowestAskPrice > 0 && offerPrice > lowestAskPrice) {
      return {
        status: "error",
        message: "Offer price is higher than the lowest ask",
      }
    }

    if (highestOfferPrice > 0 && offerPrice > highestOfferPrice) {
      return {
        status: "valid",
        message: "Offer price is highest offer",
      }
    }

    return { status: "valid", message: "" }
  }

  const getErrorLength = (records: TOfferV2[]): number =>
    records.filter((record) => getErrorMessage(record).status === "error")
      .length

  const getValidLength = (records: TOfferV2[]): number =>
    records.filter((record) => getErrorMessage(record).status !== "error")
      .length

  return { getErrorMessage, getErrorLength, getValidLength }
}

export default useBuyingDashboardOfferPriceCalc
