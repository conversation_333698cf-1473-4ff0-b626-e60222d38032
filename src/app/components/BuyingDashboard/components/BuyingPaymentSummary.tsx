import { useState } from "react"
import { cx } from "class-variance-authority"
import Text from "@kickavenue/ui/components/Text"
import { IconArrowDownOutline } from "@kickavenue/ui/components/icons"
import ModalRoundedContentItem from "@components/shared/ModalParts/ModalRoundedContentItem"
import SkeletonText from "@components/shared/Skeleton/SkeletonText"
import { formatDateWIB, formatPrice, getStripAmount } from "@utils/misc"
import { TTransactionDetail } from "types/transactionDetail.type"

interface BuyingPaymentSummaryProps {
  txDetail?: TTransactionDetail
  isLoading: boolean
}

const BuyingPaymentSummary = ({
  txDetail,
  isLoading,
}: BuyingPaymentSummaryProps) => {
  const [showAll, setShowAll] = useState(false)
  const [showCreditUsage, setShowCreditUsage] = useState(false)

  const productPrice = getStripAmount(txDetail?.price)
  const processingFee = getStripAmount(txDetail?.processingFee)
  const shippingFee = getStripAmount(txDetail?.shippingFee)
  const discountAmount = getStripAmount(txDetail?.discountAmount)
  const totalAmount = getStripAmount(txDetail?.totalAmount)

  const sellerCreditUsage = getStripAmount(
    txDetail?.sellerCreditUsage ??
      txDetail?.sellerListingData?.sellerCreditUsage,
  )
  const kickCreditUsage = getStripAmount(
    txDetail?.kickCreditUsage ?? txDetail?.sellerListingData?.kickCreditUsage,
  )
  const totalCreditUsage = sellerCreditUsage + kickCreditUsage

  const kickPointUsage = getStripAmount(
    txDetail?.kickPointUsage ?? txDetail?.sellerListingData?.kickPointUsage,
  )

  return (
    <div className="flex flex-col rounded-sm border border-solid border-gray-w-80 py-sm">
      <div className="flex flex-col gap-sm border-b border-solid border-gray-w-80 pb-sm">
        <ModalRoundedContentItem
          text="Checkout Date"
          value={formatDateWIB(txDetail?.createdAt)}
        />
        <ModalRoundedContentItem
          text="Payment Method"
          value={txDetail?.paymentMethodName as string}
        />
      </div>

      <div
        className={cx(
          "flex flex-col gap-sm overflow-hidden border-b border-solid border-gray-w-80",
          "transition-all duration-500 ease-out",
          showAll
            ? "max-h-[500px] translate-y-0 py-sm opacity-100"
            : "max-h-0 -translate-y-2 py-0 opacity-0",
        )}
      >
        <ModalRoundedContentItem
          isLoading={isLoading}
          text="Product Price"
          value={formatPrice(productPrice, null, "IDR")}
        />
        <ModalRoundedContentItem
          isLoading={isLoading}
          text="Processing Fee"
          value={formatPrice(processingFee, null, "IDR")}
        />
        <ModalRoundedContentItem
          isLoading={isLoading}
          text="Shipping Fee"
          value={formatPrice(shippingFee, null, "IDR")}
        />
        <ModalRoundedContentItem
          isLoading={isLoading}
          text="Voucher"
          value={
            discountAmount
              ? `-${formatPrice(discountAmount, null, "IDR")} (Voucher)`
              : "-"
          }
          valueProps={{
            state: "success",
          }}
        />

        <div className="flex w-full flex-col justify-between gap-xs">
          <div className="flex w-full justify-between px-sm">
            <div
              className="flex w-full cursor-pointer items-center gap-xs"
              onClick={() => setShowCreditUsage(!showCreditUsage)}
            >
              <Text size="sm" type="regular" state="primary">
                Credit Usage
              </Text>
              <IconArrowDownOutline
                className={cx(
                  "h-4 w-4 transition-transform duration-500 ease-out",
                  showCreditUsage && "rotate-180",
                )}
              />
            </div>
            {!isLoading && (
              <Text
                size="sm"
                type="regular"
                state="primary"
                className="!whitespace-nowrap !text-success"
              >
                {totalCreditUsage
                  ? `-${formatPrice(totalCreditUsage, null, "IDR")}`
                  : "-"}
              </Text>
            )}
            {isLoading && <SkeletonText className="w-1/4" />}
          </div>

          {showCreditUsage && (
            <div className="flex w-full flex-col gap-xs px-xs">
              <ModalRoundedContentItem
                isLoading={isLoading}
                text="Seller Credit Usage"
                labelProps={{
                  state: "secondary",
                }}
                value={
                  sellerCreditUsage
                    ? `-${formatPrice(sellerCreditUsage, null, "IDR")}`
                    : "-"
                }
                valueProps={{
                  state: "success",
                }}
              />

              <ModalRoundedContentItem
                isLoading={isLoading}
                text="Kick Credit Usage"
                labelProps={{
                  state: "secondary",
                }}
                value={
                  kickCreditUsage
                    ? `-${formatPrice(kickCreditUsage, null, "IDR")}`
                    : "-"
                }
                valueProps={{
                  state: "success",
                }}
              />
            </div>
          )}
        </div>

        <ModalRoundedContentItem
          isLoading={isLoading}
          text="Kick Point Usage"
          value={
            kickPointUsage
              ? `-${formatPrice(kickPointUsage, null, "IDR")}`
              : "-"
          }
          valueProps={{
            state: "success",
          }}
        />
      </div>

      {/* total payment */}
      <div className="flex w-full justify-between gap-xs px-sm pt-sm">
        <div className="flex w-full justify-end gap-1">
          <Text size="sm" type="regular" state="primary">
            Total Payment
          </Text>
          <Text size="sm" type="regular" state="primary">
            :
          </Text>
          {!isLoading && (
            <Text size="sm" type="bold" state="primary">
              {formatPrice(totalAmount, null, "IDR")}
            </Text>
          )}
          {isLoading && <SkeletonText className="w-1/4" />}
        </div>

        <button
          type="button"
          onClick={() => setShowAll(!showAll)}
          className="flex items-center justify-center"
        >
          <IconArrowDownOutline
            className={cx(
              "h-4 w-4 transition-transform duration-500 ease-out",
              showAll && "rotate-180",
            )}
          />
        </button>
      </div>
    </div>
  )
}

export default BuyingPaymentSummary
