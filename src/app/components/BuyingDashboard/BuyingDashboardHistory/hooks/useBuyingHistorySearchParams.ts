import { useSearchParams } from "next/navigation"
import { useCallback, useEffect } from "react"
import { useBuyingHistoryStore } from "@stores/buyingHistoryStore"
import {
  TTransactionDetailFilter,
  TTransactionDetailStatus,
} from "types/transactionDetail.type"
import { MiscConstant } from "@constants/misc"
import { enumerizeTxDetailStatus } from "@utils/txDetail.utils"
import { getFilterFromQuery } from "@utils/query.utils"
import { getDateFilter } from "@utils/selling"
import { getConditionFilter } from "@utils/listingItem"
import { stringToNumberArray } from "@utils/string.utils"

import { BuyingDashboardConstant } from "../../buyingDashboard.contant"
import { EBuyingDashboardTab } from "../../buyingDashboard.type"
import {
  getBuyingHistoryDefaultStatus,
  stripInvalidStatus,
} from "../buyingDashboardHistory.utils"

const { Offers, BuyingHistory } = EBuyingDashboardTab
const {
  TAB,
  PAGE,
  PAGE_SIZE,
  SEARCH,
  STATUS,
  PURCHASE_AMOUNT_MIN,
  PURCHASE_AMOUNT_MAX,
  PURCHASE_DATE_START,
  PURCHASE_DATE_END,
  CONDITION,
  SIZE_ID,
  CATEGORY_ID,
  BRAND_ID,
  SORT_BY,
} = BuyingDashboardConstant.FILTER_FIELDS

const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const useBuyingHistorySearchParams = () => {
  const searchParams = useSearchParams()
  const { setFilter } = useBuyingHistoryStore()

  const mapStatusQuery = useCallback((status?: string | null) => {
    if (!status) return undefined
    if (status.includes(",")) {
      const qStatus = status.split(",")
      const statusEnum = qStatus.map((s) => enumerizeTxDetailStatus(s))
      return statusEnum
    }
    return [enumerizeTxDetailStatus(status)] as string[]
  }, [])

  useEffect(() => {
    const tab = searchParams?.get(TAB) || Offers
    if (tab !== BuyingHistory || !searchParams?.toString()) {
      return
    }

    const page = searchParams?.get(PAGE)
    const pageSize = searchParams?.get(PAGE_SIZE)
    const search = searchParams?.get(SEARCH)
    const status = stripInvalidStatus(
      (mapStatusQuery(searchParams?.get(STATUS)) ||
        getBuyingHistoryDefaultStatus()) as TTransactionDetailStatus[],
    )
    const purchaseAmountMin = searchParams?.get(PURCHASE_AMOUNT_MIN)
    const purchaseAmountMax = searchParams?.get(PURCHASE_AMOUNT_MAX)
    const purchaseDateStart = searchParams?.get(PURCHASE_DATE_START)
    const purchaseDateEnd = searchParams?.get(PURCHASE_DATE_END)
    const condition = searchParams?.get(CONDITION)
    const sizeId = searchParams?.get(SIZE_ID)
    const categoryId = searchParams?.get(CATEGORY_ID)
    const brandId = searchParams?.get(BRAND_ID)
    const sortBy = searchParams?.get(SORT_BY)
    const filter = {
      page: getFilterFromQuery(page, PAGE_DEFAULT),
      pageSize: getFilterFromQuery(pageSize, PAGE_SIZE_DEFAULT),
      startPurchaseAmount: getFilterFromQuery(purchaseAmountMin),
      endPurchaseAmount: getFilterFromQuery(purchaseAmountMax),
      startDate: getDateFilter(purchaseDateStart),
      endDate: getDateFilter(purchaseDateEnd),
      size: stringToNumberArray(sizeId),
      categoryId: stringToNumberArray(categoryId),
      brandId: stringToNumberArray(brandId),
      status,
      search,
      totalPages: 0,
      sortBy,
      ...getConditionFilter(condition),
    } as TTransactionDetailFilter

    setFilter(filter)
  }, [searchParams, setFilter, mapStatusQuery])

  useEffect(() => {
    return () => {
      setFilter(null)
    }
  }, [setFilter])
}

export default useBuyingHistorySearchParams
