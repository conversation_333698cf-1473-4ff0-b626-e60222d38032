import {
  IconKebabMenuVertical,
  IconViewDetailsOutline,
} from "@kickavenue/ui/components/icons"
import { useCallback } from "react"
import { DropdownItemProps } from "@kickavenue/ui/components/Dropdown"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"
import {
  TTransactionDetail,
  TTransactionDetailStatus,
} from "types/transactionDetail.type"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { useBuyingHistoryStore } from "stores/buyingHistoryStore"

import { EBuyingDashboardAction } from "../buyingDashboard.type"

const { BUYING_HISTORY_DETAIL } = ModalConstant.MODAL_IDS

const { ViewDetails } = EBuyingDashboardAction

const { Completed, Failed, Refunded, Canceled } = TTransactionDetailStatus

const STATUS_ACTIONS_MAP: Partial<
  Record<TTransactionDetailStatus, EBuyingDashboardAction[]>
> = {
  [Completed]: [ViewDetails],
  [Failed]: [ViewDetails],
  [Refunded]: [ViewDetails],
  [Canceled]: [ViewDetails],
}

const BuyingDashboardHistoryRowActions = ({
  record,
}: {
  record: TTransactionDetail
}) => {
  const { setSelectedRowKeys } = useBuyingHistoryStore()
  const { setOpen } = useModalStore()

  const handleOnItemSelect = useCallback(
    (option: DropdownItemProps) => {
      switch (option.value) {
        case ViewDetails:
          setSelectedRowKeys([record.id])
          setOpen(true, BUYING_HISTORY_DETAIL)
          break
      }
    },
    [setSelectedRowKeys, setOpen, record],
  )

  const isAllowedStatus = useCallback(
    (action: EBuyingDashboardAction) =>
      STATUS_ACTIONS_MAP[record.status as TTransactionDetailStatus]?.includes(
        action,
      ),
    [record.status],
  )

  const actions = [
    {
      text: "View Details",
      value: ViewDetails,
      iconLeading: <IconViewDetailsOutline width={16} height={16} />,
      show: () => isAllowedStatus(ViewDetails),
    },
  ].filter((action) => action.show())

  return (
    <div className="flex justify-center gap-xs">
      <DropdownDynamicChild
        options={actions}
        placement="rightBottom"
        onItemSelect={handleOnItemSelect}
      >
        <IconKebabMenuVertical />
      </DropdownDynamicChild>
    </div>
  )
}

export default BuyingDashboardHistoryRowActions
