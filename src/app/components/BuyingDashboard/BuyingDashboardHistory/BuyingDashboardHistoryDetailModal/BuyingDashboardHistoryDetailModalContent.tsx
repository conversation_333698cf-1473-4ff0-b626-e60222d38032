import Text from "@kickavenue/ui/components/Text"
import { Badge } from "@kickavenue/ui/components/Badge"
import Divider from "@kickavenue/ui/components/Divider"
import { getTxDetailBuyerStatusMap } from "@utils/txDetail.utils"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import useFetchSaleDetail from "@app/hooks/useFetchSaleDetail"
import { useBuyingHistoryStore } from "stores/buyingHistoryStore"
import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import ModalUniqueID from "@components/shared/ModalParts/ModalUniqueID"
import BuyingDashboardDetailModalRecipient from "@components/BuyingDashboard/BuyingDashboardDetailModalRecipient"
import BuyingDashboardDetailModalShipment from "@components/BuyingDashboard/BuyingDashboardDetailModalShipment"
import BuyingPaymentSummary from "@components/BuyingDashboard/components/BuyingPaymentSummary"

const BuyingDashboardHistoryDetailModalContent = () => {
  const { selectedRowKeys } = useBuyingHistoryStore()
  const txDetailId = selectedRowKeys?.[0]

  const { data: txDetail, isLoading } = useFetchSaleDetail({
    txDetailId,
  })

  const txDetailStatusMap = getTxDetailBuyerStatusMap(txDetail?.status)

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  return (
    <div className="flex max-h-[551px] flex-col gap-lg overflow-y-auto p-lg">
      <div className="flex justify-between">
        <Text size="base" type="bold" state="primary">
          Order Status
        </Text>
        <Badge
          text={txDetailStatusMap?.text}
          type={txDetailStatusMap?.badgeType}
        />
      </div>
      <Divider orientation="horizontal" />
      <div className="flex flex-col gap-sm">
        <div className="">
          <ModalProduct listing={txDetail?.listingItem} />
        </div>
        <ModalUniqueID
          text="Invoice Number"
          value={txDetail?.invoiceNumber as string}
        />
        <BuyingPaymentSummary txDetail={txDetail} isLoading={isLoading} />
      </div>
      <Divider orientation="horizontal" />
      <BuyingDashboardDetailModalShipment txDetail={txDetail} />
      <Divider orientation="horizontal" />
      <BuyingDashboardDetailModalRecipient
        txDetail={txDetail}
        title="Delivery Address"
      />
    </div>
  )
}

export default BuyingDashboardHistoryDetailModalContent
