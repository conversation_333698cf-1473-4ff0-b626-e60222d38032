import { useSearchParams } from "next/navigation"
import { useEffect, useMemo, useState } from "react"
import useURLQuery from "@app/hooks/useUrlQuery"
import { formatTxDetailStatus } from "@utils/txDetail.utils"
import usePopulateFilterData from "@app/hooks/usePopulateFilterData"
import { MiscConstant } from "@constants/misc"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import FilterDashboard from "@components/FilterDashboard"

import { BuyingDashboardConstant } from "../buyingDashboard.contant"
import { EBuyingDashboardTab } from "../buyingDashboard.type"

import { getBuyingHistoryDefaultStatus } from "./buyingDashboardHistory.utils"
import useBuyingHistorySearchParams from "./hooks/useBuyingHistorySearchParams"
import BuyingDashboardHistoryTable from "./BuyingDashboardHistoryTable"
import BuyingDashboardHistoryDetailModal from "./BuyingDashboardHistoryDetailModal"

const { Offers, BuyingHistory } = EBuyingDashboardTab
const { TAB, PAGE, PAGE_SIZE, STATUS, SORT_BY } =
  BuyingDashboardConstant.FILTER_FIELDS

const BuyingDashboardHistory = () => {
  const searchParams = useSearchParams()
  const { handleBulkChangeQuery } = useURLQuery()
  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams?.get(TAB) || Offers

  const status = useMemo(
    () => formatTxDetailStatus(getBuyingHistoryDefaultStatus()),
    [],
  )

  useBuyingHistorySearchParams()
  usePopulateFilterData({ status: status as string[] })

  useEffect(() => {
    if (
      searchParams?.get(PAGE) &&
      searchParams?.get(PAGE_SIZE) &&
      searchParams?.get(STATUS) &&
      searchParams?.get(SORT_BY)
    ) {
      return
    }
    handleBulkChangeQuery({
      [PAGE]: MiscConstant.PAGING_DEFAULT.PAGE,
      [PAGE_SIZE]: MiscConstant.PAGING_DEFAULT.PAGE_SIZE,
      [STATUS]: (status as string[])?.join(","),
      [SORT_BY]: "created_at,DESC",
    })
  }, [handleBulkChangeQuery, searchParams, status])

  const renderFilterDashboard =
    openFilter && selectedTab === BuyingHistory ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <BuyingDashboardHistoryTable />
      <BuyingDashboardHistoryDetailModal />
      {renderFilterDashboard}
    </>
  )
}

export default BuyingDashboardHistory
