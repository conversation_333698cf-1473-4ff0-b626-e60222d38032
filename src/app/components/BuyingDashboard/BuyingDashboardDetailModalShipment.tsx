import Text from "@kickavenue/ui/components/Text"
import { TTransactionDetail } from "types/transactionDetail.type"

const BuyingDashboardDetailModalShipment = ({
  txDetail,
  title,
}: {
  txDetail?: TTransactionDetail
  title?: string
}) => {
  return (
    <div className="flex flex-col gap-sm">
      <Text size="base" type="bold" state="primary">
        {title || "Shipment Info"}
      </Text>
      <div className="flex">
        <Text
          size="sm"
          state="secondary"
          type="regular"
          className="flex-[0_0_128px]"
        >
          Shipping Courier
        </Text>
        <Text size="sm" state="primary" type="regular">
          <div className="flex gap-lg">
            <div>:</div>
            <div>{txDetail?.kickAvenueCourier || "-"}</div>
          </div>
        </Text>
      </div>
      <div className="flex">
        <Text
          size="sm"
          state="secondary"
          type="regular"
          className="flex-[0_0_128px]"
        >
          AWB Number
        </Text>
        <Text size="sm" state="primary" type="regular">
          <div className="flex gap-lg">
            <div>:</div>
            <div>{txDetail?.kickAvenueAwb || "-"}</div>
          </div>
        </Text>
      </div>
    </div>
  )
}

export default BuyingDashboardDetailModalShipment
