import TTextProps from "@kickavenue/ui/components/Text/Text.type"
import { TBadgeProps } from "@kickavenue/ui/components/Badge/Badge.type"
import { EStatusOffer } from "types/offer.type"
import { formatStringWithSpaces } from "@utils/misc"

import { EBuyingDashboardTab } from "./buyingDashboard.type"

export function getBuyingDashboardTabs() {
  const { Offers, BuyingPending, BuyingInProgress, BuyingHistory } =
    EBuyingDashboardTab
  return [
    { label: "Offers", value: Offers },
    { label: "Pending", value: BuyingPending },
    { label: "In Progress", value: BuyingInProgress },
    { label: "History", value: BuyingHistory },
  ] as { label: string; value: EBuyingDashboardTab }[]
}

export function getOfferStatusMap(status: EStatusOffer): {
  text: string
  textState: TTextProps["state"]
  badgeType: TBadgeProps["type"]
} {
  const { Pending, Active, UnKnown } = EStatusOffer

  const map = {
    [Pending]: {
      text: formatStringWithSpaces(Active),
      textState: "success" as const,
      badgeType: "positive" as const,
    },
  }

  const unknownMap = {
    text: formatStringWithSpaces(UnKnown),
    textState: "success" as const,
    badgeType: "positive" as const,
  }

  return map[status as keyof typeof map] || unknownMap
}
