import {
  IconAverageOrderValueBulk,
  IconSuccessTransactionBulk,
  IconTotalPurchaseBulk,
} from "@kickavenue/ui/components/icons"
import useFetchBuyingSummary from "@app/hooks/useFetchBuyingSummary"
import SummaryCard from "@components/shared/SummaryCard"
import { formatPrice } from "@utils/misc"
import { formatNumberWithSeparator } from "@utils/separator"

const BuyingDashboardSummaries = () => {
  const { data, isLoading } = useFetchBuyingSummary()

  return (
    <div className="grid grid-cols-4 gap-base md:grid-cols-12">
      <div className="col-span-4">
        <SummaryCard
          value={formatPrice(data?.totalPurchase ?? 0, null, "IDR")}
          isLoading={isLoading}
          Icon={
            <IconTotalPurchaseBulk width={24} height={24} color="#0b7a68" />
          }
          title="Total Purchase"
        />
      </div>
      <div className="col-span-4">
        <SummaryCard
          value={formatNumberWithSeparator(data?.completedPurchase ?? 0, ",")}
          isLoading={isLoading}
          Icon={
            <IconSuccessTransactionBulk
              width={24}
              height={24}
              color="#0b7a68"
            />
          }
          title="Completed Purchases"
        />
      </div>
      <div className="col-span-4">
        <SummaryCard
          value={formatPrice(data?.averagePurchaseValue ?? 0, null, "IDR")}
          isLoading={isLoading}
          Icon={
            <IconAverageOrderValueBulk width={24} height={24} color="#0b7a68" />
          }
          title="Average Purchase Value"
        />
      </div>
    </div>
  )
}

export default BuyingDashboardSummaries
