import Text from "@kickavenue/ui/components/Text"
import { getMemberFullName } from "@utils/member.utils"
import { TTransactionDetail } from "types/transactionDetail.type"

const BuyingDashboardDetailModalRecipient = ({
  txDetail,
  title,
}: {
  txDetail?: TTransactionDetail | null
  title?: string
}) => {
  return (
    <div className="flex flex-col gap-sm">
      <Text size="base" type="bold" state="primary">
        {title || "Delivery Detail"}
      </Text>
      <div className="flex">
        <Text
          size="sm"
          state="secondary"
          type="regular"
          className="flex-[0_0_128px]"
        >
          Recipient Name
        </Text>
        <Text size="sm" state="primary" type="regular">
          <div className="flex gap-lg">
            <div>:</div>
            <div>{getMemberFullName(txDetail?.buyer, "-")}</div>
          </div>
        </Text>
      </div>
      <div className="flex">
        <Text
          size="sm"
          state="secondary"
          type="regular"
          className="flex-[0_0_128px]"
        >
          Phone
        </Text>
        <Text size="sm" state="primary" type="regular">
          <div className="flex gap-lg">
            <div>:</div>
            <div>{txDetail?.buyer?.phoneNumber || "-"}</div>
          </div>
        </Text>
      </div>
      <div className="flex">
        <Text
          size="sm"
          state="secondary"
          type="regular"
          className="flex-[0_0_128px]"
        >
          Address
        </Text>
        <Text size="sm" state="primary" type="regular">
          <div className="flex gap-lg">
            <div>:</div>
            <div>{txDetail?.shippingAddress || "-"}</div>
          </div>
        </Text>
      </div>
    </div>
  )
}

export default BuyingDashboardDetailModalRecipient
