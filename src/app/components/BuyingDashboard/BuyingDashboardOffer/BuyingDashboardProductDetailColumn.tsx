import ProductImage from "@kickavenue/ui/components/ProductImage"
import {
  IconExpressBold,
  IconPreOrderBold,
  IconStandardBold,
  Space,
  Text,
} from "@kickavenue/ui/components"
import ListingBadge from "@components/shared/ListingBadge"
import {
  getBadgeTextByOfferItem,
  getBadgeTypeByOfferItem,
} from "@utils/listingItem"
import { TBadgeType } from "types/misc.type"
import { getProductImageUrl } from "@utils/misc"
import { TOfferV2 } from "types/offer.type"

export interface BuyingDashboardProductDetailColumnProps {
  offer?: TOfferV2 | null
}

const BuyingDashboardProductDetailColumn = ({
  offer,
}: BuyingDashboardProductDetailColumnProps) => {
  if (!offer) return "-"

  const getBadgeIconLeft = () => {
    if (offer?.isConsigment) {
      return IconExpressBold
    }
    if (offer?.isPreOrder) {
      return IconPreOrderBold
    }
    return IconStandardBold
  }

  const item = offer?.item

  return (
    <div className="flex items-center gap-sm">
      <ProductImage
        imageProps={{
          width: 40,
          height: 40,
          src: getProductImageUrl(item?.image),
          alt: item?.name || "",
        }}
        containerProps={{ className: "pb-xs shrink-0" }}
      />
      <div className="">
        <Text size="sm" state="primary" type="regular">
          {item?.name}
        </Text>
        <Space size="xs" type="margin" direction="y" />
        <div className="flex items-center gap-xs">
          <ListingBadge
            isUsed={false}
            is99Percents={false}
            badgeType={getBadgeTypeByOfferItem(offer) as TBadgeType}
            type={getBadgeTypeByOfferItem(offer)}
            iconLeft={getBadgeIconLeft()}
            text={getBadgeTextByOfferItem(offer)}
          />
          <Text
            size="sm"
            state="secondary"
            type="regular"
            className="whitespace-nowrap"
          >
            SKU: {item?.sku}
          </Text>
        </div>
      </div>
    </div>
  )
}

export default BuyingDashboardProductDetailColumn
