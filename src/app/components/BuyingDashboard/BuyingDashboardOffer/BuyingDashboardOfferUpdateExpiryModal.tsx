import { Space } from "@kickavenue/ui/components/index"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import UpdateExpiryDateInput from "@components/shared/UpdateExpiryDateInput"
import { ModalConstant } from "@constants/modal"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { useModalStore } from "stores/modalStore"
import UpdateExpiryDateAlert from "@components/shared/UpdateExpiryDateAlert"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import useBulkUpdateExpiryOffer from "@app/hooks/useBulkUpdateExpiryOffer"

import BuyingDashboardOfferUpdateExpiryTable from "./BuyingDashboardOfferUpdateExpiryTable"

const { UPDATE_OFFER_EXPIRY_DATE } = ModalConstant.MODAL_IDS

const BuyingDashboardOfferUpdateExpiryModal = () => {
  const { setOpen, open, modalId } = useModalStore()

  const { updateExpiryDate, selectedRowKeys, setUpdateExpiryDate } =
    useBuyingOfferStore()

  const { handleBulkUpdateExpiry, isPending } = useBulkUpdateExpiryOffer()

  const handleClose = () => {
    setOpen(false, UPDATE_OFFER_EXPIRY_DATE)
    setUpdateExpiryDate(undefined)
  }

  if (!open || modalId !== UPDATE_OFFER_EXPIRY_DATE) {
    return null
  }

  return (
    <Modal modalId={UPDATE_OFFER_EXPIRY_DATE} className="sm:!max-w-[1036px]">
      <HeaderModal onClose={handleClose} title="Update Expiry Date" />
      <div className="h-[667px] overflow-y-auto">
        <Space size="md" direction="y" type="margin" />
        <UpdateExpiryDateInput
          updateExpiryDate={updateExpiryDate}
          setUpdateExpiryDate={setUpdateExpiryDate}
        />
        <UpdateExpiryDateAlert
          updateExpiryDate={updateExpiryDate}
          selectedRowKeys={selectedRowKeys}
        />
        <div className="mb-md md:mb-lg" />
        <BuyingDashboardOfferUpdateExpiryTable />
      </div>
      <ModalCancelConfirm
        disableConfirm={
          !updateExpiryDate || isPending || selectedRowKeys.length === 0
        }
        onConfirm={handleBulkUpdateExpiry}
        onCancel={handleClose}
      />
    </Modal>
  )
}

export default BuyingDashboardOfferUpdateExpiryModal
