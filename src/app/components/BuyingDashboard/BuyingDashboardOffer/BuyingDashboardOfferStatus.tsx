import Text from "@kickavenue/ui/components/Text"
import { <PERSON><PERSON>tatusOffer, TOffer, TOfferV2 } from "types/offer.type"

import { getOfferStatusMap } from "../buyingDashboard.utils"

const BuyingDashboardOfferStatus = ({
  record,
}: {
  record: TOffer | TOfferV2
}) => {
  const map = getOfferStatusMap(record.status as EStatusOffer)
  return (
    <Text size="sm" state={map?.textState} type="regular">
      {map?.text}
    </Text>
  )
}

export default BuyingDashboardOfferStatus
