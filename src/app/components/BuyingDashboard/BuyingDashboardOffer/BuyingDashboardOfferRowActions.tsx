import {
  DropdownItemProps,
  IconKebabMenuVertical,
  IconViewDetailsOutline,
  IconTrashOutline,
  IconUpdatePriceOutline,
} from "@kickavenue/ui/dist/src/components"
import { useCallback } from "react"
import DropdownDynamicChild from "@shared/Form/DropdownDynamicChild"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { EStatusOffer, TOffer, TOfferV2 } from "types/offer.type"

import { EBuyingDashboardAction } from "../buyingDashboard.type"

export interface BuyingDashboardOfferRowActionsProps {
  record: TOffer | TOfferV2
}

const {
  UPDATE_OFFER_PRICE,
  UPDATE_OFFER_EXPIRY_DATE,
  DELETE_OFFER,
  OFFER_DETAIL,
} = ModalConstant.MODAL_IDS

const { ViewDetails, UpdatePrice, DeleteOffer, UpdateExpiry } =
  EBuyingDashboardAction

const { Pending, Expired } = EStatusOffer

const STATUS_ACTIONS_MAP: Partial<
  Record<EStatusOffer, EBuyingDashboardAction[]>
> = {
  [Pending]: [ViewDetails, UpdatePrice, DeleteOffer, UpdateExpiry],
  [Expired]: [ViewDetails, UpdatePrice, DeleteOffer, UpdateExpiry],
}

const BuyingDashboardOfferRowActions = ({
  record,
}: BuyingDashboardOfferRowActionsProps) => {
  const { setSelectedRowKeys } = useBuyingOfferStore()
  const { setOpen } = useModalStore()

  const handleOnItemSelect = useCallback(
    (option: DropdownItemProps) => {
      switch (option.value) {
        case UpdatePrice:
          setSelectedRowKeys([record.id as number])
          setOpen(true, UPDATE_OFFER_PRICE)
          break
        case DeleteOffer:
          setSelectedRowKeys([record.id as number])
          setOpen(true, DELETE_OFFER)
          break
        case ViewDetails:
          setSelectedRowKeys([record.id as number])
          setOpen(true, OFFER_DETAIL)
          break
        case UpdateExpiry:
          setSelectedRowKeys([record.id as number])
          setOpen(true, UPDATE_OFFER_EXPIRY_DATE)
          break
        default:
      }
    },
    [record.id, setSelectedRowKeys, setOpen],
  )

  const isAllowedStatus = useCallback(
    (action: EBuyingDashboardAction) =>
      STATUS_ACTIONS_MAP[record.status as EStatusOffer]?.includes(action),
    [record.status],
  )

  const options = [
    {
      text: "View Details",
      value: ViewDetails,
      iconLeading: <IconViewDetailsOutline width={16} height={16} />,
      show: () => isAllowedStatus(ViewDetails),
    },
    {
      text: "Update Offer Price",
      value: UpdatePrice,
      iconLeading: <IconUpdatePriceOutline width={16} height={16} />,
      show: () => isAllowedStatus(UpdatePrice),
    },
    {
      text: "Update Offer Expiry Date",
      value: UpdateExpiry,
      iconLeading: <IconUpdatePriceOutline width={16} height={16} />,
      show: () => isAllowedStatus(UpdateExpiry),
    },
    {
      text: "Delete Offer",
      value: DeleteOffer,
      type: "danger",
      iconLeading: <IconTrashOutline width={16} height={16} color="#FF2323" />,
      show: () => isAllowedStatus(DeleteOffer),
    },
  ].filter((item) => item.show()) as unknown as DropdownItemProps[]

  return (
    <div className="flex justify-center gap-xs">
      <DropdownDynamicChild
        options={options}
        placement="rightBottom"
        onItemSelect={handleOnItemSelect}
      >
        <IconKebabMenuVertical />
      </DropdownDynamicChild>
    </div>
  )
}

export default BuyingDashboardOfferRowActions
