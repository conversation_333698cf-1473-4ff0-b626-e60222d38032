import { But<PERSON> } from "@kickavenue/ui/components"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import ModalConfirm from "@components/shared/ModalConfirm"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { BuyingOfferApiRepository } from "@infrastructure/repositories/buyingOfferApiRepository"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"

const { DELETE_OFFER_CONFIRM } = ModalConstant.MODAL_IDS

const BuyingDashboardOfferDeleteConfirm = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { selectedRowKeys, setSelectedRowKeys } = useBuyingOfferStore()
  const queryClient = useQueryClient()

  const { setShowToast } = useToast()

  const { mutate: deleteOffers, isPending } = useMutation({
    mutationFn: async () => {
      const repository = new BuyingOfferApiRepository()
      const res = await repository.deleteOffers(selectedRowKeys)
      return res
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_MY_OFFERS],
      })
      setOpen(false, DELETE_OFFER_CONFIRM)
      setSelectedRowKeys([])
      setShowToast(true, `Deleted ${selectedRowKeys.length} offers.`, "success")
    },
    onError: (error) => {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })

  if (!open || modalId !== DELETE_OFFER_CONFIRM) return null

  return (
    <ModalConfirm
      open={open}
      onClose={() => setOpen(false, DELETE_OFFER_CONFIRM)}
      title="Are you sure you want to delete these offers?"
      subtitle="Deleting these offers will remove your chance to get these items at your set price. This action cannot be undone."
      slotAction={
        <>
          <Button
            onClick={() => setOpen(false, DELETE_OFFER_CONFIRM)}
            size="md"
            variant="secondary"
          >
            Keep Offers
          </Button>
          <Button
            size="md"
            variant="danger"
            disabled={isPending}
            onClick={() => deleteOffers()}
          >
            Delete Offers
          </Button>
        </>
      }
    />
  )
}

export default BuyingDashboardOfferDeleteConfirm
