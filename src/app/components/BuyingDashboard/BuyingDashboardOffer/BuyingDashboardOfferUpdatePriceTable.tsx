import { TTableColumn } from "types/table.type"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import BulkActionTable from "@shared/BulkActionTable"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { formatCurrencyStripe, getStripAmount } from "@utils/misc"
import { TOfferV2 } from "types/offer.type"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"

import useBuyingDashboardOfferUpdatePriceTable from "../hooks/useBuyingDashboardOfferUpdatePriceTable"
import { cx } from "class-variance-authority"
import useBuyingDashboardOfferPriceCalc from "../hooks/useBuyingDashboardOfferPriceCalc"
import Text from "@kickavenue/ui/components/Text"

const width = {
  productDetail: 274,
  SKU: 108,
  size: 64,
  offerPrice: 142,
  highestOffer: 142,
  lowestAsk: 142,
  newOffer: 142,
  currentExpiryDate: 154,
  newExpiryDate: 140,
  priceChange: 148,
}

const BuyingDashboardOfferUpdatePriceTable = () => {
  const {
    updatePrice,
    buyingOfferAllData,
    selectedRowKeys,
    updatePriceAction,
  } = useBuyingOfferStore()

  const { getErrorMessage } = useBuyingDashboardOfferPriceCalc(
    Number(updatePrice),
    updatePriceAction,
  )

  const { getNewOfferPriceColumn, getPriceChangeColumn, getNewOfferAmount } =
    useBuyingDashboardOfferUpdatePriceTable(
      Number(updatePrice),
      updatePriceAction,
    )

  const renderErrorMessage = (data: ReturnType<typeof getErrorMessage>) => {
    if (!data.message) return null
    return (
      <Text
        size="xs"
        style={{ marginTop: 4 }}
        state={
          data.status === "warning"
            ? "warning"
            : data.status === "error"
              ? "danger"
              : "success"
        }
        type="regular"
      >
        {data.message}
      </Text>
    )
  }

  let columns = [
    {
      key: "productDetails",
      width: width.productDetail,
      title: "Product Name",
      headerClassName: styles["sticky-header-1"],
      contentClassName: cx(styles["sticky-content-1"], "truncate"),
      defaultSortOrder: "ascend",
      sorter: (a: TOfferV2, b: TOfferV2) =>
        a?.itemName?.localeCompare(b?.itemName as string),
      render: (record: TOfferV2) => (
        <>
          {record?.itemName}
          {renderErrorMessage(getErrorMessage(record))}
        </>
      ),
    },
    {
      key: "SKU",
      width: width.SKU,
      title: "SKU",
      sorter: (a: TOfferV2, b: TOfferV2) =>
        a?.itemSku?.localeCompare(b?.itemSku as string),
      render: (record: TOfferV2) => <>{record?.itemSku}</>,
    },
    {
      key: "size",
      width: width.size,
      title: "Size",
      render: (record: TOfferV2) => (
        <TitleSubTitleColumn title="US" subtitle={record?.size?.us as string} />
      ),
      sorter: (a: TOfferV2, b: TOfferV2) =>
        getStripAmount(parseInt(a?.size?.us || "")) -
        getStripAmount(parseInt(b?.size?.us || "")),
    },
    {
      key: "highestOffer",
      width: width.highestOffer,
      title: "Highest Offer",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TOfferV2) =>
        formatCurrencyStripe({ price: record?.highestOffer }),
      sorter: (a: TOfferV2, b: TOfferV2) =>
        getStripAmount(a?.amount) - getStripAmount(b?.amount),
    },
    {
      key: "lowestAsk",
      width: width.lowestAsk,
      title: "Lowest Ask",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TOfferV2) =>
        formatCurrencyStripe({ price: record?.lowestAsk }),
      sorter: (a: TOfferV2, b: TOfferV2) =>
        getStripAmount(a?.amount) - getStripAmount(b?.amount),
    },
    {
      key: "offerPrice",
      width: width.offerPrice,
      title: "Old Offer",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TOfferV2) =>
        formatCurrencyStripe({ price: record?.amount }),
      sorter: (a: TOfferV2, b: TOfferV2) =>
        getStripAmount(a?.amount) - getStripAmount(b?.amount),
    },
    {
      key: "newOffer",
      width: width.newOffer,
      title: "New Offer",
      render: getNewOfferPriceColumn,
      sorter: (a: TOfferV2, b: TOfferV2) =>
        getNewOfferAmount(a) - getNewOfferAmount(b),
    },
  ] as TTableColumn[]

  if (updatePriceAction === "update-by") {
    const col: TTableColumn = {
      key: "priceChange",
      title: "Price Change",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: getPriceChangeColumn,
      width: width.priceChange,
    }
    const [columnsWithoutLastItem, columnsLastItem] = [
      columns.slice(0, -1),
      columns[columns.length - 1],
    ]
    columns = [...columnsWithoutLastItem, col, columnsLastItem]
  }

  return (
    <BulkActionTable
      columns={columns}
      data={buyingOfferAllData}
      selectedRowKeys={selectedRowKeys}
    />
  )
}

export default BuyingDashboardOfferUpdatePriceTable
