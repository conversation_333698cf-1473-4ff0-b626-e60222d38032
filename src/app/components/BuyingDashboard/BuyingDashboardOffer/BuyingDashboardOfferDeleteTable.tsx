import moment from "moment"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { TTableColumn } from "types/table.type"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { TOffer } from "types/offer.type"
import { getStripAmount, formatCurrencyStripe } from "@utils/misc"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"
import { MiscConstant } from "@constants/misc"
import BulkActionTable from "@components/shared/BulkActionTable"

const width = {
  productDetail: 374,
  SKU: 108,
  size: 64,
  offerPrice: 148,
  expiryDate: 154,
}

const BuyingDashboardOfferDeleteTable = () => {
  const { buyingOfferAllData, selectedRowKeys } = useBuyingOfferStore()

  const columns = [
    {
      key: "productDetails",
      width: width.productDetail,
      title: "Product Name",
      headerClassName: styles["sticky-header-1"],
      contentClassName: styles["sticky-content-1"],
      sorter: (a: TOffer, b: TOffer) =>
        a?.itemName?.localeCompare(b?.itemName || ""),
      defaultSortOrder: "ascend",
      render: (record: TOffer) => <>{record?.itemName}</>,
    },
    {
      key: "SKU",
      width: width.SKU,
      title: "SKU",
      sorter: (a: TOffer, b: TOffer) =>
        a?.itemSku?.localeCompare(b?.itemSku || ""),
      render: (record: TOffer) => <>{record?.itemSku}</>,
    },
    {
      key: "size",
      width: width.size,
      title: "Size",
      sorter: (a: TOffer, b: TOffer) =>
        a?.size?.us?.localeCompare(b?.size?.us || ""),
      render: (record: TOffer) => (
        <TitleSubTitleColumn title="US" subtitle={record?.size?.eu || ""} />
      ),
    },
    {
      key: "amount",
      width: width.offerPrice,
      title: "Offer Price",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TOffer) =>
        formatCurrencyStripe({ price: record?.amount }),
      sorter: (a: TOffer, b: TOffer) =>
        getStripAmount(a?.amount) - getStripAmount(b?.amount),
    },
    {
      key: "expiryDate",
      dataIndex: "expiryDate",
      width: width.expiryDate,
      title: "Expiry Date",
      render: (record: TOffer) =>
        moment(record.expiredAt).format(MiscConstant.DATE_FORMAT),
      sorter: (a: TOffer, b: TOffer) =>
        moment(a.expiredAt).diff(moment(b.expiredAt)),
    },
  ] as TTableColumn[]

  return (
    <BulkActionTable
      columns={columns}
      data={buyingOfferAllData}
      selectedRowKeys={selectedRowKeys}
    />
  )
}

export default BuyingDashboardOfferDeleteTable
