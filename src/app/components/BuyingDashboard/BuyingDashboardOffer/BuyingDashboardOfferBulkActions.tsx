import {
  Divider,
  DropdownItemProps,
  IconTagOutline,
  IconTrashOutline,
  IconUpdateExpiryDateOutline,
  Space,
  Text,
} from "@kickavenue/ui/components"
import Dropdown from "@shared/Form/Dropdown"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

import { EBuyingDashboardAction } from "../buyingDashboard.type"

const { MODAL_IDS } = ModalConstant

const { UpdatePrice, DeleteOffer, UpdateExpiry } = EBuyingDashboardAction

const BuyingDashboardOfferBulkActions = () => {
  const { setOpen } = useModalStore()
  const { selectedRowKeys } = useBuyingOfferStore()

  const handleActionSelected = (value: string) => {
    switch (value) {
      case UpdatePrice:
        setOpen(true, MODAL_IDS.UPDATE_OFFER_PRICE)
        break
      case DeleteOffer:
        setOpen(true, MODAL_IDS.DELETE_OFFER)
        break
      case UpdateExpiry:
        setOpen(true, MODAL_IDS.UPDATE_OFFER_EXPIRY_DATE)
        break
    }
  }

  const options = [
    {
      text: "Update Offer Price",
      value: UpdatePrice,
      iconLeading: <IconTagOutline />,
    },
    {
      text: "Update Offer Expiry Date",
      value: UpdateExpiry,
      iconLeading: <IconUpdateExpiryDateOutline />,
    },
    {
      text: "Delete Offer",
      value: DeleteOffer,
      type: "danger",
      iconLeading: <IconTrashOutline color="#FF2323" />,
    },
  ] as DropdownItemProps[]

  if (!selectedRowKeys.length) return null

  return (
    <>
      <Space size="lg" type="margin" direction="y" />
      <Divider orientation="horizontal" state="default" />
      <Space size="lg" type="margin" direction="y" />
      <div className="flex items-center justify-between">
        <Text size="base" type="bold" state="primary">
          {selectedRowKeys?.length} Items Selected
        </Text>
        <Dropdown
          options={options}
          type="button"
          placeholder="More Actions"
          className="!w-[200px]"
          placement="centerTop"
          onItemSelect={handleActionSelected}
        />
      </div>
    </>
  )
}

export default BuyingDashboardOfferBulkActions
