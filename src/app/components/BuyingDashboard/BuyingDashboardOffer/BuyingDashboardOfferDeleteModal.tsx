import Space from "@kickavenue/ui/components/Space"
import { Alert } from "@kickavenue/ui/components/index"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import Modal from "@components/shared/Modal"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import HeaderModal from "@components/shared/HeaderModal"

import BuyingDashboardOfferDeleteTable from "./BuyingDashboardOfferDeleteTable"

const { DELETE_OFFER, DELETE_OFFER_CONFIRM } = ModalConstant.MODAL_IDS

const BuyingDashboardOfferDeleteModal = () => {
  const { setOpen, open, modalId } = useModalStore()

  const { selectedRowKeys } = useBuyingOfferStore()

  const handleClose = () => {
    setOpen(false, DELETE_OFFER)
  }

  const handleDelete = () => {
    setOpen(false, DELETE_OFFER)
    setOpen(true, DELETE_OFFER_CONFIRM)
  }

  if (!open || modalId !== DELETE_OFFER) {
    return null
  }

  return (
    <Modal
      modalId={DELETE_OFFER}
      className="sm:!max-w-[1036px]"
      onClose={handleClose}
    >
      <HeaderModal onClose={handleClose} title="Delete Offer" />
      <Space size="md" direction="y" type="margin" />
      <div className="h-[667px] overflow-y-auto">
        <div className="mb-sm px-lg md:mb-lg">
          <Alert
            className="!w-full"
            variant="information"
            isIcon
            subTitle={`Please review the ${selectedRowKeys?.length || 0} offers listed below before confirming deletion.`}
          />
        </div>
        <BuyingDashboardOfferDeleteTable />
      </div>
      <ModalCancelConfirm
        onCancel={handleClose}
        onConfirm={handleDelete}
        disableConfirm={false}
      />
    </Modal>
  )
}

export default BuyingDashboardOfferDeleteModal
