import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { useBuyingOfferStore } from "stores/buyingOfferStore"

import BuyingDashboardOfferDetailModalContent from "./BuyingDashboardOfferDetailModalContent"

const { OFFER_DETAIL } = ModalConstant.MODAL_IDS

const BuyingDashboardOfferDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setSelectedRowKeys } = useBuyingOfferStore()

  const handleClose = () => {
    setOpen(false, OFFER_DETAIL)
    setSelectedRowKeys([])
  }

  if (!open || modalId !== OFFER_DETAIL) return null

  return (
    <Modal
      modalId={OFFER_DETAIL}
      className="!min-h-[612px]"
      onClose={handleClose}
    >
      <HeaderModal onClose={handleClose} title="Offer Detail" />
      <BuyingDashboardOfferDetailModalContent />
    </Modal>
  )
}

export default BuyingDashboardOfferDetailModal
