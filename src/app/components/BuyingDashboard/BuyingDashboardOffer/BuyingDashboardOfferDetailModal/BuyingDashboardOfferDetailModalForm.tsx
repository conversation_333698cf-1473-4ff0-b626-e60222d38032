import { Controller, useFormContext } from "react-hook-form"
import { FormFieldConstant } from "@constants/formField"
import Input from "@components/shared/Form/Input"
import useHandleOnNumberChange from "@app/hooks/useHandleOnNumberChange"
import { TItemSummary } from "types/itemSummary.type"
import { getGlobalPriceSummary } from "@utils/selling"
import useOfferPriceInputStatus from "@components/CheckoutPreview/hooks/useOfferPriceInputStatus"

const { OFFER_PRICE } = FormFieldConstant.CHECKOUT_PREVIEW_MAKE_OFFER

const BuyingDashboardOfferDetailModalForm = ({
  itemSummary,
  sizeId,
}: {
  itemSummary?: TItemSummary
  sizeId: number
}) => {
  const { control } = useFormContext()

  const { handleOnNumberChange, formatInputNumber } = useHandleOnNumberChange()

  const { lowestAsk, highestOffer } = getGlobalPriceSummary({
    summary: itemSummary,
    sizeId,
  })

  const { variant, rightIcon, helperText } = useOfferPriceInputStatus({
    highestOffer: highestOffer as number,
    lowestAsk: lowestAsk as number,
  })

  return (
    <Controller
      name={OFFER_PRICE.KEY}
      control={control}
      rules={{ required: true, validate: (value) => value > 0 }}
      render={({ field }) => (
        <Input
          size="sm"
          label={OFFER_PRICE.NAME}
          variant={variant}
          rightIcon={rightIcon}
          helperText={helperText}
          prefix="IDR"
          {...field}
          value={formatInputNumber(field.value)}
          onChange={(e) => handleOnNumberChange(e, field.onChange)}
        />
      )}
    />
  )
}

export default BuyingDashboardOfferDetailModalForm
