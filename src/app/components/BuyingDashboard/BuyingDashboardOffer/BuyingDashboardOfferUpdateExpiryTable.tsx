import moment from "moment"
import { TTableColumn } from "types/table.type"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { MiscConstant } from "@constants/misc"
import BulkActionTable from "@shared/BulkActionTable"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { formatCurrencyStripe, getStripAmount } from "@utils/misc"
import { TOffer } from "types/offer.type"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"

const width = {
  productDetail: 374,
  SKU: 108,
  size: 64,
  offerPrice: 148,
  currentExpiryDate: 154,
  newExpiryDate: 140,
}

const BuyingDashboardOfferUpdateExpiryTable = () => {
  const { updateExpiryDate, buyingOfferAllData, selectedRowKeys } =
    useBuyingOfferStore()

  const columns = [
    {
      key: "productDetails",
      width: width.productDetail,
      title: "Product Name",
      headerClassName: styles["sticky-header-1"],
      contentClassName: styles["sticky-content-1"],
      sorter: (a: TOffer, b: TOffer) =>
        a?.itemName?.localeCompare(b?.itemName || ""),
      defaultSortOrder: "ascend",
      render: (record: TOffer) => <>{record?.itemName}</>,
    },
    {
      key: "SKU",
      width: width.SKU,
      title: "SKU",
      sorter: (a: TOffer, b: TOffer) =>
        a?.itemSku?.localeCompare(b?.itemSku || ""),
      render: (record: TOffer) => <>{record?.itemSku}</>,
    },
    {
      key: "size",
      width: width.size,
      title: "Size",
      sorter: (a: TOffer, b: TOffer) =>
        a?.size?.us?.localeCompare(b?.size?.us || ""),
      render: (record: TOffer) => (
        <TitleSubTitleColumn title="US" subtitle={record?.size?.eu || ""} />
      ),
    },
    {
      key: "amount",
      width: width.offerPrice,
      title: "Offer Price",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      render: (record: TOffer) =>
        formatCurrencyStripe({ price: record?.amount }),
      sorter: (a: TOffer, b: TOffer) =>
        getStripAmount(a?.amount) - getStripAmount(b?.amount),
    },
    {
      key: "expiryDate",
      dataIndex: "expiryDate",
      width: width.currentExpiryDate,
      title: "Current Expiry Date",
      render: (record: TOffer) =>
        moment(record.expiredAt).format(MiscConstant.DATE_FORMAT),
    },
    {
      key: "newExpiryDate",
      width: width.newExpiryDate,
      title: "New Expiry Date",
      render: () => {
        if (!updateExpiryDate) {
          return "-"
        }
        return moment(updateExpiryDate).format(MiscConstant.DATE_FORMAT)
      },
    },
  ] as TTableColumn[]

  return (
    <BulkActionTable
      columns={columns}
      data={buyingOfferAllData}
      selectedRowKeys={selectedRowKeys}
    />
  )
}

export default BuyingDashboardOfferUpdateExpiryTable
