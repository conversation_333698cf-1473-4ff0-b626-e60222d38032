import {
  IconKebabMenuVertical,
  IconReceiveItemOutline,
  IconViewDetailsOutline,
} from "@kickavenue/ui/components/icons"
import { useCallback } from "react"
import { DropdownItemProps } from "@kickavenue/ui/components/Dropdown"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"
import {
  TTransactionDetail,
  TTransactionDetailStatus,
} from "types/transactionDetail.type"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

import { EBuyingDashboardAction } from "../buyingDashboard.type"

const { BUYING_IN_PROGRESS_DETAIL, BUYING_IN_PROGRESS_RECEIVE_CONFIRM } =
  ModalConstant.MODAL_IDS

const { ReceiveItem, ViewDetails } = EBuyingDashboardAction

const {
  WaitingSellerConfirmation,
  WaitingSellerDelivering,
  EnRouteToKickAvenue,
  AuthenticationProcess,
  VerificationFailed,
  EnRouteToBuyer,
} = TTransactionDetailStatus

const STATUS_ACTIONS_MAP: Partial<
  Record<TTransactionDetailStatus, EBuyingDashboardAction[]>
> = {
  [WaitingSellerConfirmation]: [ViewDetails],
  [WaitingSellerDelivering]: [ViewDetails],
  [EnRouteToKickAvenue]: [ViewDetails],
  [AuthenticationProcess]: [ViewDetails],
  [VerificationFailed]: [ViewDetails],
  [EnRouteToBuyer]: [ViewDetails, ReceiveItem],
}

const BuyingDashboardInProgressRowActions = ({
  record,
}: {
  record: TTransactionDetail
}) => {
  const { setSelectedRowKeys, setSelectedTxDetail } = useBuyingInProgressStore()
  const { setOpen } = useModalStore()

  const handleOnItemSelect = useCallback(
    (option: DropdownItemProps) => {
      switch (option.value) {
        case ReceiveItem:
          setSelectedRowKeys([record.id])
          setSelectedTxDetail(record)
          setOpen(true, BUYING_IN_PROGRESS_RECEIVE_CONFIRM)
          break
        case ViewDetails:
          setSelectedRowKeys([record.id])
          setOpen(true, BUYING_IN_PROGRESS_DETAIL)
          break
      }
    },
    [setSelectedRowKeys, setOpen, setSelectedTxDetail, record],
  )

  const isAllowedStatus = useCallback(
    (action: EBuyingDashboardAction) =>
      STATUS_ACTIONS_MAP[record.status as TTransactionDetailStatus]?.includes(
        action,
      ),
    [record.status],
  )

  const actions = [
    {
      text: "View Details",
      value: ViewDetails,
      iconLeading: <IconViewDetailsOutline width={16} height={16} />,
      show: () => isAllowedStatus(ViewDetails),
    },
    {
      text: "Receive Item",
      value: ReceiveItem,
      iconLeading: <IconReceiveItemOutline width={16} height={16} />,
      show: () => isAllowedStatus(ReceiveItem),
    },
  ].filter((action) => action.show())

  return (
    <div className="flex justify-center gap-xs">
      <DropdownDynamicChild
        options={actions}
        placement="rightBottom"
        onItemSelect={handleOnItemSelect}
      >
        <IconKebabMenuVertical />
      </DropdownDynamicChild>
    </div>
  )
}

export default BuyingDashboardInProgressRowActions
