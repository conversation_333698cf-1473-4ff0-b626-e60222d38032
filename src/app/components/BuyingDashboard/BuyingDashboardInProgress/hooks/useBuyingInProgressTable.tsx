import moment from "moment"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"
import TxDetailStatus from "@components/shared/TxDetailStatus"
import { MiscConstant } from "@constants/misc"
import { formatPrice } from "@utils/misc"
import { getTxDetailTotalPayment } from "@utils/txDetail.utils"
import { TItemConditionEnum, TPackagingConditionEnum } from "types/listing.type"
import { TTableColumn } from "types/table.type"
import { TTransactionDetail } from "types/transactionDetail.type"

import BuyingDashboardInProgressRowActions from "../BuyingDashboardInProgressRowActions"
import classes from '../BuyingDashboardInProgressTable.module.scss'

const width = {
	orderId: 150,
	productDetails: 300,
	size: 64,
	conditions: 108,
	totalPayment: 148,
	purchaseDate: 140,
	paymentMethod: 140,
	status: 155,
	action: 67,
}

const useBuyingInProgressTable = () => {
	const columns = [
		{
			key: "invoiceNumber",
			title: "Invoice Number",
			width: width.orderId,
			headerClassName: classes['sticky-header-1'],
			contentClassName: classes['sticky-content-1'],
			render: (record: TTransactionDetail) => (
				<>{record.invoiceNumber || "-"}</>
			),
			sorter: () => { },
		},
		{
			key: "productDetails",
			title: "Product Details",
			headerClassName: classes['sticky-header-2'],
			contentClassName: classes['sticky-content-2'],
			width: width.productDetails,
			render: (record: TTransactionDetail) => (
				<ProductDetailColumn listingItem={record.sellerListingDetails} />
			),
		},
		{
			key: "size",
			title: "Size",
			width: width.size,
			render: (record: TTransactionDetail) => (
				<TitleSubTitleColumn
					title="US"
					subtitle={record?.sellerListingDetails?.size?.us || "-"}
				/>
			),
		},
		{
			key: "itemCondition",
			title: "Conditions",
			width: width.conditions,
			render: (record: TTransactionDetail) => (
				<ConditionColumn
					itemCondition={
						record?.sellerListingDetails?.itemCondition as TItemConditionEnum
					}
					packagingCondition={
						record?.sellerListingDetails
							?.packagingCondition as TPackagingConditionEnum
					}
				/>
			),
		},
		{
			key: "totalPayment",
			title: "Total Payment",
			width: width.totalPayment,
			headerClassName: "text-right [&>div]:!justify-end",
			contentClassName: "text-right",
			render: (record: TTransactionDetail) => {
				const totalPayment = getTxDetailTotalPayment(record)
				if (!totalPayment) return "-"

				return formatPrice(totalPayment, null, "IDR")
			},
		},
		{
			key: "createdAt",
			title: "Purchase Date",
			width: width.purchaseDate,
			render: (record: TTransactionDetail) => (
				<>{moment(record.createdAt).format(MiscConstant.DATE_FORMAT)}</>
			),
			sorter: () => { },
		},
		{
			key: "paymentMethod",
			title: "Payment Method",
			width: width.paymentMethod,
			render: (record: TTransactionDetail) => <>{record.paymentMethodName}</>,
		},
		{
			key: "status",
			title: "Status",
			width: width.status,
			render: (record: TTransactionDetail) => (
				<TxDetailStatus status={record.status} />
			),
			sorter: () => { },
		},
		{
			key: "action",
			title: "Action",
			width: width.action,
			headerClassName: `${classes['sticky-right-header-1']} [&>div]:justify-center`,
			contentClassName: `${classes['sticky-right-content-1']} [&>div]:justify-center dropdown-options`,
			render: (record: TTransactionDetail) => (
				<BuyingDashboardInProgressRowActions record={record} />
			),
		},
	] as TTableColumn[]
	return { columns }
}

export default useBuyingInProgressTable
