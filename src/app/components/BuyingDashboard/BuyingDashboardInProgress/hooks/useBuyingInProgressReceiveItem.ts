import { useMutation } from "@tanstack/react-query"
import { TransactionDetailApiRepository } from "@infrastructure/repositories/transactionDetailApiRepository"
import { TOrderIDDetailParams } from "types/transactionDetail.type"

const useBuyingInProgressReceiveItem = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void
  onError?: (error: Error) => void
}) => {
  const submitReceiveItem = async (payload: TOrderIDDetailParams) => {
    const r = new TransactionDetailApiRepository()
    const res = await r.receiveItem(payload.id, payload.orderId)
    return res
  }

  return useMutation({
    mutationFn: submitReceiveItem,
    onSuccess,
    onError,
  })
}

export default useBuyingInProgressReceiveItem
