import { Button } from "@kickavenue/ui/components/Button"
import { useQueryClient } from "@tanstack/react-query"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import ModalConfirm from "@components/shared/ModalConfirm"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"

import useBuyingInProgressReceiveItem from "./hooks/useBuyingInProgressReceiveItem"

const { BUYING_IN_PROGRESS_RECEIVE_CONFIRM } = ModalConstant.MODAL_IDS

const BuyingDashboardInProgressReceiveConfirm = () => {
  const { open, modalId, setOpen } = useModalStore()
  const { selectedTxDetail } = useBuyingInProgressStore()
  const queryClient = useQueryClient()

  const { setShowToast } = useToast()

  const { mutate: receiveItem, isPending } = useBuyingInProgressReceiveItem({
    onSuccess: () => {
      setOpen(false, BUYING_IN_PROGRESS_RECEIVE_CONFIRM)
      queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_MY_TX_DETAILS],
      })
      setShowToast(true, "Item received successfully", "success")
    },
    onError: (error) => {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })

  if (!open || modalId !== BUYING_IN_PROGRESS_RECEIVE_CONFIRM) return null

  const renderSlotAction = () => (
    <>
      <Button
        onClick={() => setOpen(false, BUYING_IN_PROGRESS_RECEIVE_CONFIRM)}
        size="md"
        variant="secondary"
      >
        Back to Dashboard
      </Button>
      <Button
        size="md"
        variant="primary"
        onClick={() =>
          receiveItem({
            id: selectedTxDetail?.id as number,
            orderId: selectedTxDetail?.orderId as string,
          })
        }
        disabled={isPending}
      >
        Confirm Delivery
      </Button>
    </>
  )

  return (
    <ModalConfirm
      open={open}
      onClose={() => setOpen(false, BUYING_IN_PROGRESS_RECEIVE_CONFIRM)}
      title="Have You Received Your  Item?"
      subtitle="By confirming you have received the item, this will finalize your purchase and close the order."
      className="[&>div>div>div]:!w-[358px]"
      slotAction={renderSlotAction()}
    />
  )
}

export default BuyingDashboardInProgressReceiveConfirm
