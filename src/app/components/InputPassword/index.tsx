import React from "react"
import { ExtendedPasswordInputProps } from "@domain/entities/Input"

import { PasswordField, RequirementsDisplay } from "./components/PasswordField"
import { usePasswordInput } from "./hooks/usePasswordInput"

const PasswordInput: React.FC<ExtendedPasswordInputProps> = ({
  value,
  onChange,
  confirmationValue,
  onConfirmationChange,
  showConfirmation = false,
  showRequirements = false,
  className = "",
  label = "Password",
  name,
  confirmationName,
  autoComplete,
  confirmationAutoComplete,
}) => {
  const {
    isPasswordVisible,
    isConfirmationVisible,
    validationResult,
    handleChange,
    handleConfirmationChange,
    togglePasswordVisibility,
    toggleConfirmationVisibility,
    passwordVariant,
    confirmationVariant,
    passwordHelperText,
    confirmationHelperText,
    setFocusedInput,
  } = usePasswordInput({
    value,
    onChange,
    confirmationValue,
    onConfirmationChange,
    showRequirements,
  })

  return (
    <div className={className}>
      <PasswordField
        label={label}
        name={name}
        value={value}
        isVisible={isPasswordVisible}
        onChange={handleChange}
        onFocus={() => setFocusedInput("password")}
        toggleVisibility={togglePasswordVisibility}
        helperText={passwordHelperText}
        variant={
          passwordVariant as "success" | "warning" | "danger" | undefined
        }
        placeholder="Password"
        className={className}
        autoComplete={autoComplete}
      />
      <RequirementsDisplay shouldShow validationResult={validationResult} />
      {showConfirmation && (
        <PasswordField
          label={`Confirm ${label}`}
          value={confirmationValue || ""}
          name={confirmationName}
          isVisible={isConfirmationVisible}
          onChange={handleConfirmationChange}
          onFocus={() => setFocusedInput("confirmation")}
          toggleVisibility={toggleConfirmationVisibility}
          helperText={confirmationHelperText}
          variant={
            confirmationVariant as "success" | "warning" | "danger" | undefined
          }
          placeholder="Confirm Password"
          className={className}
          autoComplete={confirmationAutoComplete}
        />
      )}
    </div>
  )
}

export default PasswordInput
