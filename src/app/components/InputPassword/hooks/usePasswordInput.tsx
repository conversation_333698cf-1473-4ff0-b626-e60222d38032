import { useState, useEffect, useMemo } from "react"
import { PasswordValidationResult } from "@domain/entities/PasswordTypes"
import {
  validatePassword,
  validateRegistrationPassword,
} from "@domain/validation/validatePassword"
import { UsePasswordInputProps } from "@domain/entities/Input"

import { getInputVariant, getHelperText } from "../utils/passwordInputHelpers"

export const usePasswordInput = ({
  value,
  onChange,
  confirmationValue,
  onConfirmationChange,
  showRequirements,
}: UsePasswordInputProps) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isConfirmationVisible, setIsConfirmationVisible] = useState(false)
  const [validationResult, setValidationResult] =
    useState<PasswordValidationResult>({
      isValid: false,
      requirements: [],
    })

  const [isDirty, setIsDirty] = useState(false)
  const [isConfirmationDirty, setIsConfirmationDirty] = useState(false)
  const [focusedInput, setFocusedInput] = useState<
    "password" | "confirmation" | null
  >(null)

  useEffect(() => {
    if (isDirty) {
      setValidationResult(
        showRequirements
          ? validateRegistrationPassword(value)
          : validatePassword(value),
      )
    }
  }, [value, isDirty, showRequirements])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    if (!isDirty) {
      setIsDirty(true)
    }
  }

  const handleConfirmationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!onConfirmationChange) return

    const newValue = e.target.value
    onConfirmationChange(newValue)
    if (!isConfirmationDirty) {
      setIsConfirmationDirty(true)
    }
  }

  const togglePasswordVisibility = () =>
    setIsPasswordVisible(!isPasswordVisible)
  const toggleConfirmationVisibility = () =>
    setIsConfirmationVisible(!isConfirmationVisible)

  const passwordVariant = useMemo(() => {
    if (!isDirty) return undefined
    if (validationResult.isValid) return "success"
    return "danger"
  }, [isDirty, validationResult.isValid])

  const confirmationVariant = getInputVariant(
    isConfirmationDirty,
    true,
    validationResult,
    value,
    confirmationValue,
  )

  const passwordHelperText = getHelperText(
    false,
    isDirty,
    validationResult,
    value,
    undefined,
    showRequirements,
  )
  const confirmationHelperText = getHelperText(
    true,
    isConfirmationDirty,
    validationResult,
    value,
    confirmationValue,
  )

  const shouldShowRequirements = useMemo(
    () => showRequirements && (isDirty || !validationResult.isValid),
    [showRequirements, isDirty, validationResult.isValid],
  )

  return {
    isPasswordVisible,
    isConfirmationVisible,
    validationResult,
    isDirty,
    isConfirmationDirty,
    focusedInput,
    handleChange,
    handleConfirmationChange,
    togglePasswordVisibility,
    toggleConfirmationVisibility,
    passwordVariant,
    confirmationVariant,
    passwordHelperText,
    confirmationHelperText,
    shouldShowRequirements,
    setFocusedInput,
  }
}
