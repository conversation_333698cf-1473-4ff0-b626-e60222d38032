import React from "react"
import {
  Input,
  IconEyeOutline,
  IconEyeSlashOutline,
  Label,
} from "@kickavenue/ui/components"
import { PasswordValidationResult } from "@domain/entities/PasswordTypes"
import { PasswordFieldProps } from "@domain/entities/Input"

import { renderRequirements } from "../utils/passwordInputHelpers"

export const PasswordField: React.FC<PasswordFieldProps> = ({
  label,
  value,
  isVisible,
  onChange,
  onBlur,
  onFocus,
  toggleVisibility,
  helperText,
  variant,
  placeholder,
  className,
  name,
  autoComplete,
}) => {
  const inputType = isVisible ? "text" : "password"
  const rightIcon = isVisible ? (
    <IconEyeOutline onClick={toggleVisibility} className="text-gray-w-40" />
  ) : (
    <IconEyeSlashOutline
      onClick={toggleVisibility}
      className="text-gray-w-40"
    />
  )

  return (
    <div className={className}>
      <Label state="required" type="default" size="sm">
        {label}
      </Label>
      <Input
        type={inputType}
        value={value}
        name={name}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={placeholder}
        rightIcon={rightIcon}
        helperText={helperText}
        variant={variant}
        className="mt-2"
        autoComplete={autoComplete}
      />
    </div>
  )
}

interface RequirementsDisplayProps {
  shouldShow: boolean
  validationResult: PasswordValidationResult
}

export const RequirementsDisplay: React.FC<RequirementsDisplayProps> = ({
  shouldShow,
  validationResult,
}) => {
  if (!shouldShow) return null
  return renderRequirements(validationResult)
}
