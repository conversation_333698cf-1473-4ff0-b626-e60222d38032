"use client"

import Image from "next/image"
import Heading from "@kickavenue/ui/components/Heading"
import Text from "@kickavenue/ui/components/Text"
import { useProductStore } from "@stores/productStore"
import BuyAndSellButton from "@shared/BuyAndSellButton"
import { getProductImageUrl } from "@utils/misc"

const ProductTradeSummary = () => {
  const { detail: product } = useProductStore()
  const imgSrc = getProductImageUrl(product)
  return (
    <>
      <div className="col-span-4 flex items-center md:col-span-6">
        <div className="mr-base rounded-xs bg-gray-w-95 p-2">
          <Image alt="Product" src={imgSrc} width={82} height={82} />
        </div>
        <div className="flex flex-col">
          <Heading heading="5" textStyle="bold">
            {product?.name || ""}
          </Heading>
          <Text size="base" state="secondary" type="regular">
            SKU: {product?.skuCode || ""}
          </Text>
        </div>
      </div>
      <div className="col-span-4 md:col-span-6">
        <div className="grid grid-cols-12 gap-base">
          <BuyAndSellButton />
        </div>
      </div>
    </>
  )
}

export default ProductTradeSummary
