import Empty from "@kickavenue/ui/components/Empty"
import SizeList from "@components/shared/SizeList"
import { useProductStore } from "stores/productStore"
import { TItemListing } from "types/itemListing.type"

export interface ProductSizeListDataProps {
  list: TItemListing[] | undefined
}

const ProductSizeListData = ({ list }: ProductSizeListDataProps) => {
  const { detail: product } = useProductStore()
  const itemListing = product?.itemListing || ({} as TItemListing)
  if (!list || !list.length) {
    return (
      <div className="flex size-full items-center justify-center">
        <Empty
          title="No listing available"
          subText="Please check brand new section"
        />
      </div>
    )
  }
  return <SizeList list={list} defaultSelected={itemListing} />
}

export default ProductSizeListData
