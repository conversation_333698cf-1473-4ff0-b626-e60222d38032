import React, { useState, useEffect } from "react"
import {
  IconDangerCircleBulk,
  IconSuccessCircleBulk,
  Input,
  Label,
} from "@kickavenue/ui/components"
import { z } from "zod"
import { EmailInputProps, InputVariant } from "@domain/entities/Input"

const emailSchema = z.string().email("Enter a valid email address")

interface EmailInput extends EmailInputProps {
  value: string
  disabled?: boolean
}

const EmailInput: React.FC<EmailInput> = ({
  onChange,
  value,
  className = "",
  disabled = false,
}) => {
  const [emailError, setEmailError] = useState<string | undefined>(undefined)
  const [emailVariant, setEmailVariant] =
    useState<InputVariant["variant"]>(undefined)

  const validateEmail = (value: string) => {
    if (!value) {
      setEmailError(undefined)
      setEmailVariant(undefined)
      return false
    }
    try {
      emailSchema.parse(value)
      setEmailError(undefined)
      setEmailVariant("success")
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        setEmailError(error.errors[0].message)
        setEmailVariant("danger")
      }
      return false
    }
  }

  useEffect(() => {
    validateEmail(value)
  }, [value])

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value
    const isValid = validateEmail(newEmail)
    onChange?.(newEmail, isValid)
  }

  const emailRightIcon = (() => {
    switch (emailVariant) {
      case "danger":
        return <IconDangerCircleBulk />
      case "success":
        return <IconSuccessCircleBulk />
      default:
        return undefined
    }
  })()

  const labelState = disabled ? "default" : "required"

  return (
    <div className={className}>
      <Label state={labelState} type="default" size="sm">
        Email
      </Label>
      <Input
        type="text"
        className="mt-2"
        rightIcon={emailRightIcon}
        placeholder="Email"
        value={value}
        onChange={handleEmailChange}
        helperText={emailError}
        variant={emailVariant}
        disabled={disabled}
        autoComplete="email"
      />
    </div>
  )
}

export default EmailInput
