"use client"

import Heading from "@kickavenue/ui/components/Heading"
import Text from "@kickavenue/ui/components/Text"
import { Button } from "@kickavenue/ui/components/Button"
import useExpandableText from "@app/hooks/useExpandableText"
import { useProductStore } from "stores/productStore"

interface ProductDescriptionsProps {
  description?: string
}

const ProductDescriptions = ({ description }: ProductDescriptionsProps) => {
  const { detail: product } = useProductStore()
  const { getRenderAction, getRenderText, toggleExpand } = useExpandableText(
    description || product?.description || "",
    314,
  )
  return (
    <div className="mb-lg">
      <Heading heading="5" textStyle="bold" className="mb-base">
        Product Description
      </Heading>
      <Text size="sm" state="secondary" type="regular">
        {getRenderText().replaceAll(/<[^>]*>/g, "")}
      </Text>
      <Button
        style={{ padding: 0 }}
        variant="link"
        size="md"
        onClick={() => toggleExpand()}
      >
        {getRenderAction()}
      </Button>
    </div>
  )
}

export default ProductDescriptions
