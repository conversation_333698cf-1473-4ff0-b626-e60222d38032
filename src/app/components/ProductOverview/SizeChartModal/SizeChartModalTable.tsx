import { Text } from "@kickavenue/ui/dist/src/components"
import Table from "@components/shared/Table"
import { Size, Size<PERSON>hart } from "types/sizeChart.type"
import { TTableColumn } from "types/table.type"
import TableHeaders from "@components/shared/TableHeaders"

import styles from "./SizeChartModalTable.module.scss"
import SizeChartModalTableTextCol from "./SizeChartModalTableTextCol"

const SizeChartModalTable = ({ sizeChart }: { sizeChart: SizeChart }) => {
  const columns = [
    {
      title: "US",
      dataIndex: "us",
      key: "us",
      headerClassName: "[&>div]:justify-center",
      render: (rec: Size) => <SizeChartModalTableTextCol text={rec.us} />,
    },
    {
      title: "UK",
      dataIndex: "uk",
      key: "uk",
      headerClassName: "[&>div]:justify-center",
      render: (rec: Size) => <SizeChartModalTableTextCol text={rec.uk} />,
    },
    {
      title: "EU",
      dataIndex: "eu",
      key: "eu",
      headerClassName: "[&>div]:justify-center",
      render: (rec: Size) => <SizeChartModalTableTextCol text={rec.eu} />,
    },
    {
      title: "CM",
      dataIndex: "cm",
      key: "cm",
      headerClassName: "[&>div]:justify-center",
      render: (rec: Size) => <SizeChartModalTableTextCol text={rec.cm} />,
    },
  ] as TTableColumn[]

  if (!sizeChart.size?.length) {
    return (
      <div className="flex flex-col gap-sm">
        <TableHeaders columns={columns} />
        <Text
          size="base"
          type="regular"
          state="secondary"
          className="text-center"
        >
          No size chart available
        </Text>
      </div>
    )
  }

  return (
    <Table
      className={styles.sizeChartModalTable}
      columns={columns}
      dataSource={sizeChart.size || []}
      rowKey="id"
    />
  )
}

export default SizeChartModalTable
