import { Text } from "@kickavenue/ui/dist/src/components"
import { Product } from "types/product.type"
import { TItemListing } from "types/itemListing.type"
import { Size } from "types/sizeChart.type"

import SizeChartModalTable from "./SizeChartModalTable"

const SizeChartModalContent = ({ item }: { item: Product }) => {
  // Extract sizes from itemPriceSummary and transform to Size interface
  const sizes: Size[] =
    (item?.itemPriceSummary
      ?.map((listing: TItemListing) => {
        const size = listing?.size
        if (!size) return null
        // Transform TSize to Size interface
        return {
          id: size.id,
          sizeChartId: size.sizeChartId,
          us: size.us || "",
          eu: size.eu || "",
          uk: size.uk || "",
          cm: size.cm || "",
        }
      })
      .filter(Boolean) as unknown as Size[]) || []

  // Show message if no size data available
  if (!item?.itemPriceSummary || item.itemPriceSummary.length === 0) {
    return (
      <div className="max-h-[612px] overflow-y-auto px-lg pt-lg">
        <Text
          size="base"
          type="regular"
          state="secondary"
          className="text-center"
        >
          Size information for this product is not available yet. <br />
          Please contact our customer service <br />
          at +62812-1000-5425 for more detail
        </Text>
      </div>
    )
  }

  // Create a mock size chart object from the available sizes
  const sizeChart = {
    id: item.sizeChartId || 0,
    name: `${item.brands?.[0]?.name || "Product"} Size Chart`,
    brandId: item.brands?.[0]?.id || 0,
    categoryId: item.categoryId || 0,
    gender: item.gender || "UNISEX",
    size: sizes,
  }

  // If no sizes were transformed, show raw data as fallback
  if (sizes.length === 0) {
    return (
      <div className="max-h-[612px] overflow-y-auto px-lg pt-lg">
        <Text size="base" type="bold" state="primary" className="mb-lg">
          Raw Size Data (Debug)
        </Text>
        <div className="space-y-sm">
          {item.itemPriceSummary.map((listing: TItemListing, index: number) => (
            <div key={index} className="rounded border p-sm">
              <Text size="sm" type="bold" state="primary">
                Listing {index + 1}:
              </Text>
              <pre className="mt-xs text-xs">
                {JSON.stringify(listing.size, null, 2)}
              </pre>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="max-h-[612px] overflow-y-auto px-lg pt-lg">
      <div className="mb-lg flex flex-col gap-lg">
        <Text size="base" type="bold" state="primary">
          {sizeChart.name}
        </Text>
        <SizeChartModalTable sizeChart={sizeChart} />
      </div>
    </div>
  )
}

export default SizeChartModalContent
