import { useInfiniteQuery } from "@tanstack/react-query"
import { useMemo } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, TSizeChartFilter } from "types/sizeChart.type"
import { SizeChartApiRepository } from "@infrastructure/repositories/sizeChartApiRepository"
import { GetAllSizeChart } from "@application/usecases/getAllSizeChart"
import { QueryStatus } from "types/network.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { TPaginatedData } from "types/apiResponse.type"
import { MiscConstant } from "@constants/misc"

const { PAGE_SIZE } = MiscConstant.PAGING_DEFAULT

const useFetchInfiniteSizeChart = (filter?: TSizeChartFilter) => {
  const getAllSizeChart = async (filter?: TSizeChartFilter) => {
    const r = new SizeChartApiRepository()
    const u = new GetAllSizeChart(r)
    return u.execute(filter)
  }

  const query = useInfiniteQuery({
    queryKey: [QueryKeysConstant.GET_ALL_SIZE_CHART, filter],
    queryFn: ({ pageParam }): Promise<TPaginatedData<SizeChart>> =>
      getAllSizeChart({
        ...filter,
        page: pageParam,
        pageSize: PAGE_SIZE,
        sort: [],
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      if (lastPage?.page < lastPage?.totalPages - 1) {
        return lastPage?.page + 1
      }
      return null
    },
    retry: false,
    staleTime: 1,
    enabled: Boolean(filter?.brandId && filter.brandId > 0), // Only run query if brandId is valid
  })

  const { data, isFetching, status } = query
  const isLoading = status === QueryStatus.Pending || isFetching
  const totalSize = data?.pages?.[0]?.totalSize
  const sizeCharts = useMemo(
    () => query?.data?.pages.map((page) => page?.content).flat(),
    [query?.data],
  )

  return { data: sizeCharts, totalSize, isLoading, query }
}

export default useFetchInfiniteSizeChart
