import { useQuery } from "@tanstack/react-query"
import { GetProductHistorical } from "@application/usecases/getProductHistorical"
import { TransactionDetailApiRepository } from "@infrastructure/repositories/transactionDetailApiRepository"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { TProductHistoricalTimeFilter } from "types/transactionDetail.type"

const useFetchProductHistorical = (
  productId: string,
  timeFilter: TProductHistoricalTimeFilter,
) => {
  const fetchProductHistorical = async (
    productId: string,
    timeFilter: TProductHistoricalTimeFilter,
  ) => {
    const r = new TransactionDetailApiRepository()
    const u = new GetProductHistorical(r)
    const res = await u.execute(productId, timeFilter)
    return res
  }

  const { data, isLoading } = useQuery({
    queryKey: [QueryKeysConstant.GET_PRODUCT_HISTORICAL, productId, timeFilter],
    queryFn: () => fetchProductHistorical(productId, timeFilter),
    enabled: Boolean(productId) && Boolean(timeFilter),
    retry: false,
  })

  return { data, isLoading }
}

export default useFetchProductHistorical
