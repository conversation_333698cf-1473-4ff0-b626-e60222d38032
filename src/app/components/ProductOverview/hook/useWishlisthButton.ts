import { useCallback, useEffect } from "react"
import { useQueryClient } from "@tanstack/react-query"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import useBulkUnWishlist from "@components/Wishlist/hooks/useBulkUnWishlist"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { useMiscStore } from "stores/miscStore"
import { MiscConstant } from "@constants/misc"
import { useProductStore } from "stores/productStore"
import { sleep } from "@utils/misc"
import useFetchMyWishlistRaw from "@components/Wishlist/hooks/useFetchMyWishlistRaw"

const { WISHLIST_SAVE_PRODUCT_DETAIL } = ModalConstant.MODAL_IDS

const { PAGE, PAGE_SIZE } = MiscConstant.PAGING_DEFAULT

const useWishlistButton = () => {
  const { setOpen } = useModalStore()
  const { setShowWishlistSnackbar } = useMiscStore()
  const { detail: product } = useProductStore()
  const { data: wishlist, isLoading: isWishlistLoading } =
    useFetchMyWishlistRaw(
      {
        itemId: product?.id,
        page: PAGE,
        pageSize: PAGE_SIZE,
        sort: [],
      },
      Boolean(product?.id),
    )
  const queryClient = useQueryClient()
  const isInWishlist = wishlist?.content?.length

  const onRemovedFromWishlist = useCallback(async () => {
    await sleep(500)
    queryClient.resetQueries({
      queryKey: [QueryKeysConstant.GET_MY_WISHLIST],
    })
  }, [queryClient])

  const { bulkUnWishlist, isLoading: isBulkUnWishlistLoading } =
    useBulkUnWishlist(onRemovedFromWishlist)

  const handleClick = useCallback(() => {
    if (!isInWishlist) {
      setOpen(true, WISHLIST_SAVE_PRODUCT_DETAIL)
      return
    }

    const ids = wishlist?.content?.map((item) => item.id)
    bulkUnWishlist(ids as number[], product)
  }, [isInWishlist, setOpen, bulkUnWishlist, wishlist, product])

  const onSaved = useCallback(() => {
    queryClient.resetQueries({
      queryKey: [QueryKeysConstant.GET_MY_WISHLIST],
    })
    setShowWishlistSnackbar(true)
  }, [queryClient, setShowWishlistSnackbar])

  const isLoading = isWishlistLoading || isBulkUnWishlistLoading

  useEffect(() => {
    return () => {
      setShowWishlistSnackbar(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return { isInWishlist, isLoading, handleClick, onSaved }
}

export default useWishlistButton
