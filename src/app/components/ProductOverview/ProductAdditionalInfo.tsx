import Text from "@kickavenue/ui/components/Text"
import Tooltip from "@kickavenue/ui/components/Tooltip"
import {
  IconInfoCircleBold,
  IconVintageItemBulk,
  IconTimeBulk,
  IconUnderRetailBulk,
} from "@kickavenue/ui/components/icons"
import { ProductInfoType } from "types/product.type"

import styles from "./ProductAdditionalInfo.module.scss"

export interface ProductAdditionalInfoProps {
  type: ProductInfoType
  tooltipText?: string
  title: string
  subtitle: string
}

const { Vintage, UnderRetail, OnlyOneLeft, None } = ProductInfoType

const ProductAdditionalInfo = ({
  type,
  tooltipText,
  title,
  subtitle,
}: ProductAdditionalInfoProps) => {
  const renderTooltip = tooltipText ? (
    <Tooltip className={styles.tooltip} direction="bottom" text={tooltipText}>
      <IconInfoCircleBold width={20} height={20} />
    </Tooltip>
  ) : null

  const renderIcon = () => {
    if (type === UnderRetail) {
      return <IconUnderRetailBulk width={32} height={32} color="#0B7A68" />
    }

    if (type === Vintage) {
      return <IconVintageItemBulk width={32} height={32} />
    }

    if (type === OnlyOneLeft) {
      return <IconTimeBulk width={32} height={32} color="#0B7A68" />
    }

    return null
  }

  if (type === None) return null

  return (
    <div className="mb-lg flex items-center gap-sm rounded-base border border-gray-w-80 p-sm">
      <div className="">{renderIcon()}</div>
      <div className="flex flex-col justify-between gap-xxs">
        <div className="flex flex-row items-center gap-xxs">
          <Text size="base" state="primary" type="bold">
            {title}
          </Text>
          {renderTooltip}
        </div>
        <Text size="sm" state="secondary" type="regular">
          {subtitle}
        </Text>
      </div>
    </div>
  )
}

export default ProductAdditionalInfo
