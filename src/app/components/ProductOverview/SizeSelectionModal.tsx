"use client"

import { Modal } from "@kickavenue/ui/components/Modal"
import { IconCloseOutline } from "@kickavenue/ui/components/icons"
import { useEffect, useMemo } from "react"
import { useProductStore } from "stores/productStore"
import { TItemListing } from "types/itemListing.type"
import SizeList from "@shared/SizeList"
import { processSummariesItem } from "@components/ProductSizeList/ProductSizeList.utils"
import { TItemConditionEnum } from "types/listing.type"

import styles from "./SizeSelectionModal.module.scss"

const SizeSelectionModal = () => {
  const {
    showSizeSelection,
    setShowSizeSelection,
    detail: product,
  } = useProductStore()

  const list = useMemo(() => {
    const allSize = (product?.itemPriceSummary?.[0] || {}) as TItemListing
    const summaries = (
      product?.itemPriceSummary || ([] as TItemListing[])
    )?.slice(1)
    const listData = [] as TItemListing[]
    summaries.forEach((item) =>
      processSummariesItem(item, TItemConditionEnum.BrandNewUsed, listData),
    )
    const finalList = [allSize, ...listData]

    return finalList
  }, [product])

  useEffect(() => {
    document.body.style.overflow = "hidden"
    return () => {
      document.body.style.overflowY = "auto"
    }
  }, [showSizeSelection])

  return (
    <Modal
      title="Size"
      open={showSizeSelection}
      trailing={
        <IconCloseOutline
          className="cursor-pointer"
          onClick={() => setShowSizeSelection(false)}
        />
      }
      classNameModalItem="md:!pb-lg"
    >
      <div className={styles["modal-content"]}>
        <SizeList list={list} defaultSelected={product?.itemListing} />
      </div>
    </Modal>
  )
}

export default SizeSelectionModal
