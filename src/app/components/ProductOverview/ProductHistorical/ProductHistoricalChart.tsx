import { Text } from "@kickavenue/ui/dist/src/components"
import Graph from "@components/shared/Graph"
import {
  getProductHistoricalChart,
  getProductHistoricalLabels,
} from "@utils/transactions.utils"
import { TProductHistorical } from "types/transactionDetail.type"

export interface TProductHistoricalChart {
  data: TProductHistorical
  isLoading: boolean
}

const ProductHistoricalChart = ({
  data,
  isLoading,
}: TProductHistoricalChart) => {
  const cartData = getProductHistoricalChart(data)

  if (isLoading) {
    return (
      <Text
        size="sm"
        state="secondary"
        type="regular"
        className="mb-base text-center"
      >
        ...
      </Text>
    )
  }

  if (!cartData?.length) {
    return (
      <Text
        size="sm"
        state="secondary"
        type="regular"
        className="mb-base text-center"
      >
        No data available
      </Text>
    )
  }

  return (
    <div className="mb-base max-h-[204px]">
      <Graph
        data={getProductHistoricalChart(data)}
        labels={getProductHistoricalLabels(data)}
      />
    </div>
  )
}

export default ProductHistoricalChart
