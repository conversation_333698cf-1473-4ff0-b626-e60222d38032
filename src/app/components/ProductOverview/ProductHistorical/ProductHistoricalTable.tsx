import { Table } from "@kickavenue/ui/components/Table"
import { TableHeader } from "@kickavenue/ui/components/Table/TableHeader"
import { TableRow } from "@kickavenue/ui/components/Table/TableRow"
import { TableHead } from "@kickavenue/ui/components/Table/TableHead"
import { TableBody } from "@kickavenue/ui/components/Table/TableBody"
import { TableCell } from "@kickavenue/ui/components/Table/TableCell"
import { TProductHistorical } from "types/transactionDetail.type"
import { MiscConstant } from "@constants/misc"
import { formatDate, formatPrice } from "@utils/misc"

import ProductHistoricalTableStatus from "./ProductHistoricalTableStatus"

export interface TProductHistoricalTableProps {
  data: TProductHistorical
  isLoading?: boolean
}

const { size, price, date } = {
  size: "20%",
  price: "40%",
  date: "40%",
}

const ProductHistoricalTable = ({
  data,
  isLoading,
}: TProductHistoricalTableProps) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead style={{ width: size, textAlign: "left" }}>Size</TableHead>
          <TableHead style={{ width: price, textAlign: "right" }}>
            Sale Price
          </TableHead>
          <TableHead style={{ width: date, textAlign: "left" }}>Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <ProductHistoricalTableStatus
          data={data}
          isLoading={isLoading || false}
        />
        {data?.sales?.map((item) => (
          <TableRow key={item?.date}>
            <TableCell style={{ width: size }}>{item?.size || "- "}</TableCell>
            <TableCell style={{ width: price, textAlign: "right" }}>
              {formatPrice(item?.salePrice, null, "IDR")}
            </TableCell>
            <TableCell style={{ width: date }}>
              {formatDate(item?.date, MiscConstant.DATE_FORMAT_SHORT)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export default ProductHistoricalTable
