"use client"

import Segmented from "@kickavenue/ui/components/Segmented"
import Heading from "@kickavenue/ui/components/Heading"
import { Button } from "@kickavenue/ui/components/Button"
import { useParams } from "next/navigation"
import {
  TProductHistorical,
  TProductHistoricalTimeFilter,
} from "types/transactionDetail.type"

import ProductStatistic from "../ProductStatistic"
import useFetchProductHistorical from "../hook/useFetchProductHistorical"

import ProductHistoricalTable from "./ProductHistoricalTable"
import ProductHistoricalChart from "./ProductHistoricalChart"

const ProductHistorical = () => {
  const params = useParams<{ productId: string }>()
  const productId = params?.productId
  const { data, isLoading } = useFetchProductHistorical(
    productId || "",
    TProductHistoricalTimeFilter.OneMonth,
  )
  return (
    <>
      <Heading heading="5" textStyle="bold" className="mb-base">
        Product Historical Data
      </Heading>
      <Segmented
        size="lg"
        variant="default"
        segments={["1M", "3M", "6M", "1YR", "All"]}
        style={{ width: "100%" }}
        className="mb-base"
        defaultActive={0}
      />
      <ProductHistoricalChart
        data={data as TProductHistorical}
        isLoading={isLoading}
      />
      <ProductHistoricalTable
        data={data as TProductHistorical}
        isLoading={isLoading}
      />
      <div className="my-base flex w-full justify-center">
        <Button size="md" variant="link">
          View All Sales
        </Button>
      </div>
      <ProductStatistic data={data as TProductHistorical} />
    </>
  )
}

export default ProductHistorical
