"use client"

import { Heading, Space } from "@kickavenue/ui/components"
import TabSwitcher from "@components/shared/TabSwitcher/TabSwitcher"
import { EKickCreditDisbursementType } from "types/disbursement.type"

import KickCreditBalance from "./KickCreditBalance"
import PendingTopUp from "./PendingTopUp"
import PendingCashOut from "./PendingCashOut"
import CreditLog from "./CreditLog"

const KickCreditContainer = () => {
  const tabs = [
    {
      id: EKickCreditDisbursementType.KickPendingTopUp,
      title: "Pending Top Up",
      content: <PendingTopUp />,
    },
    {
      id: EKickCreditDisbursementType.KickPendingCashOut,
      title: "Pending Cash Out",
      content: <PendingCashOut />,
    },
    {
      id: EKickCreditDisbursementType.KickCreditLog,
      title: "Kick Credit Log",
      content: <CreditLog />,
    },
  ]

  return (
    <div className="size-full overflow-y-auto p-sm lg:p-lg">
      <div className="flex items-center justify-between">
        <Heading heading="4" textStyle="bold">
          Kick Credit
        </Heading>
      </div>
      <Space size="lg" type="margin" direction="y" />
      <KickCreditBalance />
      <Space size="lg" type="margin" direction="y" />
      <TabSwitcher
        tabs={tabs}
        defaultTabId={EKickCreditDisbursementType.KickPendingTopUp}
      />
    </div>
  )
}

export default KickCreditContainer
