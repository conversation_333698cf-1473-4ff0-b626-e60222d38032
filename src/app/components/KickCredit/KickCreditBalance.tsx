"use client"

import {
  Button,
  Space,
  Text,
  IconKickCreditBulkColor,
  Divider,
  IconAddOutline,
  IconArrowDown2Outline,
  IconEyeOutline,
  IconEyeSlashOutline,
} from "@kickavenue/ui/components"
import { useState } from "react"
import Link from "next/link"
import { EBalanceType } from "types/balance.type"
import { objectToQueryParams } from "@utils/query.utils"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { MiscConstant } from "@constants/misc"
import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"
import { convertText, formatPrice } from "@utils/misc"

const { DEFAULT_IDR_ZERO, BULLET_MASK } = MiscConstant

const CASH_OUT_PATH = objectToQueryParams({
  path: PageRouteConstant.CASH_OUT_CONFIRMATION,
  query: {
    balanceType: convertText(EBalanceType.KickCredit).toKebabCase(),
  },
})

const KickCreditBalance = () => {
  const { currentBalance } = useGetCurrentBalanceAndFee(EBalanceType.KickCredit)
  const [showBalance, setShowBalance] = useState(true)

  const toggleBalance = () => {
    setShowBalance(!showBalance)
  }

  const displayOnHoldBalance = (amount: number) => {
    return showBalance
      ? formatPrice(amount, null, "IDR", DEFAULT_IDR_ZERO)
      : BULLET_MASK
  }

  const displayBalanceValue = showBalance ? currentBalance : BULLET_MASK

  const eyeIcon = showBalance ? (
    <IconEyeSlashOutline className="size-5 text-gray-w-40" />
  ) : (
    <IconEyeOutline className="size-5 text-gray-w-40" />
  )

  return (
    <>
      <div className="rounded-lg border border-solid border-gray-w-80 p-3">
        <div className="flex flex-col">
          <div className="flex items-center justify-between gap-x-xxxs">
            <div className="flex items-center gap-2">
              <IconKickCreditBulkColor className="size-6" />
              <Text size="base" type="regular" state="secondary">
                Balance
              </Text>
            </div>
            <div className="flex items-center gap-2">
              <Text size="base" type="bold" state="primary">
                {displayBalanceValue}
              </Text>
              <button
                type="button"
                onClick={toggleBalance}
                className="flex items-center justify-center"
              >
                {eyeIcon}
              </button>
            </div>
          </div>
          <div className="my-3">
            <Divider orientation="horizontal" type="solid" />
          </div>
          <div className="flex items-center justify-between">
            <Text size="sm" type="regular" state="secondary">
              On Hold Balance
            </Text>
            <Text size="sm" type="bold" state="primary">
              {displayOnHoldBalance(0)}
            </Text>
          </div>
        </div>
      </div>
      <Space size="lg" type="margin" direction="y" />
      <div className="flex w-full gap-base">
        <Link className="flex-1" href={PageRouteConstant.TOPUP_CONFIRMATION}>
          <Button
            size="md"
            variant="secondary"
            IconLeft={IconAddOutline}
            className="!w-full"
          >
            Top Up
          </Button>
        </Link>
        <Link className="flex-1" href={CASH_OUT_PATH}>
          <Button
            size="md"
            variant="secondary"
            IconLeft={IconArrowDown2Outline}
            className="!w-full"
          >
            Cash Out
          </Button>
        </Link>
      </div>
    </>
  )
}

export default KickCreditBalance
