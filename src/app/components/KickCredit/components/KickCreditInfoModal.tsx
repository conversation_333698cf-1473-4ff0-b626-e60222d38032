import {
  Text,
  Divider,
  IconQuestionmarkCircleBulk,
} from "@kickavenue/ui/components"
import Modal from "@shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import useFetchFeeById from "@app/hooks/useFetchFeeById"
import { MiscConstant } from "@constants/misc"
import { formatPrice, getStripAmount } from "@utils/misc"

const { KICK_CREDIT_INFO } = ModalConstant.MODAL_IDS

const KickCreditInfoModal = () => {
  const { data: feeData } = useFetchFeeById({
    id: MiscConstant.TOP_UP_FEE_ID,
  })
  const topUpFee = formatPrice(
    getStripAmount(feeData?.amount) ?? 0,
    null,
    "IDR",
  )

  const { setOpen } = useModalStore()
  const handleClose = () => {
    setOpen(false, KICK_CREDIT_INFO)
  }

  const info = {
    description:
      "Use Kick Credit for secure transactions in Kick Avenue. Easily top up your Kick Credit to make offers, complete purchases, or cash out anytime.",
    sections: [
      {
        title: "Instant Top Up",
        description: `Top up your Kick Credit anytime. A fee of ${topUpFee} applies to each top-up, regardless of the amount.`,
      },
      {
        title: "Seamless Shopping",
        description:
          "Make payments effortlessly using your Kick Credit balance.",
      },
      {
        title: "Easy Cash Out",
        description:
          "Withdraw your Kick Credit balance to your bank account without any additional fees.",
      },
      {
        title: "Stay within the listing price",
        description:
          "Your offer can't exceed the listed price. Stick to your budget and increase your chances of a great deal.",
      },
    ],
  }

  return (
    <Modal modalId={KICK_CREDIT_INFO} title="Select Bank">
      <div className="flex max-h-[480px] flex-col">
        <HeaderModal onClose={handleClose} title="What is Kick Credit?" />
        <div className="h-[427px] overflow-y-auto px-lg pt-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-4">
              <IconQuestionmarkCircleBulk className="mx-auto size-[60px] text-gray-w-40" />
              <Text
                size="base"
                state="secondary"
                type="regular"
                className="text-center"
              >
                {info.description}
              </Text>
            </div>

            <Divider orientation="horizontal" />

            <div className="mb-4 flex flex-col gap-4">
              {info.sections.map((section) => (
                <div key={section.title}>
                  <Text
                    size="base"
                    state="primary"
                    type="bold"
                    className="mb-2"
                  >
                    {section.title}
                  </Text>
                  <Text size="base" state="secondary" type="regular">
                    {section.description}
                  </Text>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default KickCreditInfoModal
