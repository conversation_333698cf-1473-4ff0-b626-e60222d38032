import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { DateRange } from "react-day-picker"
import {
  TListingItemFilterOptionValue,
  TListingItemFilterState,
} from "types/listingItem.type"

import {
  createFilterParams,
  getTabNames,
  mapParamsToFilterDateRange,
  mapParamsToFilterState,
} from "../utils"

export const useFilterDashboard = (
  selectedTab: string,
  setOpenFilterOfferPrice: (open: boolean) => void,
) => {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [filterState, setFilterState] = useState<TListingItemFilterState>({})
  const [dateRanges, setDateRanges] = useState<
    Record<string, DateRange | undefined>
  >({})

  const tabNames = useMemo(() => getTabNames(selectedTab), [selectedTab])

  useEffect(() => {
    if (!searchParams) return

    const newFilterState: TListingItemFilterState =
      mapParamsToFilterState(searchParams)

    const newDateRanges = mapParamsToFilterDateRange(searchParams)

    setFilterState(newFilterState)
    setDateRanges(newDateRanges)
  }, [searchParams])

  const applyFilters = useCallback(() => {
    const { params } = createFilterParams({
      filterState,
    })

    router.push(`${window.location.pathname}?${params.toString()}`)
    setOpenFilterOfferPrice(false)
  }, [router, filterState, setOpenFilterOfferPrice])

  const resetFilters = useCallback(() => {
    const currentTab = searchParams?.get("tab")
    const newParams = new URLSearchParams()
    if (currentTab) {
      newParams.set("tab", currentTab)
    }

    const newFilterState: Record<string, string | string[]> = currentTab
      ? { tab: currentTab }
      : {}
    setFilterState(newFilterState)
    setDateRanges({})
    router.push(`${window.location.pathname}?${newParams.toString()}`)
  }, [router, searchParams])

  const handleCloseModal = useCallback(() => {
    setOpenFilterOfferPrice(false)
  }, [setOpenFilterOfferPrice])

  const handleRemoveChip = useCallback(
    (key: string, item?: TListingItemFilterOptionValue) => {
      setFilterState((prevState) => {
        const newState = { ...prevState }
        if (!item) {
          delete newState[key]
          return newState
        }
        if (Array.isArray(newState[key]) && newState[key].length === 1) {
          delete newState[key]
          return newState
        }
        if (Array.isArray(newState[key])) {
          newState[key] = (newState[key] as string[]).filter((i) => i !== item)
          return newState
        }
        return newState
      })
      if (key?.includes("date")) {
        setDateRanges({})
      }
    },
    [],
  )

  return {
    tabNames,
    filterState,
    dateRanges,
    setFilterState,
    setDateRanges,
    applyFilters,
    resetFilters,
    handleCloseModal,
    handleRemoveChip,
  }
}
