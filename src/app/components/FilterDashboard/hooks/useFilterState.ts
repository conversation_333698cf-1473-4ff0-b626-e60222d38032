import { useState, useCallback } from "react"
import { DateRange } from "react-day-picker"
import {
  TListingItemFilterStateValue,
  TListingItemSetFilterState,
} from "types/listingItem.type"

const useFilterState = (
  setFilterState: TListingItemSetFilterState,
  setDateRanges: React.Dispatch<
    React.SetStateAction<Record<string, DateRange | undefined>>
  >,
) => {
  const [datePickerOpen, setDatePickerOpen] = useState<Record<string, boolean>>(
    {},
  )

  const handleInputClick = useCallback((key: string) => {
    setDatePickerOpen((prev) => ({
      ...Object.keys(prev).reduce((acc, k) => ({ ...acc, [k]: false }), {}),
      [key]: !prev[key],
    }))
  }, [])

  const updateFilterState = useCallback(
    (key: string, value: TListingItemFilterStateValue) => {
      setFilterState((prev) => {
        const newState = { ...prev }
        if (Array.isArray(value) && value.length === 0) {
          delete newState[key]
        } else {
          newState[key] = value
        }
        return newState
      })
    },
    [setFilterState],
  )

  const handleDateRangeSelect = useCallback(
    (key: string, range: DateRange | undefined) => {
      setDateRanges((prev) => ({ ...prev, [key]: range }))
      updateFilterState(`${key}Start`, range?.from?.toISOString() || "")
      updateFilterState(`${key}End`, range?.to?.toISOString() || "")
      if (range?.to) {
        setDatePickerOpen((prev) => ({ ...prev, [key]: false }))
      }
    },
    [updateFilterState, setDateRanges],
  )

  return {
    datePickerOpen,
    handleInputClick,
    handleDateRangeSelect,
    updateFilterState,
  }
}

export default useFilterState
