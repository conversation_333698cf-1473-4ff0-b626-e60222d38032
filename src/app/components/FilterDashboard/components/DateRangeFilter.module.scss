.customDatePicker {
  @apply drop-shadow-lg;
  & > div {
    & > div:first-child {
      padding-left: 16px;
      padding-right: 16px;
    }

    /* Only apply custom styles to divs with specific data attributes */
    div[data-selected="true"][data-from="false"][data-to="false"] {
      background-color: #f0f0f0 !important;
      border-color: #f0f0f0 !important;
    }

    div[data-from="true"],
    div[data-to="true"] {
      background-color: #f0f0f0 !important;
    }
  }
  td {
    div {
      width: auto !important;
      height: 100% !important;
    }
  }
}
