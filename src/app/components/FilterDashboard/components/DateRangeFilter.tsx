import React, { useEffect, useRef, useState } from "react"
import { DateRange } from "react-day-picker"
import { Input } from "@kickavenue/ui/components"
import DatePickerRange from "@kickavenue/ui/dist/src/components/DatePickerRange"
import { format } from "date-fns"
import { createPortal } from "react-dom"
import classes from "./DateRangeFilter.module.scss"

interface DateRangeFilterProps {
  dateRange: DateRange | undefined
  datePickerOpen: boolean
  handleInputClick: () => void
  handleDateRangeSelect: (range: DateRange | undefined) => void
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  dateRange,
  datePickerOpen,
  handleInputClick,
  handleDateRangeSelect,
}) => {
  const startDateValue = dateRange?.from
    ? format(dateRange.from, "d MMMM yyyy")
    : ""
  const endDateValue = dateRange?.to ? format(dateRange.to, "d MMMM yyyy") : ""
  const inputRef = useRef<HTMLDivElement | null>(null)
  const [position, setPosition] = useState<React.CSSProperties>({})

  // Update position when datePickerOpen changes or on window resize
  useEffect(() => {
    const updatePosition = () => {
      if (inputRef.current && datePickerOpen) {
        const parentPosition = inputRef.current.getBoundingClientRect()
        setPosition({
          position: "absolute",
          top: parentPosition.y + parentPosition.height,
          left: parentPosition.left - 16,
          right: "auto",
          bottom: "auto",
          width: 670,
          zIndex: 31,
        })
      }
    }

    // Update position immediately
    updatePosition()

    // Also update on window resize
    window.addEventListener("resize", updatePosition)

    // Clean up
    return () => {
      window.removeEventListener("resize", updatePosition)
    }
  }, [datePickerOpen])

  return (
    <div className="relative h-[300px] px-6">
      <div ref={inputRef} className="mt-6 flex items-center">
        <Input
          type="string"
          prefix="Start Date"
          placeholder="Select Start Date"
          value={startDateValue}
          onClick={handleInputClick}
          readOnly
        />
        <div className="mx-3">-</div>
        <Input
          type="string"
          prefix="End Date"
          placeholder="Select End Date"
          value={endDateValue}
          onClick={handleInputClick}
          readOnly
        />
      </div>
      {datePickerOpen &&
        createPortal(
          <div className={classes["customDatePicker"]} style={position}>
            <DatePickerRange
              selectedRange={dateRange}
              setSelectedRange={handleDateRangeSelect}
            />
          </div>,
          document.body,
        )}
    </div>
  )
}

export default DateRangeFilter
