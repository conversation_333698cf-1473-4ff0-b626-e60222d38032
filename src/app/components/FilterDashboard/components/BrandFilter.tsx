"use client"

import React, { useEffect } from "react"
import { CheckBox, Search, Text } from "@kickavenue/ui/components"
import { useInView } from "react-intersection-observer"
import Spinner from "@components/shared/Spinner/Spinner"
import useFetchBrands from "@app/hooks/useFetchBrands"
import {
  TListingItemFilterStateValue,
  TListingItemFilterOption,
  TListingItemFilterStateKey,
} from "types/listingItem.type"
import { TBrandItem } from "types/brand.type"
import { isEmptyArray } from "@utils/misc"

import { getNewSelectedFilters, isFilterSelected } from "../utils"

const { Brand } = TListingItemFilterStateKey

interface BrandFilterProps {
  selectedBrands: TListingItemFilterOption[]
  updateFilterState: (key: string, value: TListingItemFilterStateValue) => void
}

const BrandFilter: React.FC<BrandFilterProps> = ({
  selectedBrands,
  updateFilterState,
}) => {
  const { brands, hasNextPage, fetchNextPage, handleBrandSearch, isLoading } =
    useFetchBrands()

  const { ref, inView } = useInView()

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  const handleBrandToggle = (brand: TBrandItem) => {
    const newSelectedBrands = getNewSelectedFilters(selectedBrands, brand)
    updateFilterState(Brand, newSelectedBrands)
  }

  return (
    <div className="h-[250px] overflow-y-auto px-6 pt-6">
      <Search
        placeholder="Search"
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          handleBrandSearch(e.target.value)
        }
        className="mb-4"
      />
      {brands?.map((brand) => (
        <div key={brand.id} className="mb-2">
          <CheckBox
            checked={isFilterSelected(selectedBrands, brand)}
            onChange={() => handleBrandToggle(brand)}
            label={brand.name}
            className="!mb-3"
          />
        </div>
      ))}
      {!isLoading && isEmptyArray(brands) && (
        <div className="flex h-1/2 w-full items-center justify-center">
          <Text size="sm" state="secondary" type="regular">
            No brands found
          </Text>
        </div>
      )}
      {(hasNextPage || isLoading) && (
        <div className="flex h-1/2 w-full items-center justify-center">
          <div ref={ref} className="py-sm">
            <Spinner />
          </div>
        </div>
      )}
    </div>
  )
}

export default BrandFilter
