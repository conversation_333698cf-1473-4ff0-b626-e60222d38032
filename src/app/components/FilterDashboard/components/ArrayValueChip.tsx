import React from "react"
import { Chip } from "@kickavenue/ui/components"
import {
  TListingItemFilterOption,
  TListingItemFilterOptionValue,
} from "types/listingItem.type"

import { getFilterStateValue } from "../utils"
import { uniqueId } from "lodash"

interface ArrayValueChipProps {
  keyName: string
  values: string[] | TListingItemFilterOption[]
  handleRemoveChip: (key: string, item?: TListingItemFilterOptionValue) => void
}

const ArrayValueChip: React.FC<ArrayValueChipProps> = ({
  keyName,
  values,
  handleRemoveChip,
}) => {
  const mappedValues = values.map((item) => ({
    key: `${keyName}-${item}`,
    value: getFilterStateValue(item),
    item,
  }))
  return (
    <>
      {mappedValues.map(({ value, item }) => (
        <Chip
          key={uniqueId("brand")}
          size="sm"
          isRemovable
          className="!mb-4 whitespace-nowrap"
          onRemove={() => handleRemoveChip(keyName, item)}
        >
          {value}
        </Chip>
      ))}
    </>
  )
}

export default ArrayValueChip
