"use client"

import React, { useEffect, useState } from "react"
import { Modal, IconCloseOutline, Divider } from "@kickavenue/ui/components"
import { DateRange } from "react-day-picker"
import {
  TListingItemFilterOptionValue,
  TListingItemFilterState,
  TListingItemSetFilterState,
} from "types/listingItem.type"

import { getFilterCount } from "../utils"
import useFilterState from "../hooks/useFilterState"

import TabComponent from "./TabComponent"
import FilterContent from "./FilterContent"
import FilterActions from "./FilterActions"

interface FilterModalProps {
  openFilterOfferPrice: boolean
  setOpenFilterOfferPrice: (open: boolean) => void
  tabNames: string[]
  filterState: TListingItemFilterState
  setFilterState: TListingItemSetFilterState
  applyFilters: () => void
  resetFilters: () => void
  handleRemoveChip: (key: string, item?: TListingItemFilterOptionValue) => void
  setDateRanges: React.Dispatch<
    React.SetStateAction<Record<string, DateRange | undefined>>
  >
  dateRanges: Record<string, DateRange | undefined>
}

const FilterModal: React.FC<FilterModalProps> = ({
  openFilterOfferPrice,
  tabNames,
  filterState,
  dateRanges,
  setOpenFilterOfferPrice,
  setFilterState,
  applyFilters,
  resetFilters,
  handleRemoveChip,
  setDateRanges,
}) => {
  const [tabCurrent, setTabCurrent] = useState<string>(tabNames[0] || "")
  const {
    datePickerOpen,
    handleInputClick,
    handleDateRangeSelect,
    updateFilterState,
  } = useFilterState(setFilterState, setDateRanges)

  useEffect(() => {
    if (openFilterOfferPrice) {
      document.body.style.overflow = "hidden"
    }
    return () => {
      document.body.style.overflowY = "auto"
    }
  }, [openFilterOfferPrice])

  return (
    <Modal
      showDivider
      open={openFilterOfferPrice}
      trailing={
        <IconCloseOutline
          className="size-6 cursor-pointer"
          color="#A3A3A3"
          onClick={() => setOpenFilterOfferPrice(false)}
        />
      }
      title="Filter"
      classNameModalChild="!p-0 !pt-6"
      classNameModalItem="!min-h-[520px]"
    >
      <div className="relative">
        <Divider
          orientation="horizontal"
          className="absolute inset-x-0 bottom-0 z-0"
        />
        <TabComponent
          tabNames={tabNames}
          tabCurrent={tabCurrent}
          setTabCurrent={setTabCurrent}
          getFilterCount={(tabName) => getFilterCount(tabName, filterState)}
        />
      </div>
      <div className="flex h-[300px] flex-1 flex-col [&>div]:flex-1">
        <FilterContent
          tabCurrent={tabCurrent}
          filterState={filterState}
          updateFilterState={updateFilterState}
          dateRanges={dateRanges}
          datePickerOpen={datePickerOpen}
          handleInputClick={handleInputClick}
          handleDateRangeSelect={handleDateRangeSelect}
        />
      </div>
      <FilterActions
        filterState={filterState}
        handleRemoveChip={handleRemoveChip}
        resetFilters={resetFilters}
        applyFilters={applyFilters}
      />
    </Modal>
  )
}

export default FilterModal
