"use client"

import React, { useEffect } from "react"
import { CheckBox, Text } from "@kickavenue/ui/components"
import { useInView } from "react-intersection-observer"
import Spinner from "@components/shared/Spinner/Spinner"
import useFetchCategories from "@app/hooks/useFetchCategories"
import { QueryStatus } from "types/network.type"
import {
  TListingItemFilterStateKey,
  TListingItemFilterStateValue,
  TListingItemFilterOption,
} from "types/listingItem.type"
import { TCategory } from "types/category.type"
import { isEmptyArray } from "@utils/misc"

import { getNewSelectedFilters, isFilterSelected } from "../utils"

const { Categories } = TListingItemFilterStateKey

interface CategoriesFilterProps {
  selectedCategories: TListingItemFilterOption[]
  updateFilterState: (key: string, value: TListingItemFilterStateValue) => void
}

const CategoriesFilter: React.FC<CategoriesFilterProps> = ({
  selectedCategories,
  updateFilterState,
}) => {
  const {
    fetchNextPage,
    status,
    categories,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
  } = useFetchCategories()

  const { ref, inView } = useInView()

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  const handleCategoryToggle = (category: TCategory) => {
    const newSelectedCategories = getNewSelectedFilters(
      selectedCategories,
      category,
    )
    updateFilterState(Categories, newSelectedCategories)
  }

  return (
    <div className="h-[250px] overflow-y-auto px-6 pt-6">
      {categories?.map((category) => (
        <div key={category.id} className="mb-2">
          <CheckBox
            checked={isFilterSelected(selectedCategories, category)}
            onChange={() => handleCategoryToggle(category)}
            label={category.name as string}
            className="!mb-3"
          />
        </div>
      ))}
      {!isFetching && isEmptyArray(categories) && (
        <div className="flex h-1/2 w-full items-center justify-center">
          <Text size="sm" state="secondary" type="regular">
            No categories found
          </Text>
        </div>
      )}
      {(hasNextPage || isFetching || status === QueryStatus.Pending) && (
        <div className="flex h-1/2 w-full items-center justify-center">
          <div ref={ref} className="py-sm">
            {isFetchingNextPage && <Spinner />}
          </div>
        </div>
      )}
    </div>
  )
}

export default CategoriesFilter
