"use client"

import React from "react"
import { DateRange } from "react-day-picker"
import {
  TListingItemFilterOption,
  TListingItemFilterState,
  TListingItemFilterStateValue,
} from "types/listingItem.type"

import { mapHasOfferOptions, mapOfferFilterStateToOptions } from "../utils"

import StatusFilter from "./StatusFilter"
import PriceRangeFilter from "./PriceRangeFilter"
import SizeFilter from "./SizeFilter"
import CategoriesFilter from "./CategoriesFilter"
import BrandFilter from "./BrandFilter"
import ConditionsFilter from "./ConditionsFilter"
import DateRangeFilter from "./DateRangeFilter"
import MultiOptionFilter from "./MultiOptionFilter"
import SingleConditionFilter from "./SingleConditionFilter"

interface FilterContentProps {
  tabCurrent: string
  filterState: TListingItemFilterState
  dateRanges: Record<string, DateRange | undefined>
  datePickerOpen: Record<string, boolean>
  updateFilterState: (key: string, value: TListingItemFilterStateValue) => void
  handleInputClick: (key: string) => void
  handleDateRangeSelect: (key: string, range: DateRange | undefined) => void
}

// eslint-disable-next-line max-lines-per-function
const FilterContent: React.FC<FilterContentProps> = ({
  tabCurrent,
  filterState,
  dateRanges,
  datePickerOpen,
  updateFilterState,
  handleInputClick,
  handleDateRangeSelect,
}) => {
  const key = tabCurrent.toLowerCase().replace(" ", "")

  switch (key) {
    case "status":
      return (
        <StatusFilter
          selectedStatus={(filterState[key] as string[]) || []}
          updateStatus={(status: string[]) => updateFilterState(key, status)}
        />
      )
    case "hasoffer":
      return (
        <MultiOptionFilter
          selectedOptions={mapOfferFilterStateToOptions(filterState)}
          options={["Yes", "No"]}
          onChange={(selectedOptions: string[]) =>
            updateFilterState(key, mapHasOfferOptions(selectedOptions))
          }
          multiSelect={false}
        />
      )
    case "offerprice":
    case "purchaseamount":
    case "listingprice":
    case "soldprice":
    case "price":
      return (
        <PriceRangeFilter
          minPrice={(filterState[`${key}Min`] as string) || ""}
          maxPrice={(filterState[`${key}Max`] as string) || ""}
          updatePrice={(min: string, max: string) => {
            updateFilterState(`${key}Min`, min)
            updateFilterState(`${key}Max`, max)
          }}
        />
      )
    case "size":
      return (
        <SizeFilter
          selectedSizes={(filterState[key] as TListingItemFilterOption[]) || []}
          updateFilterState={updateFilterState}
        />
      )
    case "categories":
      return (
        <CategoriesFilter
          selectedCategories={
            (filterState[key] as TListingItemFilterOption[]) || []
          }
          updateFilterState={updateFilterState}
        />
      )
    case "brand":
      return (
        <BrandFilter
          selectedBrands={
            (filterState[key] as TListingItemFilterOption[]) || []
          }
          updateFilterState={updateFilterState}
        />
      )
    case "condition":
      return (
        <ConditionsFilter
          selectedConditions={(filterState[key] as string[]) || []}
          updateConditions={(conditions: string[]) =>
            updateFilterState(key, conditions)
          }
        />
      )
    case "singlecondition":
      return (
        <SingleConditionFilter
          selectedCondition={(filterState[key] as string[]) || []}
          updateCondition={(condition: string[]) =>
            updateFilterState(key, condition)
          }
        />
      )
    case "createddate":
    case "purchasedate":
    case "solddate":
      return (
        <DateRangeFilter
          dateRange={dateRanges[key]}
          datePickerOpen={datePickerOpen[key]}
          handleInputClick={() => handleInputClick(key)}
          handleDateRangeSelect={(range) => handleDateRangeSelect(key, range)}
        />
      )
    default:
      return <div className="h-[300px] px-6">Content for {tabCurrent}</div>
  }
}

export default FilterContent
