"use client"

import { CheckBox } from "@kickavenue/ui/components"
import { useFilterDashboardStore } from "stores/filterDashboardStore"

interface StatusFilterProps {
  selectedStatus: string[]
  updateStatus: (status: string[]) => void
}

const StatusFilter: React.FC<StatusFilterProps> = ({
  selectedStatus,
  updateStatus,
}) => {
  const { statusOptions } = useFilterDashboardStore()
  const status = statusOptions

  const handleStatusToggle = (status: string) => {
    const newSelectedStatus = selectedStatus.includes(status)
      ? selectedStatus.filter((s) => s !== status)
      : [...selectedStatus, status]
    updateStatus(newSelectedStatus)
  }

  return (
    <div className="h-[300px] overflow-y-auto px-6 pt-6">
      {status.map((statusItem) => (
        <div key={statusItem} className="mb-2">
          <CheckBox
            checked={selectedStatus.includes(statusItem)}
            onChange={() => handleStatusToggle(statusItem)}
            label={statusItem}
            className="!mb-3"
          />
        </div>
      ))}
    </div>
  )
}

export default StatusFilter
