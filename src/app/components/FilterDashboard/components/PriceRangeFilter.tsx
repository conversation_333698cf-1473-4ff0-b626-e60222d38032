"use client"

import React from "react"
import { Input } from "@kickavenue/ui/components"
import useHandleOnNumberChange from "@hooks/useHandleOnNumberChange"

interface PriceRangeFilterProps {
  minPrice: string
  maxPrice: string
  updatePrice: (min: string, max: string) => void
}

const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({
  minPrice,
  maxPrice,
  updatePrice,
}) => {
  const { handleOnNumberChange, formatInputNumber } = useHandleOnNumberChange()

  return (
    <div className="h-[300px] overflow-y-auto px-6 pt-6">
      <div className="mb-4 flex items-center">
        <Input
          type="default"
          prefix="IDR"
          placeholder="Min Price"
          value={formatInputNumber(minPrice)}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleOnNumberChange(e, (value: string) =>
              updatePrice(value || "", maxPrice),
            )
          }
        />
        <div className="mx-3">-</div>
        <Input
          type="default"
          prefix="IDR"
          placeholder="Max Price"
          value={formatInputNumber(maxPrice)}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleOnNumberChange(e, (value: string) =>
              updatePrice(minPrice, value || ""),
            )
          }
        />
      </div>
    </div>
  )
}

export default PriceRangeFilter
