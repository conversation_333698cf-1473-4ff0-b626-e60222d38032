"use client"

import React from "react"
import { format } from "date-fns"
import { MiscConstant } from "@constants/misc"
import {
  TListingItemFilterOptionValue,
  TListingItemFilterState,
} from "types/listingItem.type"

import DateRangeChip from "./DateRangeChip"
import PriceRangeChip from "./PriceRangeChip"
import SingleValueChip from "./SingleValueChip"
import ArrayValueChip from "./ArrayValueChip"

const { TAB, PAGE, PAGE_SIZE, CATEGORY_ID, BRAND_ID, SIZE_ID, SORT_BY } =
  MiscConstant.FILTER_FIELDS

interface FilterChipsProps {
  filterState: TListingItemFilterState
  handleRemoveChip: (key: string, item?: TListingItemFilterOptionValue) => void
  formatPrice: (price: string) => string
}

const skipKeys = [TAB, PAGE, PAGE_SIZE, CATEGORY_ID, BRAND_ID, SIZE_ID, SORT_BY]

const FilterChips: React.FC<FilterChipsProps> = ({
  filterState,
  handleRemoveChip,
  formatPrice,
}) => {
  const formatDateForChip = (dateString: string): string => {
    if (!dateString) return ""
    const date = new Date(dateString)
    return format(date, "d MMMM yyyy")
  }

  const renderChips = () => {
    const chips: React.ReactNode[] = []
    const dateRanges: Record<string, { start: string; end: string }> = {}

    Object.entries(filterState).forEach(([key, value]) => {
      if (skipKeys.includes(key)) return
      if (key.endsWith("Min") || key.endsWith("Max")) return
      if (key.endsWith("Start") || key.endsWith("End")) {
        const baseKey = key.replace("Start", "").replace("End", "")
        if (!dateRanges[baseKey]) dateRanges[baseKey] = { start: "", end: "" }
        dateRanges[baseKey][key.endsWith("Start") ? "start" : "end"] =
          value as string
        return
      }
      if (Array.isArray(value)) {
        chips.push(
          <ArrayValueChip
            key={key}
            keyName={key}
            values={value}
            handleRemoveChip={handleRemoveChip}
          />,
        )
      } else {
        chips.push(
          <SingleValueChip
            key={key}
            keyName={key}
            value={value as string}
            handleRemoveChip={handleRemoveChip}
          />,
        )
      }
    })

    Object.entries(dateRanges).forEach(([key, { start, end }]) => {
      chips.push(
        <DateRangeChip
          key={key}
          keyName={key}
          start={start}
          end={end}
          formatDateForChip={formatDateForChip}
          handleRemoveChip={handleRemoveChip}
        />,
      )
    })

    return chips
  }

  return (
    <div className="hide-scrollbar col-span-12 flex gap-3 overflow-x-auto pb-2">
      {renderChips()}
      <PriceRangeChip
        filterState={filterState}
        handleRemoveChip={handleRemoveChip}
        formatPrice={formatPrice}
      />
    </div>
  )
}

export default FilterChips
