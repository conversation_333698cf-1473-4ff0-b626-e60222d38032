"use client"

import { <PERSON><PERSON>, Dropdown } from "@kickavenue/ui/components"
import { DropdownItemProps } from "@kickavenue/ui/components/Dropdown/Dropdown.type"
import { cx } from "class-variance-authority"
import React, { useCallback, useEffect, useMemo, useState } from "react"
import { useInView } from "react-intersection-observer"
import useFetchUniqueSize from "@app/hooks/useFetchUniqueSize"
import Spinner from "@components/shared/Spinner"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { getUniqueSizeIds } from "@utils/sizeChart.utils"
import {
  TListingItemFilterOption,
  TListingItemFilterStateKey,
  TListingItemFilterStateValue,
} from "types/listingItem.type"
import { TUniqueSize } from "types/sizeChart.type"

import { getSizeNewSelectedFilters, isSizeFilterSelected } from "../utils"

interface SizeFilterProps {
  selectedSizes: TListingItemFilterOption[]
  updateFilterState: (key: string, value: TListingItemFilterStateValue) => void
}

const SizeFilter: React.FC<SizeFilterProps> = ({
  selectedSizes,
  updateFilterState,
}) => {
  const [openDropdown, setOpenDropdown] = useState<boolean>(false)
  const [sizeSystem, setSizeSystem] = useState<string>("US")
  const { ref, inView } = useInView()

  const {
    data: uniqueSize,
    isLoading,
    hasNextPage,
    isFetching,
    fetchNextPage,
  } = useFetchUniqueSize({})

  const getSizeValue = useCallback(
    (size: TUniqueSize) => {
      switch (sizeSystem.toLowerCase()) {
        case "eu":
          return size.eu
        case "uk":
          return size.uk
        default:
          return size.us
      }
    },
    [sizeSystem],
  )

  const handleSizeToggle = useCallback(
    (size: TUniqueSize) => {
      const newSelectedSizes = getSizeNewSelectedFilters(selectedSizes, size)
      updateFilterState(TListingItemFilterStateKey.Size, newSelectedSizes)
    },
    [selectedSizes, updateFilterState],
  )

  const sizeSystemOptions: DropdownItemProps[] = useMemo(
    () => [
      { text: "US", value: "US" },
      { text: "EU", value: "EU" },
      { text: "UK", value: "UK" },
    ],
    [],
  )

  const handleSizeSystemSelect = useCallback(
    (option: DropdownItemProps) => {
      setSizeSystem(option.value as string)
    },
    [setSizeSystem],
  )

  useEffect(() => {
    if (inView && !isFetching) {
      fetchNextPage()
    }
  }, [inView, isFetching, fetchNextPage])

  if (isLoading) {
    return <SpinnerLoading className="!h-[250px]" />
  }

  return (
    <div className="h-[300px] overflow-y-auto px-6 pt-6">
      <Dropdown
        className="!z-50 mb-4 !w-full !font-medium"
        options={sizeSystemOptions}
        onItemSelect={handleSizeSystemSelect}
        onClick={() => setOpenDropdown(!openDropdown)}
        open={openDropdown}
        placeholder={sizeSystem}
        type="button"
      />
      <div className="grid grid-cols-7 gap-2">
        {uniqueSize?.pages.map((page) =>
          page.content.map((size) => (
            <Button
              key={getUniqueSizeIds(size)}
              size="md"
              variant="secondary"
              onClick={() => handleSizeToggle(size)}
              className={cx(
                "!w-full !text-sm !font-medium",
                isSizeFilterSelected(size, selectedSizes) &&
                  "!border !border-gray-b-65 !bg-gray-w-80",
                !isSizeFilterSelected(size, selectedSizes) && "!bg-transparent",
              )}
            >
              {getSizeValue(size)}
            </Button>
          )),
        )}
      </div>
      {hasNextPage && (
        <div ref={ref} className="flex justify-center">
          <Spinner className="my-2" />
        </div>
      )}
    </div>
  )
}

export default SizeFilter
