import { Button } from "@kickavenue/ui/components"
import {
  TListingItemFilterOptionValue,
  TListingItemFilterState,
} from "types/listingItem.type"

import { formatPrice } from "../utils"

import FilterChips from "./FilterChips"

const FilterActions: React.FC<{
  filterState: TListingItemFilterState
  handleRemoveChip: (key: string, item?: TListingItemFilterOptionValue) => void
  resetFilters: () => void
  applyFilters: () => void
}> = ({ filterState, handleRemoveChip, resetFilters, applyFilters }) => (
  <div className="grid grid-cols-12 p-6 shadow-base">
    <FilterChips
      filterState={filterState}
      handleRemoveChip={handleRemoveChip}
      formatPrice={formatPrice}
    />
    <div className="col-span-4 mr-3">
      <Button
        size="lg"
        className="!w-full"
        variant="secondary"
        onClick={resetFilters}
      >
        Reset
      </Button>
    </div>
    <div className="col-span-8 ml-3">
      <Button
        size="lg"
        className="!h-full !w-full"
        variant="primary"
        onClick={applyFilters}
      >
        Apply
      </Button>
    </div>
  </div>
)

export default FilterActions
