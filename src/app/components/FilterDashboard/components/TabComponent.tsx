"use client"

import React from "react"
import { Tab, Button } from "@kickavenue/ui/components"
import { BuyingDashboardConstant } from "@components/BuyingDashboard/buyingDashboard.contant"
import { enumerizeFilterTabName } from "@utils/offer.utils"
import { formatStringWithSpaces } from "@utils/misc"

const { MAPPED_FILTER_TAB_CONTENT } = BuyingDashboardConstant

interface TabComponentProps {
  tabNames: string[]
  tabCurrent: string
  setTabCurrent: (tab: string) => void
  getFilterCount: (tabName: string) => number
}

const TabComponent: React.FC<TabComponentProps> = ({
  tabNames,
  tabCurrent,
  setTabCurrent,
  getFilterCount,
}) => {
  return (
    <div className="hide-scrollbar overflow-x-auto">
      <Tab className="!gap-8 whitespace-nowrap px-6">
        {tabNames.map((item) => {
          const filterCount = getFilterCount(item)
          const mappedTabName =
            MAPPED_FILTER_TAB_CONTENT[enumerizeFilterTabName(item)]
          const tabName = mappedTabName
            ? formatStringWithSpaces(mappedTabName)
            : item
          return (
            <Button
              data-active={item === tabCurrent}
              onClick={() => setTabCurrent(item)}
              variant="link"
              key={item}
              size="lg"
              className={`relative whitespace-nowrap !p-0 ${
                tabCurrent === item
                  ? "!font-bold !text-[#242424]"
                  : "!font-normal !text-gray"
              }`}
            >
              {tabName}
              {filterCount > 0 && (
                <div className="ml-1">{`(${filterCount})`}</div>
              )}
            </Button>
          )
        })}
      </Tab>
    </div>
  )
}

export default TabComponent
