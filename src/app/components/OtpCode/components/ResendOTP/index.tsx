import React from "react"
import { But<PERSON> } from "@kickavenue/ui/components"

interface ResendOTPProps {
  remainingTime: number
  handleResendRequest: () => void
  disabled?: boolean
}

const ResendOTP: React.FC<ResendOTPProps> = ({
  remainingTime,
  handleResendRequest,
  disabled,
}) => {
  const renderResendAction = () => {
    if (remainingTime > 0) {
      return (
        <p className="mt-2 text-sm text-gray">
          Request again in{" "}
          <span className="text-sm text-gray-b-75">{remainingTime}</span>{" "}
          seconds
        </p>
      )
    }

    return (
      <Button
        size="md"
        variant="link"
        className="mt-4 !p-0"
        disabled={disabled}
        onClick={handleResendRequest}
      >
        Request Again
      </Button>
    )
  }

  return (
    <div className="flex w-full flex-col items-center">
      {renderResendAction()}
    </div>
  )
}

export default ResendOTP
