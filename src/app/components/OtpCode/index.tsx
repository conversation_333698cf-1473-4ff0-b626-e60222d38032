"use client"

import React, { useEffect, useState } from "react"
import { useRegistrationStore } from "@stores/registrationStore"

import useOTP from "./hooks/useOTP"
import { useOtpTimer } from "./hooks/useOtpTimer"
import OTPInputField from "./components/OTPInputField"
import ErrorMessage from "./components/ErrorMessage"
import ResendOTP from "./components/ResendOTP"

interface OTPInputProps {
  onChange: (value: string) => void
  type: "EMAIL" | "SMS"
  error: string
  onResendError?: (error: string) => void
}

const OTPInput: React.FC<OTPInputProps> = ({
  onChange,
  type,
  error,
  onResendError,
}) => {
  const { registrationData } = useRegistrationStore()
  const { otpValues, setError, handleInputChange, handleKeyDown, handlePaste } =
    useOTP(type?.toLowerCase() as "phone" | "email")
  const { remainingTime, handleResendRequest } = useOtpTimer()
  const [isResendError, setIsResendError] = useState(false)

  useEffect(() => {
    const combinedOtp = otpValues.join("")
    onChange(combinedOtp)
  }, [otpValues, onChange])

  useEffect(() => {
    if (error) {
      setError(error)
    }
  }, [error, setError])

  const onResendRequest = async () => {
    const to =
      type === "EMAIL" ? registrationData.email : registrationData.phoneNumber

    const handleError = (error: string) => {
      setIsResendError(true)
      onResendError?.(error)
    }

    await handleResendRequest(to, type, handleError)
  }

  return (
    <div className="bg-gray-100 flex flex-col items-center justify-center">
      <div className="shadow-md rounded-lg bg-white px-6">
        <OTPInputField
          otpValues={otpValues}
          error={error}
          handleInputChange={handleInputChange}
          handleKeyDown={handleKeyDown}
          handlePaste={handlePaste}
          type={type}
        />
        <ErrorMessage error={error} />
        <ResendOTP
          disabled={isResendError}
          remainingTime={remainingTime}
          handleResendRequest={onResendRequest}
        />
      </div>
    </div>
  )
}

export default OTPInput
