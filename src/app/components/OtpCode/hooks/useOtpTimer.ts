import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { AuthService } from "@application/services/authService"
import { UseOtpTimerResult } from "./types"

// 3 minutes in seconds
const DEFAULT_TIMER_DURATION = 180
// 1 second
const TIMER_INTERVAL = 1000

// Remove the old fetch-based logic - now handled by AuthService

export const useOtpTimer = (
  initialDuration: number = DEFAULT_TIMER_DURATION,
): UseOtpTimerResult => {
  const [remainingTime, setRemainingTime] = useState(initialDuration)
  const [canResend, setCanResend] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Memoize auth service instance
  const authService = useMemo(() => new AuthService(), [])

  // Clear any existing interval
  const clearTimer = useCallback(() => {
    if (!intervalRef.current) {
      return
    }

    clearInterval(intervalRef.current)
    intervalRef.current = null
  }, [])

  // Start countdown timer
  const startCountdown = useCallback(() => {
    clearTimer()

    intervalRef.current = setInterval(() => {
      setRemainingTime((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(intervalRef.current!)
          intervalRef.current = null
          setCanResend(true)
          return 0
        }
        return prevTime - 1
      })
    }, TIMER_INTERVAL)
  }, [clearTimer])

  // Reset timer to initial state
  const resetTimer = useCallback(() => {
    clearTimer()
    setRemainingTime(initialDuration)
    setCanResend(false)
    startCountdown()
  }, [clearTimer, initialDuration, startCountdown])

  // Handle OTP resend request
  const handleResendRequest = useCallback(
    async (to: string, type: string, onError: (error: string) => void) => {
      if (!canResend) {
        onError(
          `Please wait ${remainingTime} seconds before requesting a new code`,
        )
        return
      }

      try {
        const result = await authService.resendOtp(to, type as "EMAIL" | "SMS")
        if (!result.success) {
          onError(result.error || "Failed to resend OTP")
          return
        }
        resetTimer()
      } catch (err) {
        onError(
          err instanceof Error
            ? err.message
            : "An error occurred while resending OTP",
        )
      }
    },
    [canResend, remainingTime, resetTimer, authService],
  )

  // Initialize timer on mount and cleanup on unmount
  useEffect(() => {
    resetTimer()

    // Cleanup interval on unmount
    return clearTimer
  }, [resetTimer, clearTimer])

  return {
    remainingTime,
    startCountdown,
    handleResendRequest,
    canResend,
  }
}
