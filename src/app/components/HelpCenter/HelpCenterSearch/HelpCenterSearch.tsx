"use client"

import { Breadcrumb } from "@kickavenue/ui/dist/src/components"
import { getFaqSearchBreadcrumbItem } from "@utils/faq.utils"

import HelpCenterSection from "../HelpCenterSection"

import HelpCenterSearchResult from "./HelpCenterSearchResult"
import HelpCenterSearchInput from "./HelpCenterSearchInput"

const HelpCenterSearch = () => {
  return (
    <HelpCenterSection parentClassName="mt-xl px-base pb-xl xl:px-0">
      <div className="mb-xl flex flex-wrap justify-between gap-base">
        <div className="max-w-md overflow-x-auto md:max-w-full">
          <Breadcrumb
            listItem={getFaqSearchBreadcrumbItem()}
            className="whitespace-nowrap"
          />
        </div>
        <HelpCenterSearchInput
          onClearText={() => {}}
          className="max-w-[294px]"
          placeholder="Search"
          size="sm"
        />
      </div>
      <HelpCenterSearchResult />
    </HelpCenterSection>
  )
}

export default HelpCenterSearch
