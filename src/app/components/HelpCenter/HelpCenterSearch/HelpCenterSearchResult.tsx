"use client"

import { But<PERSON>, <PERSON> } from "@kickavenue/ui/dist/src/components"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import useURLQuery from "@app/hooks/useUrlQuery"
import { FaqConstant } from "@constants/faq.constant"
import { TFaqContentFilter } from "types/faq.type"

import HelpCenterListContent from "../HelpCenterListContent"
import useFetchInfiniteFaqContent from "../hook/useFetchInfiniteFaqContent"

const { KEYWORD } = FaqConstant.FILTER_FIELDS

const HelpCenterSearchResult = () => {
  const { getSearchParam } = useURLQuery()
  const keyword = getSearchParam(KEYWORD)
  const { data, isLoading, totalSize, query } = useFetchInfiniteFaqContent({
    search: keyword,
  } as TFaqContentFilter)
  if (isLoading) {
    return <SpinnerLoading className="!h-[100px]" />
  }
  return (
    <div className="xl:mb-[219px]">
      <div className="mb-xl flex gap-xxs">
        <Text size="base" type="regular" state="primary">
          Found
        </Text>
        <Text size="base" type="bold" state="primary">
          {totalSize || 0}
        </Text>
        <Text size="base" type="regular" state="primary">
          Results for
        </Text>
        <Text size="base" type="bold" state="primary">
          {keyword}
        </Text>
      </div>
      <HelpCenterListContent
        faqContents={data}
        weight="bold"
        className="mb-xl"
      />
      {query?.hasNextPage && (
        <div className="flex justify-center">
          <Button
            size="md"
            variant="secondary"
            onClick={() => query?.fetchNextPage()}
          >
            More Results
          </Button>
        </div>
      )}
    </div>
  )
}

export default HelpCenterSearchResult
