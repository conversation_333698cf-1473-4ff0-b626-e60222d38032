import { cx } from "class-variance-authority"
import {
  Heading,
  IconBuyingDashboardOutline,
  Text,
} from "@kickavenue/ui/dist/src/components"
import { TFaq } from "types/faq.type"
import ClickableDiv from "@components/shared/ClickableDiv"
import useChangePage from "@app/hooks/useChangePage"
import { getFaqContentDetailUrl } from "@utils/faq.utils"

interface HelpCenterCategoryCardProps {
  faq?: TFaq
  className?: string
}

const HelpCenterCategoryCard = ({
  faq,
  className,
}: HelpCenterCategoryCardProps) => {
  const { goToPage } = useChangePage()
  return (
    <ClickableDiv
      className={cx(
        "cursor-pointer rounded-sm border border-gray-w-80 p-base hover:bg-gray-w-95",
        className,
      )}
      keyDownHandler={() => {}}
      onClick={() => goToPage(getFaqContentDetailUrl(faq))}
    >
      <IconBuyingDashboardOutline width={32} height={32} className="mb-base" />
      <Heading heading="5" textStyle="bold" className="mb-base">
        {faq?.category || "-"}
      </Heading>
      <Text
        size="sm"
        type="regular"
        state="secondary"
        className="max-w-[35ch] truncate"
      >
        {faq?.title || "-"}
      </Text>
    </ClickableDiv>
  )
}

export default HelpCenterCategoryCard
