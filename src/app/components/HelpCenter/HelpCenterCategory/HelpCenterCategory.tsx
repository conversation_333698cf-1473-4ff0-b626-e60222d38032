"use client"

import { useEffect, useState } from "react"
import SpinnerLoading from "@components/shared/SpinnerLoading"

import HelpCenterSection from "../HelpCenterSection"
import useFetchCurrentFaq from "../hook/useFetchCurrentFaq"

import HelpCenterCategoryCard from "./HelpCenterCategoryCard"

const HelpCenterCategory = () => {
  const { data: faqs, isLoading } = useFetchCurrentFaq()
  const [columnCount, setColumnCount] = useState(2)

  useEffect(() => {
    const handleResize = () => {
      setColumnCount(window.innerWidth >= 1024 ? 4 : 2)
    }

    handleResize()

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  const getRowsData = () => {
    const items = faqs?.content || []
    const rows: (typeof items)[] = []

    for (let i = 0; i < items.length; i += columnCount) {
      const row = items.slice(i, i + columnCount)
      rows.push(row)
    }

    return rows
  }

  const getGridColsClass = (itemCount: number) => {
    return `grid-cols-${Math.min(itemCount, columnCount)}`
  }

  if (isLoading) {
    return <SpinnerLoading className="!h-[100px]" />
  }

  return (
    <HelpCenterSection childClassName="flex flex-col justify-center px-base py-xl xl:px-0">
      <div className="flex flex-col gap-lg">
        {getRowsData().map((row, rowIndex) => (
          <div
            // eslint-disable-next-line react/no-array-index-key
            key={rowIndex}
            className={`grid ${getGridColsClass(row.length)} gap-lg`}
          >
            {row.map((faq) => (
              <HelpCenterCategoryCard key={faq.id} faq={faq} />
            ))}
          </div>
        ))}
      </div>
    </HelpCenterSection>
  )
}

export default HelpCenterCategory
