import { useQuery } from "@tanstack/react-query"
import { GetAllFaqContentUseCase } from "@application/usecases/getAllFaqContent"
import { FaqContentApiRepository } from "@infrastructure/repositories/faqContentApiRepository"
import { QueryStatus } from "types/network.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { TFaqContentFilter } from "types/faq.type"

const useFetchFaqContent = (filter?: TFaqContentFilter) => {
  const getAllFaqContent = async () => {
    const r = new FaqContentApiRepository()
    const u = new GetAllFaqContentUseCase(r)
    return u.execute(filter)
  }
  const { data, isFetching, status } = useQuery({
    queryKey: [QueryKeysConstant.GET_ALL_FAQ_CONTENT],
    queryFn: getAllFaqContent,
  })
  const isLoading = status === QueryStatus.Pending || isFetching
  return { data, isLoading }
}

export default useFetchFaqContent
