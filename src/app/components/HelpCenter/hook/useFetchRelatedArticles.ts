import { useQuery } from "@tanstack/react-query"
import { GetCurrentArticle } from "@application/usecases/getCurrentArticle"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { ArticleApiRepository } from "@infrastructure/repositories/articleApiRepository"
import { TArticleFilter } from "types/article.type"
import { QueryStatus } from "types/network.type"

const useFetchRelatedArticles = (filter?: TArticleFilter) => {
  const fetchRelatedArticles = async () => {
    const r = new ArticleApiRepository()
    const u = new GetCurrentArticle(r)
    const res = await u.execute(filter)
    return res
  }
  const { data, isFetching, status } = useQuery({
    queryKey: [QueryKeysConstant.GET_CURRENT_ARTICLE],
    queryFn: fetchRelatedArticles,
  })
  const isLoading = status === QueryStatus.Pending || isFetching
  return { data, isLoading }
}

export default useFetchRelatedArticles
