import { useInfiniteQuery } from "@tanstack/react-query"
import { useMemo } from "react"
import { GetAllFaqContentUseCase } from "@application/usecases/getAllFaqContent"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { FaqContentApiRepository } from "@infrastructure/repositories/faqContentApiRepository"
import { TFaqContent, TFaqContentFilter, TFaqFilter } from "types/faq.type"
import { TPaginatedData } from "types/apiResponse.type"
import { QueryStatus } from "types/network.type"

const useFetchInfiniteFaqContent = ({ search }: { search?: string }) => {
  const getAllFaqContent = async (filter?: TFaqContentFilter) => {
    const r = new FaqContentApiRepository()
    const u = new GetAllFaqContentUseCase(r)
    return u.execute({ ...(filter as TFaqFilter), search })
  }

  const query = useInfiniteQuery({
    queryKey: [QueryKeysConstant.GET_ALL_FAQ_CONTENT, search],
    queryFn: ({ pageParam }): Promise<TPaginatedData<TFaqContent>> =>
      getAllFaqContent({ page: pageParam, pageSize: 8, sort: [] }),
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      if (lastPage?.page < lastPage?.totalPages - 1) {
        return lastPage?.page + 1
      }
      return null
    },
  })
  const { data, isFetching, status } = query
  const isLoading = status === QueryStatus.Pending || isFetching
  const totalSize = data?.pages?.[0]?.totalSize
  const faqContents = useMemo(
    () => query?.data?.pages.map((page) => page?.content).flat(),
    [query?.data],
  )

  return { data: faqContents, totalSize, isLoading, query }
}

export default useFetchInfiniteFaqContent
