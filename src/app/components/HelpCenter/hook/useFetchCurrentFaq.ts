import { useQuery } from "@tanstack/react-query"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { FaqApiRepository } from "@infrastructure/repositories/faqApiRepository"
import { GetCurrentFaq } from "@application/usecases/getCurrentFaq"
import { MiscConstant } from "@constants/misc"
import { buildQueryStringBySortBy } from "@utils/misc"
import { QueryStatus } from "types/network.type"
import { slugifyCategory } from "@utils/faq.utils"
import { TFaqContent } from "types/faq.type"

const { PAGE } = MiscConstant.PAGING_DEFAULT

const useFetchCurrentFaq = () => {
  const getCurrentFaq = async () => {
    const r = new FaqApiRepository()
    const u = new GetCurrentFaq(r)
    return u.execute({
      page: PAGE,
      pageSize: 12,
      sort: [],
      sortBy: buildQueryStringBySortBy("sequence,ASC"),
    })
  }
  const { data, isFetching, status } = useQuery({
    queryKey: [QueryKeysConstant.GET_CURRENT_FAQ],
    queryFn: getCurrentFaq,
  })

  const mappedData = data?.content?.reduce(
    (acc, faq) => {
      const newFaq = { ...faq, contentsMapped: {} } as any
      const categoryKey = slugifyCategory(faq)
      newFaq.contentsMapped = faq.contents.reduce(
        (acc, contentItem) => {
          acc[contentItem.id] = contentItem
          return acc
        },
        {} as Record<number, TFaqContent>,
      )
      acc[categoryKey] = newFaq
      return acc
    },
    {} as Record<string, any>,
  )

  const isLoading = status === QueryStatus.Pending || isFetching
  return { data, isLoading, mappedData }
}

export default useFetchCurrentFaq
