import { Divider } from "@kickavenue/ui/dist/src/components"
import NavigationOption from "@kickavenue/ui/dist/src/components/NavigationOption"
import TNavigationOptionProps from "@kickavenue/ui/dist/src/components/NavigationOption/NavigationOption.type"
import useChangePage from "@app/hooks/useChangePage"
import { getFaqContentDetailUrl } from "@utils/faq.utils"
import { TFaqContent } from "types/faq.type"

interface HelpCenterListContentProps {
  faqContents?: TFaqContent[]
  weight?: TNavigationOptionProps["weight"]
  className?: string
}

const HelpCenterListContent = ({
  faqContents,
  weight,
  className,
}: HelpCenterListContentProps) => {
  const { goToPage } = useChangePage()
  const isLastItem = (index: number) => index === (faqContents?.length ?? 0) - 1
  if (!faqContents?.length) {
    return null
  }
  return (
    <div className={className}>
      {faqContents?.map((faqContent, index) => (
        <div key={faqContent.id}>
          <NavigationOption
            title={faqContent.title}
            weight={weight || "medium"}
            className="!cursor-pointer"
            onClick={() =>
              goToPage(getFaqContentDetailUrl(faqContent.faq, faqContent.id))
            }
          />
          {!isLastItem(index) && <Divider orientation="horizontal" />}
        </div>
      ))}
    </div>
  )
}

export default HelpCenterListContent
