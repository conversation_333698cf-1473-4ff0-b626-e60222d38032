import { Button, Heading } from "@kickavenue/ui/dist/src/components"
import SpinnerLoading from "@components/shared/SpinnerLoading"

import useFetchRelatedArticles from "../hook/useFetchRelatedArticles"

const HelpCenterArticle = () => {
  const { data, isLoading } = useFetchRelatedArticles()
  if (isLoading) {
    return <SpinnerLoading />
  }
  return (
    <>
      <Heading heading="4" textStyle="bold">
        Related Articles
      </Heading>
      <div className="flex flex-col gap-base">
        {data?.content?.map((article) => (
          <Button
            size="lg"
            variant="link"
            key={article.id}
            className="!p-0 !text-left"
          >
            {article.title}
          </Button>
        ))}
      </div>
    </>
  )
}

export default HelpCenterArticle
