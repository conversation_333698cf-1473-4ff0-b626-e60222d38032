import { queryParamsToObject } from "@utils/query.utils"
import { useSearchParams } from "next/navigation"
import { useMemo, useCallback } from "react"

type QueryValue =
  | string
  | number
  | boolean
  | string[]
  | number[]
  | string[][]
  | number[][]
  | Record<string, any>

/**
 * @description Get query params from URL
 * @returns {
 *  search: string;
 *  page: number;
 *  pageSize: number;
 *  totalPages: number;
 *  sortBy: string;
 *  get: <T extends string | number | boolean>(key: string, defaultValue: T) => T;
 *  getAll: Record<string, any>;
 * }
 */
export const useGetQueryParams = (): {
  search: string
  page: number
  pageSize: number
  totalPages: number
  sortBy: string
  get: <T extends QueryValue>(key: string, defaultValue?: T) => T
  all: Record<string, any>
} => {
  const searchParams = useSearchParams()

  const getQueryParams = useCallback(
    <T extends QueryValue>(key: string, defaultValue?: T): T => {
      const raw = searchParams.get(key)
      if (raw === null) return defaultValue as T

      // --- handle nested array ([][]) ---
      if (
        Array.isArray(defaultValue) &&
        defaultValue.length > 0 &&
        Array.isArray(defaultValue[0])
      ) {
        try {
          const parsed = JSON.parse(raw)
          if (Array.isArray(parsed)) return parsed as T
        } catch {
          // fallback kalau bukan JSON
          return defaultValue
        }
      }

      // --- handle array (string[] / number[]) ---
      if (Array.isArray(defaultValue)) {
        const arr = raw.split(",")
        if (typeof defaultValue[0] === "number") {
          return arr.map((v) => Number(v)) as T
        }
        return arr as T
      }

      // --- handle object ---
      if (
        typeof defaultValue === "object" &&
        defaultValue !== null &&
        !Array.isArray(defaultValue)
      ) {
        try {
          return JSON.parse(raw) as T
        } catch {
          return defaultValue
        }
      }

      // --- handle number ---
      if (typeof defaultValue === "number") {
        return (Number(raw) as T) || defaultValue
      }

      // --- handle boolean ---
      if (typeof defaultValue === "boolean") {
        return (raw === "true") as T
      }

      // --- fallback string ---
      return (raw as T) || defaultValue || ("" as T)
    },
    [searchParams],
  )

  const defaultQueryParams = useMemo(
    () => ({
      search: getQueryParams("search", ""),
      page: getQueryParams("page", 0),
      pageSize: getQueryParams("pageSize", 10),
      totalPages: getQueryParams("totalPages", 1),
      sortBy: getQueryParams("sortBy", "updated_at,desc"),
    }),
    [getQueryParams],
  )

  const all = useMemo(
    () => queryParamsToObject(searchParams.toString()),
    [searchParams],
  )

  return {
    ...defaultQueryParams,
    get: getQueryParams,
    all,
  }
}
