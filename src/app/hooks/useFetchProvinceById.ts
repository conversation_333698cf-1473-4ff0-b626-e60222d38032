import { useQuery } from "@tanstack/react-query"

import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { ProvinceApiRepository } from "@infrastructure/repositories/provinceApiRepository"
import { TProvince } from "types/province.type"

const useFetchProvinceById = (provinceId?: string | number) => {
  const fetchProvinceById = async () => {
    const r = new ProvinceApiRepository()
    const res = await r.getById(provinceId)
    return res
  }

  return useQuery<TProvince>({
    queryKey: [QueryKeysConstant.GET_PROVINCE_BY_ID, provinceId],
    queryFn: fetchProvinceById,
    enabled: Boolean(provinceId),
  })
}

export default useFetchProvinceById
