import { useInfiniteQuery } from "@tanstack/react-query"
import { useCallback, useState } from "react"
import debounce from "lodash/debounce"
import { GetAllBrands } from "@application/usecases/GetAllBrands"
import { BrandApiRepository } from "@infrastructure/repositories/brandApiRepository"
import { TBrandFilter } from "types/brand.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

interface UseFetchBrandsProps {
  filter?: TBrandFilter
}

const useFetchBrands = (props?: UseFetchBrandsProps) => {
  const r = new BrandApiRepository()
  const u = new GetAllBrands(r)
  const [filter, setFilter] = useState<TBrandFilter>(props?.filter ?? {})

  const debouncedSetFilter = debounce((name: string) => {
    setFilter({ ...props?.filter, name })
  }, 500)

  const handleBrandSearch = useCallback(
    (name: string) => {
      debouncedSetFilter(name)
    },
    [debouncedSetFilter],
  )

  const query = useInfiniteQuery({
    queryKey: [QueryKeysConstant.GET_BRANDS, filter],
    initialPageParam: 0,
    queryFn: ({ pageParam }) => {
      return u.execute({ page: pageParam, pageSize: 10, ...filter })
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.page < lastPage.totalPages - 1) {
        return lastPage.page + 1
      }
      return null
    },
    enabled: true,
    retry: false,
  })

  const mappedData = query.data?.pages.map((page) => page.content).flat()

  // The brands are already flattened from the paginated structure
  const flattenedBrands = mappedData || []

  return {
    ...query,
    brands: flattenedBrands,
    hasNextPage: query.hasNextPage,
    fetchNextPage: query.fetchNextPage,
    handleBrandSearch,
    setFilter,
  }
}

export default useFetchBrands
