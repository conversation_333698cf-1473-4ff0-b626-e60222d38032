import { useMutation, useQueryClient } from "@tanstack/react-query"
import { addressApi, CreateAddressData } from "@infrastructure/api/addressApi"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import useToast from "./useToast"

/**
 * Hook for creating a new address
 */
export const useCreateAddress = () => {
  const queryClient = useQueryClient()
  const { showToast } = useToast()

  return useMutation({
    mutationFn: (data: CreateAddressData) => addressApi.createAddress(data),
    onSuccess: (newAddress) => {
      // Invalidate and refetch address queries
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_MY_ADDRESS] })
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_ALL_ADDRESS] })
      
      // Show success message
      showToast({
        message: "Address created successfully!",
        type: "success",
      })

      // Optionally add the new address to the cache optimistically
      queryClient.setQueryData([QueryKeysConstant.GET_MY_ADDRESS], (oldData: any) => {
        if (oldData?.content) {
          return {
            ...oldData,
            content: [newAddress, ...oldData.content],
            totalSize: oldData.totalSize + 1,
          }
        }
        return oldData
      })
    },
    onError: (error: Error) => {
      showToast({
        message: error.message || "Failed to create address. Please try again.",
        type: "error",
      })
    },
  })
}

/**
 * Hook for updating an existing address
 */
export const useUpdateAddress = () => {
  const queryClient = useQueryClient()
  const { showToast } = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: CreateAddressData }) => 
      addressApi.update(id, data),
    onSuccess: (updatedAddress, variables) => {
      // Invalidate and refetch address queries
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_MY_ADDRESS] })
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_ALL_ADDRESS] })
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_ADDRESS_BY_ID, variables.id] })
      
      // Show success message
      showToast({
        message: "Address updated successfully!",
        type: "success",
      })

      // Update the specific address in cache
      queryClient.setQueryData([QueryKeysConstant.GET_ADDRESS_BY_ID, variables.id], updatedAddress)
    },
    onError: (error: Error) => {
      showToast({
        message: error.message || "Failed to update address. Please try again.",
        type: "error",
      })
    },
  })
}

/**
 * Hook for deleting an address
 */
export const useDeleteAddress = () => {
  const queryClient = useQueryClient()
  const { showToast } = useToast()

  return useMutation({
    mutationFn: (id: number) => addressApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch address queries
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_MY_ADDRESS] })
      queryClient.invalidateQueries({ queryKey: [QueryKeysConstant.GET_ALL_ADDRESS] })
      
      // Remove the deleted address from cache
      queryClient.setQueryData([QueryKeysConstant.GET_MY_ADDRESS], (oldData: any) => {
        if (oldData?.content) {
          return {
            ...oldData,
            content: oldData.content.filter((address: any) => address.id !== deletedId),
            totalSize: Math.max(0, oldData.totalSize - 1),
          }
        }
        return oldData
      })
      
      // Show success message
      showToast({
        message: "Address deleted successfully!",
        type: "success",
      })
    },
    onError: (error: Error) => {
      showToast({
        message: error.message || "Failed to delete address. Please try again.",
        type: "error",
      })
    },
  })
}

/**
 * Combined hook that provides all address mutations
 */
export const useAddressMutations = () => {
  const createAddress = useCreateAddress()
  const updateAddress = useUpdateAddress()
  const deleteAddress = useDeleteAddress()

  return {
    createAddress,
    updateAddress,
    deleteAddress,
    
    // Convenience flags
    isLoading: createAddress.isPending || updateAddress.isPending || deleteAddress.isPending,
    isCreating: createAddress.isPending,
    isUpdating: updateAddress.isPending,
    isDeleting: deleteAddress.isPending,
  }
}
