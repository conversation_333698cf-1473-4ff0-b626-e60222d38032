import { useQuery } from "@tanstack/react-query"
import { GetAllBankInfo } from "@application/usecases/getAllBankInfo"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { BankInfoApiRepository } from "@infrastructure/repositories/bankInfoApiRepository"
import { TBankInfoFilter } from "types/bankInfo.type"

const useGetAllBankInfo = (filter?: TBankInfoFilter) => {
  const fetchAllBankInfo = async (filter?: TBankInfoFilter) => {
    const r = new BankInfoApiRepository()
    const u = new GetAllBankInfo(r)
    const banks = await u.execute(filter)
    return banks
  }

  const filterValues = Object.entries(filter || {}).map(
    ([key, value]) => `${key}-${value}`,
  )

  return useQuery({
    queryKey: [QueryKeysConstant.GET_ALL_BANK_INFO, ...filterValues],
    queryFn: () => fetchAllBankInfo(filter),
    enabled: Boolean(filter),
  })
}

export default useGetAllBankInfo
