import { useEffect, useMemo } from "react"

import useChangePage from "./useChangePage"
import useAuthSession from "./useAuthSession"

interface AuthRedirectOptions {
  whenAuthed?: string
  whenUnauthed?: string
}

export const useAuthRedirect = (options: AuthRedirectOptions = {}) => {
  const { isAuthenticated, isLoading } = useAuthSession()
  const { goToPage, isLoading: isLoadingPage } = useChangePage()
  const loading = useMemo(() => {
    return (
      isLoading ||
      isLoadingPage[options.whenAuthed || options.whenUnauthed || ""]
    )
  }, [isLoading, isLoadingPage, options.whenAuthed, options.whenUnauthed])

  useEffect(
    () => {
      if (loading) return

      // Don't redirect if we're already on the target page
      const currentPath = window.location.pathname

      // For login page, only redirect if authenticated
      if (currentPath === "/login") {
        if (isAuthenticated && options.whenAuthed) {
          goToPage(options.whenAuthed)
        }
        return
      }

      // For other pages, handle both authenticated and unauthenticated cases
      if (
        isAuthenticated &&
        options.whenAuthed &&
        currentPath !== options.whenAuthed
      ) {
        goToPage(options.whenAuthed)
      } else if (
        !isAuthenticated &&
        options.whenUnauthed &&
        currentPath !== options.whenUnauthed
      ) {
        goToPage(options.whenUnauthed)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [loading, isAuthenticated, options.whenAuthed, options.whenUnauthed],
  )

  return { loading }
}
