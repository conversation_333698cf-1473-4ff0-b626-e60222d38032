import { useQuery } from "@tanstack/react-query"
import { ItemSummaryApiRepository } from "@infrastructure/repositories/itemSummaryApiRepository"

const useGetItemListingPriceSummary = (itemId: number) => {
  const ItemSummary = new ItemSummaryApiRepository()

  return useQuery({
    queryKey: ["ItemSummary", itemId],
    queryFn: () => ItemSummary.getByItemId(itemId),
    enabled: Boolean(itemId),
  })
}

export default useGetItemListingPriceSummary
