import { useMutation } from "@tanstack/react-query"
import { OfferApiRepository } from "@infrastructure/repositories/offerApiRepository"

const usePostSellNowMutation = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void
  onError?: (error: Error) => void
}) => {
  const submitSellNow = async (offerId: number) => {
    const repo = new OfferApiRepository()
    const res = await repo.sellNow(offerId)
    return res
  }

  return useMutation({
    mutationFn: submitSellNow,
    onSuccess,
    onError,
  })
}

export default usePostSellNowMutation
