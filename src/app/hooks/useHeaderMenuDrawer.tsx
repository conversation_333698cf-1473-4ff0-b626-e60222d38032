import {
  IconHomeBold,
  IconHomeOutline,
  IconProfileBold,
  IconProfileOutline,
  IconSearchBold,
  IconSearchOutline,
  IconTagBold,
  IconTagOutline,
} from "@kickavenue/ui/components/icons"
import { useState } from "react"
import { getMemberFullName } from "@utils/member.utils"
import { convertS3UrlToCloudFront } from "@utils/misc"
import TDrawerMenuProps from "@components/Header/DrawerMenu/DrawerMenu.type"
import { useMemberStore } from "stores/memberStore"
import useAuthSession from "./useAuthSession"

const useHeaderMenuDrawer = () => {
  const { isAuthenticated } = useAuthSession()
  const { member } = useMemberStore()
  const [showDrawer, setShowDrawer] = useState(false)

  const defaultProps: TDrawerMenuProps = {
    linkActive: "home",
    links: [
      {
        id: "/",
        label: "Home",
        iconBold: IconHomeBold,
        iconOutline: IconHomeOutline,
      },
      {
        id: "/search",
        label: "Market",
        iconBold: IconSearchBold,
        iconOutline: IconSearchOutline,
      },
      {
        id: "/sell-consignment",
        label: "Sell",
        iconBold: IconTagBold,
        iconOutline: IconTagOutline,
      },
      {
        id: "/profile",
        label: "Profile",
        iconBold: IconProfileBold,
        iconOutline: IconProfileOutline,
      },
    ],
    isLogin: isAuthenticated,
    name: getMemberFullName(member),
    email: member.email || "...",
    urlProfile: member?.image ? convertS3UrlToCloudFront(member.image) : "",
    toggleDrawerMenu: () => {},
  }

  return { defaultProps, showDrawer, setShowDrawer }
}

export default useHeaderMenuDrawer
