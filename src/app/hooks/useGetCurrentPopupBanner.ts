import { useQuery } from "@tanstack/react-query"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { PopupBannerApiRepository } from "@infrastructure/repositories/popupBannerApiRepository"
import { IPopupBannerParams } from "types/popupBanner.type"
import { ELocationPageType, EPlatformType } from "types/misc.type"

const useGetCurrentPopupBanner = () => {
  const fetchCurrentPopupBanner = async () => {
    const popupBannerRepository = new PopupBannerApiRepository()
    const params: Partial<IPopupBannerParams> = {
      sortBy: "updated_at,desc",
      platform: [EPlatformType.All, EPlatformType.Web],
      locationPage: ELocationPageType.HomePage,
    }
    const res = await popupBannerRepository.getCurrent(params)
    return res.data
  }

  return useQuery({
    queryKey: [QueryKeysConstant.GET_CURRENT_POPUP_BANNER],
    queryFn: fetchCurrentPopupBanner,
  })
}

export default useGetCurrentPopupBanner
