"use client"

import { useSession } from "next-auth/react"
import { TMemberAuthStatus } from "types/member.type"

const useAuthSession = () => {
  const { data: session, status } = useSession()

  return {
    sessionUser: session?.user,
    isAuthenticated: status === TMemberAuthStatus.Authenticated,
    isUnauthenticated: status === TMemberAuthStatus.Unauthenticated,
    isLoading: status === TMemberAuthStatus.Loading,
    isError: Boolean(session?.error),
  }
}

export default useAuthSession
