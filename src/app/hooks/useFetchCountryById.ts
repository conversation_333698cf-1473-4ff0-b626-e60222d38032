import { useQuery } from "@tanstack/react-query"

import { GetByIdCountry } from "@application/usecases/getByIdCountry"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { CountryApiRepository } from "@infrastructure/repositories/countryApiRepository"

const useFetchCountryById = (id: number | string) => {
  const fetchCountryById = async () => {
    const r = new CountryApiRepository()
    const u = new GetByIdCountry(r)
    const res = await u.execute(id)
    return res
  }

  return useQuery({
    queryKey: [QueryKeysConstant.GET_COUNTRY_BY_ID, id],
    queryFn: () => fetchCountryById(),
    enabled: Boolean(id),
    retry: false,
  })
}

export default useFetchCountryById
