import { useInfiniteQuery } from "@tanstack/react-query"
import { CategoryApiRepository } from "@infrastructure/repositories/categoryApiRepository"
import { GetAllCategoryWithPagination } from "@application/usecases/GetAllCategoryWithPagination"
import { ECategoryType } from "types/category.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

interface IUseFetchCategoriesProps {
  enabled?: boolean
  type?: ECategoryType
	parentID?: number
}

const useFetchCategories = ({
  enabled = true,
  type = ECategoryType.All,
	parentID,
}: IUseFetchCategoriesProps = {}) => {
  const r = new CategoryApiRepository()
  const u = new GetAllCategoryWithPagination(r)

  const query = useInfiniteQuery({
    queryKey: [QueryKeysConstant.GET_CATEGORIES, type, parentID],
    initialPageParam: 0,
    queryFn: ({ pageParam }) => {
      return u.execute({
        page: pageParam,
        pageSize: 10,
        type,
        parentID,
      })
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.page < lastPage.totalPages - 1) {
        return lastPage.page + 1
      }
      return null
    },
    enabled,
    retry: false,
  })

  const mappedData = query.data?.pages.map((page) => page.content).flat()

  return {
    categories: mappedData,
    ...query,
  }
}

export default useFetchCategories
