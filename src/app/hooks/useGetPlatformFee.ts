import { useQuery } from "@tanstack/react-query"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { PlatformFeeApiRepository } from "@infrastructure/repositories/platformFeeApiRepository"
import { useCallback } from "react"
import { TPlatformFee } from "types/fee.type"

interface IUseGetPlatformFee {
  categoryId?: number
}

const useGetPlatformFee = ({ categoryId }: IUseGetPlatformFee) => {
  const PlatformFee = new PlatformFeeApiRepository()
  const query = useQuery({
    queryKey: [QueryKeysConstant.GET_PLATFORM_FEE],
    queryFn: () => {
      return PlatformFee.getPlatformFee()
    },
  })

  const getMyPlatformFee: () => TPlatformFee | undefined = useCallback(() => {
    if (!query.data) return undefined
    // Since we now get a single platform fee object instead of an array,
    // we need to check if it matches the categoryId
    const platformFee = query.data.find(
      (fee) => fee.categoryId === categoryId,
    )
    return platformFee || undefined
  }, [query.data, categoryId])

  return {
    getMyPlatformFee,
    ...query,
  }
}

export default useGetPlatformFee
