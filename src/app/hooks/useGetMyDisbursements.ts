import { useQuery } from "@tanstack/react-query"
import { DisbursementApiRepository } from "@infrastructure/repositories/disbursementApiRepository"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { EBalanceType } from "types/balance.type"
import {
  EDisbursementActionNameType,
  EDisbursementStatusesType,
  EKickCreditDisbursementType,
  EKickPointDisbursementType,
  ESellerCreditDisbursementType,
  GetMyDisbursementParams,
} from "types/disbursement.type"

const { KickPendingTopUp, KickPendingCashOut } = EKickCreditDisbursementType
const { KickPointLog } = EKickPointDisbursementType
const { SellerPendingCashOut, SellerCreditLog } = ESellerCreditDisbursementType

const useGetMyDisbursement = (
  type:
    | EKickCreditDisbursementType
    | EKickPointDisbursementType
    | ESellerCreditDisbursementType,
  initialFilter: GetMyDisbursementParams = {
    page: 0,
    pageSize: 10,
    search: "",
  },
) => {
  const repository = new DisbursementApiRepository()

  const getBalanceType = () => {
    switch (type) {
      case SellerPendingCashOut:
      case SellerCreditLog:
        return EBalanceType.SellerCredit
      case KickPointLog:
        return EBalanceType.KickPoint
      default:
        return EBalanceType.KickCredit
    }
  }

  const getActionNames = () => {
    switch (type) {
      case KickPendingTopUp:
        return EDisbursementActionNameType.TopUp
      case KickPendingCashOut:
      case SellerPendingCashOut:
        return EDisbursementActionNameType.CashOut
      default:
        return undefined
    }
  }

  const getStatusFilter = () => {
    switch (type) {
      case KickPendingTopUp:
      case KickPendingCashOut:
      case SellerPendingCashOut:
        return [
          EDisbursementStatusesType.Pending,
          EDisbursementStatusesType.InProgress,
        ]
      default:
        return undefined
    }
  }

  return useQuery({
    queryKey: [
      QueryKeysConstant.GET_MY_DISBURSEMENT,
      type,
      initialFilter,
      initialFilter.page,
    ],
    queryFn: () =>
      repository.getMyDisbursements({
        page: initialFilter.page || 0,
        pageSize: initialFilter.pageSize || 10,
        balanceType: getBalanceType(),
        ...(getActionNames() && { actionNames: getActionNames() }),
        ...(getStatusFilter() && { statuses: getStatusFilter() }),
        search: initialFilter.search || "",
        sortBy: "created_at,desc",
      }),
    enabled: Boolean(type),
  })
}

export default useGetMyDisbursement
