import { useRef, useCallback, useEffect } from "react"

const useSetParentStyle = (selector: string, styles: React.CSSProperties) => {
  const originalStylesRef = useRef<Record<string, string>>({})
  const targetElementRef = useRef<HTMLElement | null>(null)
  const componentRef = useRef<HTMLElement | null>(null)

  // Find the target parent element (nearest td)
  const findTargetElement = useCallback((): HTMLElement | null => {
    if (!targetElementRef.current && componentRef.current) {
      // Use closest() to find the nearest parent matching the selector
      targetElementRef.current = componentRef.current.closest(selector)
    }
    return targetElementRef.current
  }, [selector])

  // Apply styles to the target element
  const apply = useCallback(() => {
    const targetElement = findTargetElement()

    if (targetElement && styles) {
      // Store original styles before applying new ones
      Object.keys(styles).forEach((property) => {
        const styleProperty = property as keyof CSSStyleDeclaration
        if (!(property in originalStylesRef.current)) {
          originalStylesRef.current[property] =
            (targetElement.style[styleProperty] as string) || ""
        }
        ;(targetElement.style[styleProperty] as string) = String(
          styles[property as keyof React.CSSProperties],
        )
      })
    }
  }, [findTargetElement, styles])

  // Revert styles to original values
  const revert = useCallback(() => {
    const targetElement = findTargetElement()

    if (targetElement) {
      Object.keys(originalStylesRef.current).forEach((property) => {
        const styleProperty = property as keyof CSSStyleDeclaration
        ;(targetElement.style[styleProperty] as string) =
          originalStylesRef.current[property]
      })
      originalStylesRef.current = {}
      // Reset target reference so it can be found again if needed
      targetElementRef.current = null
    }
  }, [findTargetElement])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      revert()
    }
  }, [revert])

  return {
    ref: componentRef,
    apply,
    revert,
  }
}

export default useSetParentStyle
