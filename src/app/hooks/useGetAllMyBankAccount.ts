import { useQuery } from "@tanstack/react-query"
import { MiscConstant } from "@constants/misc"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { BankApiRepository } from "@infrastructure/repositories/bankApiRepository"

const { PAGING_DEFAULT } = MiscConstant

const FILTER_FETCH_ALL_MY_BANK = {
  page: PAGING_DEFAULT.PAGE,
  pageSize: PAGING_DEFAULT.MAX_PAGE_SIZE,
  sortBy: [
    {
      sortBy: "updated_at",
      sortOrder: "DESC",
    },
  ],
}

const useGetAllMyBankAccount = () => {
  const fetchMyBankAccount = async () => {
    const r = new BankApiRepository()
    const resp = await r.getByMy(FILTER_FETCH_ALL_MY_BANK)
    return resp
  }

  const result = useQuery({
    queryKey: [QueryKeysConstant.GET_ALL_MY_BANK_ACCOUNT],
    queryFn: fetchMyBankAccount,
  })

  return {
    ...result,
    bankData: result.data?.content || [],
  }
}

export default useGetAllMyBankAccount
