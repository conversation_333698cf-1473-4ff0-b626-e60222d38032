import { useQuery } from "@tanstack/react-query"
import { GetByIdSellerListing } from "@application/usecases/getByIdSellerListing"
import { SellerListingApiRepository } from "@infrastructure/repositories/sellerListingApiRepository"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { QueryStatus } from "types/network.type"

const useFetchSellerListingById = (id?: number) => {
  const getSellerListingById = async () => {
    const r = new SellerListingApiRepository()
    const u = new GetByIdSellerListing(r)
    const res = await u.execute(id as number)
    return res
  }

  const { data, isFetching, status } = useQuery<any>({
    queryKey: [QueryKeysConstant.GET_SELLER_LISTING_BY_ID, id],
    queryFn: getSellerListingById,
    enabled: Boolean(id),
    retry: false,
    staleTime: 1,
  })

  const isLoading = status === QueryStatus.Pending || isFetching

  return {
    data,
    isLoading,
  }
}

export default useFetchSellerListingById
