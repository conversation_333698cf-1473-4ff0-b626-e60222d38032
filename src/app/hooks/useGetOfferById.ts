import { TOffer } from "types/offer.type"
import { OfferApiRepository } from "@infrastructure/repositories/offerApiRepository"
import { GetByIdOffer } from "@application/usecases/getByIdOffer"
import { QueryKeysConstant } from "@constants/queryKeys.constant"

import useQuery from "./useQuery"
import { getItemSummaryByItemId } from "@utils/productDetail"

const useGetOfferById = ({
  id,
  enabled = true,
  onSuccess,
}: {
  id?: number
  enabled?: boolean
  onSuccess?: (offer: TOffer) => void
}) => {
  const getOfferById = async () => {
    const r = new OfferApiRepository()
    const u = new GetByIdOffer(r)
    const offer = await u.execute(id!)

    const summary = await getItemSummaryByItemId(offer?.item?.id as number)

    const finalData = {
      ...offer,
      item: {
        ...offer.item,
        itemSummary: summary.data,
      },
    }

    onSuccess?.(finalData as TOffer)
    return finalData as TOffer
  }

  return useQuery({
    queryKey: [QueryKeysConstant.GET_OFFER_BY_ID, id],
    enabled: Boolean(id) && enabled,
    queryFn: getOfferById,
  })
}

export default useGetOfferById
