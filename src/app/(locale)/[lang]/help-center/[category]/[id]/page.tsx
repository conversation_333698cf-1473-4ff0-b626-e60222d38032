import HelpCenterContent from "@components/HelpCenter/HelpCenterContent"

export const metadata = {
  title: "Help Center - Content",
}

interface HelpCenterCategoryContentPageProps {
  params: Promise<{
    category: string
    id: string
  }>
}

const HelpCenterCategoryContentPage = async (
  props: HelpCenterCategoryContentPageProps,
) => {
  const params = await props.params

  const { category, id } = params

  return <HelpCenterContent category={category} faqId={id} />
}

export default HelpCenterCategoryContentPage
