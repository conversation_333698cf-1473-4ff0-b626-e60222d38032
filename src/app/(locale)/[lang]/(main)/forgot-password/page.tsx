"use client"

import {
  Head<PERSON>,
  Button,
  Label,
  Input,
  IconSuccessCircleBulk,
  IconDangerCircleBulk,
} from "@kickavenue/ui/components"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMemo } from "react"
import { usePasswordReset } from "@app/components/ForgotPassword/hooks/usePasswordReset"

const emailSchema = z.object({
  email: z.string().email("Enter a valid email address"),
})

const ForgotPassword = () => {
  const { requestPasswordReset, isLoading } = usePasswordReset()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<{ email: string }>({
    resolver: zodResolver(emailSchema),
    defaultValues: { email: "" },
    mode: "onChange",
  })

  const email = watch("email")

  const onSubmit = async (data: { email: string }) => {
    await requestPasswordReset(data.email)
  }

  const { emailInputIcon, emailInputVariant, emailInputHelperText } =
    useMemo(() => {
      if (email === "") {
        return {
          emailInputIcon: undefined,
          emailInputVariant: undefined,
          emailInputHelperText: undefined,
        }
      } else if (errors.email) {
        return {
          emailInputIcon: <IconDangerCircleBulk />,
          emailInputVariant: "danger" as const,
          emailInputHelperText: errors.email.message,
        }
      } else {
        return {
          emailInputIcon: <IconSuccessCircleBulk />,
          emailInputVariant: "success" as const,
          emailInputHelperText: undefined,
        }
      }
    }, [email, errors.email])

  return (
    <div className="mx-auto mb-80 mt-8 max-w-md">
      <div className="mb-8 text-center">
        <Heading heading="4" textStyle="bold">
          Forgot Password
        </Heading>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Label state="required" type="default" size="sm">
          Email
        </Label>
        <Input
          {...register("email")}
          type="text"
          className="mt-2"
          rightIcon={emailInputIcon}
          placeholder="Email"
          value={email}
          helperText={emailInputHelperText}
          variant={emailInputVariant}
          autoComplete="email"
        />
        <div className="mt-8 w-full">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            type="submit"
            disabled={
              isSubmitting || Boolean(errors.email) || isLoading || email === ""
            }
          >
            Send Reset Link
          </Button>
        </div>
      </form>
    </div>
  )
}

export default ForgotPassword
