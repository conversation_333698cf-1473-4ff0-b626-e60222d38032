"use client"

import React from "react"
import {
  But<PERSON>,
  Heading,
  IconDangerCircleBulk,
  Text,
} from "@kickavenue/ui/dist/src/components"
import { useRouter } from "next/navigation"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"

export default function ErrorPage() {
  const router = useRouter()

  const handleTryAgain = () => {
    router.push("/sell-consignment")
  }

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="flex h-svh flex-col items-center justify-center gap-lg bg-white py-xxl">
        <IconDangerCircleBulk className="size-[29px] scale-[2.2] text-danger" />
        <Heading heading="4" textStyle="bold">
          Oops, Something Went Wrong
        </Heading>
        <Text size="base" state="secondary" type="regular">
          We&apos;re sorry, but we encountered an error while processing your
          request. Please try again.
        </Text>
        <Button onClick={handleTryAgain}>Try Again</Button>
      </div>
    </AuthRedirectWrapper>
  )
}
