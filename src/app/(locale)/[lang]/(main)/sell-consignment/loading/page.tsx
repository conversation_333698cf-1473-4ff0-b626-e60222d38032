"use client"

import React, { useEffect } from "react"
import { useMutationState } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import Loading from "@components/shared/Loading"
import { useSellConsignmentStore } from "stores/sellConsignmentStore"

export default function AwaitingPaymentPage() {
  const router = useRouter()
  const { currentStep } = useSellConsignmentStore()
  const data = useMutationState({
    filters: { mutationKey: ["addSellerListing"] },
    select: (mutation) => mutation.state.status,
  })

  useEffect(() => {
    // If there's no current step or mutation data, redirect to start
    // This handles the case when user navigates directly to loading page
    // or comes back from success page
    if (!currentStep || !data.length) {
      router.replace("/sell-consignment")
      return
    }

    if (data[0] === "success") {
      // Replace current history state so back button skips the loading page
      window.history.replaceState(null, "", "/sell-consignment")
      router.push("/sell-consignment/success")
    } else if (data[0] === "error") {
      router.push("/sell-consignment/error")
    }
  }, [data, router, currentStep])

  return <Loading />
}
