import React from "react"
import { Text, Tooltip, IconInfoCircleBold } from "@kickavenue/ui/components"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { formatPrice, getStripAmount } from "@utils/misc"
import { TTransactionResponse } from "types/transaction.type"
import useGetTransactionDetail from "@app/hooks/useGetTransactionDetail"
import Spinner from "@components/shared/Spinner"
import { MiscConstant } from "@constants/misc"

const { PAYMENT_SUMMARY_PAYMENT } = ModalConstant.MODAL_IDS
const { PAGE } = MiscConstant.PAGING_DEFAULT

interface PaymentSummaryItemProps {
  label: string
  amount: number
  type?: "addition" | "deduction"
}

const PaymentSummaryItem: React.FC<PaymentSummaryItemProps> = ({
  label,
  amount,
  type = "addition",
}) => {
  const isDeduction = type === "deduction"
  const formattedAmount = `${isDeduction ? "-" : ""}${formatPrice(amount || 0, null, "IDR")}`

  const getStatus = () => {
    switch (type) {
      case "addition":
        return "primary"
      case "deduction":
        return "success"
      default:
        return "primary"
    }
  }

  const labelTooltip = label === "Processing Fee"
  const textLabel =
    "Ensuring your order arrives safely, covering bubble wrap for weather protection, double-boxing for shipment security, and insurance for added peace of mind."

  if (amount === 0) return null

  const renderAmount = () => {
    return (
      <Text size="sm" type="regular" state={getStatus()}>
        {formattedAmount}
      </Text>
    )
  }

  return (
    <div className="flex justify-between py-2">
      <Text
        size="sm"
        type="regular"
        state="primary"
        className="flex items-center"
      >
        {label}{" "}
        {labelTooltip && (
          <Tooltip
            direction="bottom"
            text={textLabel}
            className="ml-2"
            classNameContent="!w-[240px] !text-left"
          >
            <IconInfoCircleBold className="!h-4 !w-4" />
          </Tooltip>
        )}
      </Text>
      {renderAmount()}
    </div>
  )
}

interface PaymentSummaryTransaction {
  transactionData?: TTransactionResponse
}

export default function PaymentSummaryTransaction({
  transactionData,
}: PaymentSummaryTransaction) {
  const { setOpen, modalId, open } = useModalStore()
  const { data: transactionDetail, isLoading } = useGetTransactionDetail({
    filter: {
      page: PAGE,
      pageSize: 100,
      sort: [],
      transactionId: Number(transactionData?.id),
    },
    enable: true,
  })

  const isOpen = open && modalId === PAYMENT_SUMMARY_PAYMENT

  const handleClose = () => {
    setOpen(false, PAYMENT_SUMMARY_PAYMENT)
  }

  if (!isOpen) return null

  if (isLoading) {
    {
      return (
        <div className="flex min-h-screen flex-col items-center justify-center bg-gray-w-95">
          <Spinner />
        </div>
      )
    }

    return (
      <Modal modalId={PAYMENT_SUMMARY_PAYMENT} onClose={handleClose}>
        <HeaderModal title="Payment Summary" onClose={handleClose} />
        <div className="w-full bg-white p-6 pb-[88px]">
          <div className="rounded-t-lg border border-gray-w-80 px-3">
            <PaymentSummaryItem
              label="Product Price"
              amount={transactionDetail?.content[0].price.minUnitVal || 0}
            />
            <PaymentSummaryItem
              label="Processing Fee"
              amount={getStripAmount(transactionData?.totalProcessingFee)}
            />
            <PaymentSummaryItem
              label="Shipping Fee"
              amount={getStripAmount(transactionData?.totalShippingFee)}
            />
            <PaymentSummaryItem
              label="Voucher"
              amount={getStripAmount(transactionData?.voucherAmount)}
              type="deduction"
            />
            <PaymentSummaryItem
              label="Seller Credit Usage"
              amount={transactionData?.sellerCreditAmount || 0}
              type="deduction"
            />

            <PaymentSummaryItem
              label="Kick Credit Usage"
              amount={transactionData?.kickCreditAmount || 0}
              type="deduction"
            />
            <PaymentSummaryItem
              label="Kick Points Usage"
              amount={transactionData?.kickPointAmount || 0}
              type="deduction"
            />
          </div>
          <div className="rounded-b-lg border border-gray-w-80 p-3">
            <div className="flex justify-between">
              <Text size="base" type="bold" state="primary">
                Total Payment
              </Text>
              <Text size="base" type="bold" state="primary">
                {formatPrice(
                  transactionData?.totalAmount?.minUnitVal || 0,
                  null,
                  "IDR",
                )}
              </Text>
            </div>
          </div>
        </div>
      </Modal>
    )
  }
}
