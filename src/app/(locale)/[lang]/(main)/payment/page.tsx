import React from "react"
import Script from "next/script"
import { Metada<PERSON> } from "next"
import Payment from "@components/Payment/Payment"

export const metadata: Metadata = {
  title: "Select Payment",
}

export default function PaymentPage() {
  return (
    <>
      <Script
        src="https://api.midtrans.com/v2/assets/js/midtrans-new-3ds.min.js"
        id="midtrans-script"
        type="text/javascript"
        data-environment={process.env.NEXT_PUBLIC_MIDTRANS_ENVIRONMENT}
        data-client-key={process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY}
      />
      <Payment />
    </>
  )
}
