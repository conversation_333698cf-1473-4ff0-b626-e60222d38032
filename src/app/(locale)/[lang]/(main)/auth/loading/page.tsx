"use client"

import { useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"

export default function AuthLoadingPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const code = searchParams?.get("code")

    if (!code) {
      router.push("/login?error=no_code")
      return
    }

    // Redirect to the gateway callback URL using environment variable
    const gatewayUrl =
      process.env.NEXT_PUBLIC_GATEWAY_BASE_URL ||
      "https://revampstaging.kickavenue.com"
    window.location.href = `${gatewayUrl}/api/v1/auth/callback?code=${code}`
  }, [router, searchParams])

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="border-primary size-8 animate-spin rounded-full border-4 border-t-transparent" />
        <p className="text-gray-600">Authenticating your account...</p>
      </div>
    </div>
  )
}
