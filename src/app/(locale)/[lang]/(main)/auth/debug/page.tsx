"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { signIn } from "next-auth/react"

export default function AuthDebugPage() {
  const searchParams = useSearchParams()
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    const info = {
      code: searchParams?.get("code"),
      state: searchParams?.get("state"),
      error: searchParams?.get("error"),
      errorDescription: searchParams?.get("error_description"),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }
    setDebugInfo(info)
  }, [searchParams])

  const handleBackToLogin = () => {
    window.location.href = "/login"
  }

  const handleGoogleSignIn = () => {
    // Use NextAuth's signIn function with cognito provider
    signIn("cognito", {
      callbackUrl: "/auth/debug?debug=true",
    })
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <h1 className="text-2xl mb-6 font-bold">OAuth Debug Information</h1>
        <div className="rounded-lg border p-4">
          <pre className="whitespace-pre-wrap text-sm">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
        <div className="mt-4 flex gap-2">
          <button
            type="button"
            onClick={handleBackToLogin}
            className="bg-blue-500 hover:bg-blue-600 rounded px-4 py-2 text-black"
          >
            Back to Login
          </button>
          <button
            type="button"
            onClick={handleGoogleSignIn}
            className="bg-green-500 hover:bg-green-600 rounded px-4 py-2 text-black"
          >
            Try Google Sign-In Again
          </button>
        </div>
      </div>
    </div>
  )
}
