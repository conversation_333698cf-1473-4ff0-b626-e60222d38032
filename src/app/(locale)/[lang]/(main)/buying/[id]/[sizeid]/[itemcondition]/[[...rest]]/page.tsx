import { Metadata } from "next"
import { prepareProductDetailData } from "@utils/productDetail"
import BuyingContainer from "@components/Buying/BuyingContainer"
import { TItemCondition } from "types/listing.type"

export interface BuyingProps {
  params: Promise<{
    id: string
    sizeid: string
    itemcondition: string
    rest: string[]
  }>
}

export async function generateMetadata(props: BuyingProps): Promise<Metadata> {
  const params = await props.params
  const productId = parseInt(params.id, 10)
  const { product } = await prepareProductDetailData(productId)

  return {
    title: product?.name || "product",
  }
}

export default async function BuyingPage(props: BuyingProps) {
  const params = await props.params
  const productId = parseInt(params.id, 10)
  const sizeId = parseInt(params.sizeid, 10)
  const restSegments = params?.rest || []
  const { product } = await prepareProductDetailData(productId)
  return (
    <BuyingContainer
      product={product}
      sizeId={sizeId}
      productType={params?.itemcondition as TItemCondition}
      restUrlSegments={restSegments}
    />
  )
}
