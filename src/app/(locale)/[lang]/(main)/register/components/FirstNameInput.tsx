import React, { useState, useEffect } from "react"
import {
  IconDangerCircleBulk,
  IconSuccessCircleBulk,
  Input,
  Label,
} from "@kickavenue/ui/components"
import { z } from "zod"
import { FirstNameInputProps, InputVariant } from "@domain/entities/Input"

const firstNameSchema = z
  .string()
  .min(2, "First name must be at least 2 characters")

interface FirstNameInput extends FirstNameInputProps {
  value: string
}

const FirstNameInput: React.FC<FirstNameInput> = ({
  onChange,
  value,
  className = "",
}) => {
  const [firstNameError, setFirstNameError] = useState<string | undefined>(
    undefined,
  )
  const [firstNameVariant, setFirstNameVariant] =
    useState<InputVariant["variant"]>(undefined)

  const validateFirstName = (value: string) => {
    if (!value) {
      setFirstNameError(undefined)
      setFirstNameVariant(undefined)
      return false
    }
    try {
      firstNameSchema.parse(value)
      setFirstNameError(undefined)
      setFirstNameVariant("success")
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        setFirstNameError(error.errors[0].message)
        setFirstNameVariant("danger")
      }
      return false
    }
  }

  useEffect(() => {
    validateFirstName(value)
  }, [value])

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFirstName = e.target.value
    const isValid = validateFirstName(newFirstName)
    onChange(newFirstName, isValid)
  }

  const firstNameRightIcon = (() => {
    switch (firstNameVariant) {
      case "danger":
        return <IconDangerCircleBulk />
      case "success":
        return <IconSuccessCircleBulk />
      default:
        return undefined
    }
  })()

  return (
    <div className={className}>
      <Label state="required" type="default" size="sm">
        First Name
      </Label>
      <Input
        type="text"
        className="mt-2"
        rightIcon={firstNameRightIcon}
        placeholder="First Name"
        value={value}
        onChange={handleFirstNameChange}
        helperText={firstNameError}
        variant={firstNameVariant}
      />
    </div>
  )
}

export default FirstNameInput
