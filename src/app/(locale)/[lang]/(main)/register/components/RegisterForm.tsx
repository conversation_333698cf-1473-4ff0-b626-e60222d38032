import React, { type JSX } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@kickavenue/ui/components"
import { useTranslations } from "@app/i18n"

import { useRegisterForm } from "../hooks/useRegisterForm"

import { EmailInput } from "./EmailInput"
import { CountryInput } from "./CountryInput"
import PhoneNumberInput from "./PhoneNumberInput"
import PasswordInput from "./PasswordInput"
import TermsCheckbox from "./TermsCheckbox"
import FirstNameInput from "./FirstNameInput"
import LastNameInput from "./LastNameInput"

export default function RegisterForm(): JSX.Element {
  const t = useTranslations()
  const {
    error,
    isFormValid,
    registrationData,
    handleInputChange,
    handleRegister,
  } = useRegisterForm()

  return (
    <form onSubmit={handleRegister}>
      {error && (
        <Alert
          isIcon
          subTitle={error}
          variant="danger"
          className="!mt-6 !w-full"
        />
      )}
      <FirstNameInput
        onChange={handleInputChange("firstName")}
        value={registrationData.firstName}
        className="mb-4 mt-8"
      />
      <LastNameInput
        onChange={handleInputChange("lastName")}
        value={registrationData.lastName}
        className="mb-4"
      />
      <EmailInput />
      <CountryInput />
      <PhoneNumberInput />
      <PasswordInput />
      <TermsCheckbox />
      <div className="mt-6">
        <Button
          type="submit"
          disabled={!isFormValid}
          size="lg"
          className="!w-full"
        >
          {t("register")}
        </Button>
      </div>
    </form>
  )
}
