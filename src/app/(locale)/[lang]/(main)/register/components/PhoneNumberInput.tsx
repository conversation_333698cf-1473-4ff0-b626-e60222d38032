import { ComboBox, Input, Label } from "@kickavenue/ui/components"
import type { TItemOption } from "@kickavenue/ui/components/ComboBox/ComboBox.type"
import Image from "next/image"
import React, { useCallback, useMemo, useState, useEffect } from "react"
import { useCountries } from "@application/hooks/useCountries"
import { useRegistrationStore } from "@stores/registrationStore"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { TCountry } from "types/country.type"

const prettierCountryFlagItem = (c: TCountry) => {
  return {
    label: c.prefix,
    value: String(c.country),
    icon: c.flag ? (
      <Image
        height={16}
        width={22}
        src={convertS3UrlToCloudFront(c.flag)}
        alt={c.name || ""}
      />
    ) : null,
  } as TItemOption
}

const PhoneNumberInput: React.FC = () => {
  const { data: countries } = useCountries()
  const { registrationData, setRegistrationData } = useRegistrationStore()
  const [localPhoneNumber, setLocalPhoneNumber] = useState("")

  const comboBoxItems =
    (countries?.map((c: TCountry) =>
      prettierCountryFlagItem(c),
    ) as TItemOption[]) || []

  const selectedCountry = useMemo(
    () => countries?.find((c) => c.country === registrationData.phoneCountryId),
    [countries, registrationData.phoneCountryId],
  )

  const comboBoxSelected = selectedCountry
    ? prettierCountryFlagItem(selectedCountry)
    : null

  const phonePrefix = selectedCountry?.prefix || ""

  const formatPhoneNumber = useCallback((prefix: string, number: string) => {
    const trimmedNumber = number.replace(/^0+/, "")
    return `${prefix}${trimmedNumber}`
  }, [])

  // Set default phone number format when Indonesia is selected and no phone number is set
  useEffect(() => {
    if (
      selectedCountry &&
      selectedCountry.prefix === "+62" &&
      !registrationData.phoneNumber
    ) {
      setRegistrationData({ phoneNumber: "+62" })
    }
  }, [selectedCountry, registrationData.phoneNumber, setRegistrationData])

  const handlePhoneNumberChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value.replace(/\D/g, "")
      setLocalPhoneNumber(value)

      const formattedNumber = formatPhoneNumber(phonePrefix, value)
      setRegistrationData({ phoneNumber: formattedNumber })
    },
    [phonePrefix, formatPhoneNumber, setRegistrationData],
  )

  const displayValue = useMemo(() => {
    return localPhoneNumber.replace(/^0+/, "")
  }, [localPhoneNumber])

  const handleComboBoxChange = useCallback(
    (val: TItemOption | null) => {
      const countryId = val ? parseInt(val.value, 10) : 0
      setRegistrationData({ phoneCountryId: countryId })

      const country = countries?.find((c) => c.country === countryId)
      const formattedNumber = formatPhoneNumber(
        country?.prefix || "",
        localPhoneNumber,
      )
      setRegistrationData({ phoneNumber: formattedNumber })
    },
    [countries, localPhoneNumber, formatPhoneNumber, setRegistrationData],
  )

  return (
    <div className="mt-4 flex flex-col gap-y-xs">
      <Label state="required" size="sm" type="default">
        Mobile Number
      </Label>
      <div className="flex gap-xs">
        <ComboBox
          items={comboBoxItems}
          selected={comboBoxSelected}
          iconLeading={comboBoxSelected?.icon}
          setSelected={handleComboBoxChange}
          className="!max-w-28"
          placeholder="+62"
        />
        <div className="w-full">
          <Input
            placeholder="Mobile Number"
            type="text"
            value={displayValue}
            onChange={handlePhoneNumberChange}
          />
        </div>
      </div>
    </div>
  )
}

export default PhoneNumberInput
