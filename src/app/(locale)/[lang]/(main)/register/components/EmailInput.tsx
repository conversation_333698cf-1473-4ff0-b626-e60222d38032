import React, { type JSX } from "react"
import { InputEmail } from "@app/components"
import { useRegistrationStore } from "@stores/registrationStore"

export function EmailInput(): JSX.Element {
  const { registrationData, setRegistrationData } = useRegistrationStore()

  const handleEmailChange = (newEmail: string) => {
    setRegistrationData({ email: newEmail })
  }

  return (
    <InputEmail
      value={registrationData.email}
      onChange={handleEmailChange}
      className="mt-4"
    />
  )
}
