"use client"
import { Heading } from "@kickavenue/ui/components"
import { AuthSwitchPrompt } from "@app/components"
import { useTranslations } from "@app/i18n"
import Spinner from "@components/shared/Spinner/Spinner"
import { useLoadingStore } from "@stores/registrationStore"
import type { JSX } from 'react'
import RegisterForm from './RegisterForm'

export default function Register(): JSX.Element {
  const t = useTranslations()
  const { isLoading } = useLoadingStore()

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Spinner />
      </div>
    )
  }

  return (
    <div className="mx-auto mb-32 mt-8 max-w-[400px] px-6 md:px-0">
      <div className="text-center">
        <Heading heading="4" textStyle="bold">
          {t("register")}
        </Heading>
      </div>
      <RegisterForm />
      <AuthSwitchPrompt
        promptText="Already have an account?"
        linkText={t("login")}
        linkHref="/login"
      />
    </div>
  )
}
