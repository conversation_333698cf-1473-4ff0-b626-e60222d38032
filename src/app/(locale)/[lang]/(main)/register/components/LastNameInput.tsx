import React, { useState, useEffect } from "react"
import {
  IconDangerCircleBulk,
  IconSuccessCircleBulk,
  Input,
  Label,
} from "@kickavenue/ui/components"
import { z } from "zod"
import { LastNameInputProps, InputVariant } from "@domain/entities/Input"

const lastNameSchema = z
  .string()
  .min(2, "Last name must be at least 2 characters")

interface LastNameInput extends LastNameInputProps {
  value: string
}

const LastNameInput: React.FC<LastNameInput> = ({
  onChange,
  value,
  className = "",
}) => {
  const [lastNameError, setLastNameError] = useState<string | undefined>(
    undefined,
  )
  const [lastNameVariant, setLastNameVariant] =
    useState<InputVariant["variant"]>(undefined)

  const validateLastName = (value: string) => {
    if (!value) {
      setLastNameError(undefined)
      setLastNameVariant(undefined)
      return false
    }
    try {
      lastNameSchema.parse(value)
      setLastNameError(undefined)
      setLastNameVariant("success")
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        setLastNameError(error.errors[0].message)
        setLastNameVariant("danger")
      }
      return false
    }
  }

  useEffect(() => {
    validateLastName(value)
  }, [value])

  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newLastName = e.target.value
    onChange?.(newLastName, validateLastName(newLastName))
  }

  const lastNameRightIcon = (() => {
    switch (lastNameVariant) {
      case "danger":
        return <IconDangerCircleBulk />
      case "success":
        return <IconSuccessCircleBulk />
      default:
        return undefined
    }
  })()

  return (
    <div className={className}>
      <Label state="required" type="default" size="sm">
        Last Name
      </Label>
      <Input
        type="text"
        className="mt-2"
        rightIcon={lastNameRightIcon}
        placeholder="Last Name"
        value={value}
        onChange={handleLastNameChange}
        helperText={lastNameError}
        variant={lastNameVariant}
      />
    </div>
  )
}

export default LastNameInput
