import { useRouter } from "next/navigation"
import { useCallback, useMemo, useState } from "react"
import { AuthService } from "@application/services/authService"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { event } from "@lib/gtag"
import { useRegistrationStore } from "@stores/registrationStore"

import { useRegisterFormValidation } from "./useRegisterFormValidation"

// Constants
const REGISTRATION_ERRORS = {
  PASSWORD_MISMATCH: "Passwords do not match",
} as const

export const useRegisterForm = () => {
  const router = useRouter()
  const { registrationData, setRegistrationData } = useRegistrationStore()

  const [error, setError] = useState("")
  const { isFormValid } = useRegisterFormValidation(registrationData)

  // Memoize auth service instance to prevent recreation on every render
  const authService = useMemo(() => new AuthService(), [])

  // Clear error when user starts typing
  const clearError = useCallback(() => {
    if (error) {
      setError("")
    }
  }, [error])

  // Scroll to top helper function
  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }, [])

  // Handle input changes with error clearing
  const handleInputChange = useCallback(
    (field: string) => (value: string) => {
      setRegistrationData({ [field]: value })
      clearError()
    },
    [setRegistrationData, clearError],
  )

  // Validate password match
  const validatePasswordMatch = useCallback(() => {
    if (registrationData.password !== registrationData.passwordConfirmation) {
      setError(REGISTRATION_ERRORS.PASSWORD_MISMATCH)
      scrollToTop()
      return false
    }
    return true
  }, [
    registrationData.password,
    registrationData.passwordConfirmation,
    scrollToTop,
  ])

  // Handle registration success
  const handleRegistrationSuccess = useCallback(() => {
    const updatedData = {
      ...registrationData,
      registrationTimestamp: new Date().toISOString(),
      otpRequestTimestamp: new Date().toISOString(),
      otpVerified: false,
      createdSource: "WEB",
    }

    // Track registration event
    event({
      action: "user_registered",
      params: {
        email: updatedData.email,
      },
      userId: String(updatedData.id || ""),
    })

    setRegistrationData(updatedData)
    router.push(PageRouteConstant.VERIFICATION_CODE)
  }, [registrationData, setRegistrationData, router])

  // Main registration handler
  const handleRegister = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()

      // Early return for password validation
      if (!validatePasswordMatch()) {
        return
      }

      // Call AuthService register method
      const result = await authService.register(registrationData)

      // Handle registration result
      if (!result.success) {
        setError(result.error || "Registration failed")
        scrollToTop()
        return
      }

      // Handle successful registration
      handleRegistrationSuccess()
    },
    [
      validatePasswordMatch,
      authService,
      registrationData,
      handleRegistrationSuccess,
      scrollToTop,
    ],
  )

  return {
    error,
    isFormValid,
    registrationData,
    handleInputChange,
    handleRegister,
  }
}
