import { useState, useEffect } from "react"
import { RegistrationData } from "@stores/registrationStore"

export const useRegisterFormValidation = (
  registrationData: RegistrationData,
) => {
  const [isFormValid, setIsFormValid] = useState(false)

  useEffect(() => {
    const hasPhoneNumberDigits = /\d{8,}/.test(
      registrationData.phoneNumber.replace(/[+\s]/g, ""),
    )
    const isAllFieldsFilled =
      registrationData.countryId !== 0 &&
      registrationData.email !== "" &&
      registrationData.password !== "" &&
      registrationData.passwordConfirmation !== "" &&
      hasPhoneNumberDigits &&
      registrationData.firstName !== "" &&
      registrationData.lastName !== ""

    const doPasswordsMatch =
      registrationData.password === registrationData.passwordConfirmation

    setIsFormValid(
      isAllFieldsFilled && doPasswordsMatch && registrationData.isTermsChecked,
    )
  }, [registrationData])

  return { isFormValid }
}
