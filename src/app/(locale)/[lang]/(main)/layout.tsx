import { notFound } from "next/navigation"
import Footer from "@app/components/Footer"
import Header from "@app/components/Header"
import ExpandedSearchUnified from "@components/ExpandedSearch/ExpandedSearchUnified"
import { defaultLocale, locales } from "@config/i18n"

interface Props {
  children: React.ReactNode
  params: Promise<{ lang: string }>
}

export default async function LocaleLayout(props: Props) {
  const params = await props.params

  const { lang } = params

  const { children } = props

  if (!locales.includes(lang ?? defaultLocale)) notFound()

  return (
    <>
      <div className="relative md:sticky md:top-0 md:z-30">
        <Header />
        <ExpandedSearchUnified />
      </div>
      <main>{children}</main>
      <Footer />
    </>
  )
}
