import { FilterItem, SizeOption } from "@app/components/Filter/types"
import { TCountry } from "types/country.type"

export const categories: FilterItem[] = [
  { id: 1, name: "Footwear" },
  { id: 2, name: "Apparel" },
  { id: 3, name: "Accessories" },
]

export const subcategories: FilterItem[] = [
  { id: 1, name: "Sneakers" },
  { id: 2, name: "T-Shirts" },
  { id: 3, name: "Hats" },
  { id: 4, name: "Bags" },
]

export const brands: FilterItem[] = [
  { id: 1, name: "Nike" },
  { id: 2, name: "Adidas" },
  { id: 3, name: "<PERSON><PERSON>" },
  { id: 4, name: "Reebok" },
  { id: 5, name: "New Balance" },
]

export const collections: FilterItem[] = [
  { id: 1, name: "Summer 2024" },
  { id: 2, name: "Vintage Classics" },
  { id: 3, name: "Limited Edition" },
]

export const shippingMethod: FilterItem[] = [
  { id: 1, name: "Standard" },
  { id: 2, name: "Express" },
  { id: 3, name: "Overnight" },
]

export const gender: FilterItem[] = [
  { id: 1, name: "Men" },
  { id: 2, name: "Women" },
  { id: 3, name: "Unisex" },
]

export const colors: FilterItem[] = [
  { id: 1, name: "Red", color: "#FF0000" },
  { id: 2, name: "Blue", color: "#0000FF" },
  { id: 3, name: "Green", color: "#00FF00" },
  { id: 4, name: "Yellow", color: "#FFFF00" },
  { id: 5, name: "Purple", color: "#800080" },
  { id: 6, name: "Orange", color: "#FFA500" },
  { id: 7, name: "Pink", color: "#FFC0CB" },
  { id: 8, name: "Brown", color: "#A52A2A" },
  { id: 9, name: "Black", color: "#000000" },
  { id: 10, name: "White", color: "#FFFFFF" },
]

export const sizeOptions: SizeOption[] = [
  { value: "4", label: "4" },
  { value: "4.5", label: "4.5" },
  { value: "5", label: "5" },
  { value: "5.5", label: "5.5" },
  { value: "6", label: "6" },
  { value: "6.5", label: "6.5" },
  { value: "7", label: "7" },
  { value: "7.5", label: "7.5" },
  { value: "8", label: "8" },
  { value: "8.5", label: "8.5" },
  { value: "9", label: "9" },
  { value: "9.5", label: "9.5" },
  { value: "10", label: "10" },
  { value: "10.5", label: "10.5" },
  { value: "11", label: "11" },
  { value: "11.5", label: "11.5" },
  { value: "12", label: "12" },
  { value: "12.5", label: "12.5" },
  { value: "13", label: "13" },
  { value: "13.5", label: "13.5" },
  { value: "14", label: "14" },
  { value: "14.5", label: "14.5" },
  { value: "15", label: "15" },
  { value: "15.5", label: "15.5" },
  { value: "16", label: "16" },
  { value: "16.5", label: "16.5" },
  { value: "17", label: "17" },
]

export const sizeTypes: string[] = ["US", "EU", "UK", "CM"]

export const condition: FilterItem[] = [
  { id: 1, name: "Brand New" },
  { id: 2, name: "Used" },
  { id: 3, name: "99% Perfect" },
]

export const countries: TCountry = {
  id: 1,
  country: 1,
  name: "United States",
  currency: "USD",
  symbol: "$",
  region: 1,
}

export const options = [
  {
    text: "Featured Items",
  },
  {
    text: "Most Popular",
  },
  {
    text: "Newly Added",
  },
]
