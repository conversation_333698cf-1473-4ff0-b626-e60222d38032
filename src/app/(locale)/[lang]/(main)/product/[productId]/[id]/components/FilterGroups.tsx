import React, { forwardRef } from "react"
import FilterGroup, { FilterGroupHandle } from "@app/components/Filter"
import { FilterItem, SizeOption } from "@app/components/Filter/types"

interface FilterGroupsProps {
  categories: FilterItem[]
  subcategories: FilterItem[]
  brands: FilterItem[]
  shippingMethod: FilterItem[]
  gender: FilterItem[]
  sizeOptions: SizeOption[]
  sizeTypes: string[]
  condition: FilterItem[]
  handleFilterChange: (filterName: string, selectedItems: string[]) => void
  handlePriceChange: (min: number, max: number) => void
}

const FilterGroups = forwardRef<
  Record<string, FilterGroupHandle | null>,
  FilterGroupsProps
>((props, ref) => {
  const setFilterGroupRef = (key: string) => (el: FilterGroupHandle | null) => {
    if (!ref || typeof ref !== "object") return
    ;(ref as any).current[key] = el
  }

  return (
    <>
      <FilterGroup
        ref={setFilterGroupRef("category")}
        title="Category"
        type="default"
        items={props.categories}
        onFilterChange={(items) => props.handleFilterChange("category", items)}
      />
      <FilterGroup
        ref={setFilterGroupRef("subcategory")}
        title="Subcategory"
        type="default"
        items={props.subcategories}
        onFilterChange={(items) =>
          props.handleFilterChange("subcategory", items)
        }
      />
      <FilterGroup
        ref={setFilterGroupRef("brands")}
        title="Brands"
        type="default"
        items={props.brands}
        showSearch
        maxItems={5}
        onFilterChange={(items) => props.handleFilterChange("brand", items)}
      />
      <FilterGroup
        ref={setFilterGroupRef("shippingMethod")}
        title="Shipping Method"
        type="default"
        items={props.shippingMethod}
        onFilterChange={(items) =>
          props.handleFilterChange("shippingMethod", items)
        }
      />
      <FilterGroup
        ref={setFilterGroupRef("gender")}
        title="Gender"
        type="default"
        items={props.gender}
        onFilterChange={(items) => props.handleFilterChange("gender", items)}
      />
      <FilterGroup
        ref={setFilterGroupRef("size")}
        title="Size"
        type="size"
        sizeOptions={props.sizeOptions}
        sizeTypes={props.sizeTypes}
        onFilterChange={(items) => props.handleFilterChange("size", items)}
      />
      <FilterGroup
        ref={setFilterGroupRef("condition")}
        title="Condition"
        type="default"
        items={props.condition}
        onFilterChange={(items) => props.handleFilterChange("condition", items)}
      />
      <FilterGroup
        ref={setFilterGroupRef("priceRange")}
        title="Price Range"
        type="priceRange"
        currency="IDR"
        onPriceChange={props.handlePriceChange}
      />
    </>
  )
})

FilterGroups.displayName = "FilterGroups"

export default FilterGroups
