import React, {
  useCallback,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from "react"
import { useSearchParams, usePathname, useRouter } from "next/navigation"
import { useQueryClient } from "@tanstack/react-query"
import { But<PERSON>, Divider } from "@kickavenue/ui/components"
import { FilterGroupHandle } from "@app/components/Filter"
import { FilterItem, SizeOption } from "@app/components/Filter/types"

import FilterGroups from "./FilterGroups"

interface FilterContainerProps {
  categories: FilterItem[]
  subcategories: FilterItem[]
  brands: FilterItem[]
  shippingMethod: FilterItem[]
  gender: FilterItem[]
  sizeOptions: SizeOption[]
  sizeTypes: string[]
  condition: FilterItem[]
}

export interface FilterContainerHandle {
  handleReset: () => void
}

const FilterContainer = forwardRef<FilterContainerHandle, FilterContainerProps>(
  (props, ref) => {
    const searchParams = useSearchParams()
    const pathname = usePathname()
    const router = useRouter()
    const queryClient = useQueryClient()
    const filterGroupRefs = useRef<Record<string, FilterGroupHandle | null>>({})

    const createQueryString = useCallback(
      (name: string, value: string) => {
        const params = new URLSearchParams(searchParams || "")
        if (value) {
          params.set(name, value)
        } else {
          params.delete(name)
        }
        return params.toString()
      },
      [searchParams],
    )

    const handleFilterChange = useCallback(
      (filterName: string, selectedItems: string[]) => {
        const newQueryString = createQueryString(
          filterName,
          selectedItems.join(","),
        )
        router.push(`${pathname}?${newQueryString}`)
      },
      [createQueryString, pathname, router],
    )

    const handlePriceChange = useCallback(
      (min: number, max: number) => {
        const newQueryString = createQueryString("priceRange", `${min}-${max}`)
        router.push(`${pathname}?${newQueryString}`)
      },
      [createQueryString, pathname, router],
    )

    const handleReset = useCallback(() => {
      if (typeof window !== "undefined") {
        window.history.pushState({}, "", pathname || "")
      }

      queryClient.invalidateQueries({ queryKey: ["products"] })

      Object.values(filterGroupRefs.current).forEach((ref) => {
        if (ref && ref.reset) {
          ref.reset()
        }
      })

      router.replace(pathname || "", undefined)
    }, [pathname, queryClient, router])

    useImperativeHandle(ref, () => ({
      handleReset,
    }))

    const hasFilters = useMemo(() => {
      return searchParams?.toString() !== ""
    }, [searchParams])

    return (
      <div className="col-span-3 hidden md:block">
        <div className="sticky top-0 z-10 h-10 bg-white">
          <div className="mb-3 flex items-center justify-between bg-white">
            <div className="text-lg font-semibold">Filter</div>
            <Button
              size="lg"
              variant="link"
              className="!p-0 !text-base"
              onClick={handleReset}
              disabled={!hasFilters}
            >
              Reset
            </Button>
          </div>
          <Divider orientation="horizontal" />
        </div>
        <FilterGroups
          {...props}
          handleFilterChange={handleFilterChange}
          handlePriceChange={handlePriceChange}
          ref={filterGroupRefs}
        />
      </div>
    )
  },
)

FilterContainer.displayName = "FilterContainer"

export default FilterContainer
