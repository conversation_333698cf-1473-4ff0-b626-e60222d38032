// ProductGrid.tsx
import React from "react"
import ProductCard from "@kickavenue/ui/dist/src/components/ProductCard"
import { formatPrice } from "@utils/productDetail"
import { TCountry } from "types/country.type"

import { Product } from "../types"

interface ProductGridProps {
  list: Product[]
  country: TCountry
}

const ProductGrid: React.FC<ProductGridProps> = ({ list, country }) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
      {list?.map((item) => (
        <ProductCard
          key={item.product?.name}
          cardContainer={{
            className: "w-fit",
            style: { width: "100%" },
          }}
          imageProps={{
            src: "https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            width: 300,
            height: 300,
            alt: "Product Image",
          }}
          brandName={item.brand?.name || ""}
          itemName={item.product?.name || ""}
          price={formatPrice(item.lowestAsk || 0, country)}
          isWishlistActive={false}
          strikeThroughPrice={formatPrice(2000000, country)}
        />
      ))}
    </div>
  )
}

export default ProductGrid
