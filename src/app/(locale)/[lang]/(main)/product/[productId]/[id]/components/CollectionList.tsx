"use client"

import React, { useRef, useCallback, useMemo } from "react"
import { <PERSON><PERSON>crumb, Divider, Heading } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { usePathname } from "next/navigation"
import SortDropdown from "@components/SortDropdown"

import { CollectionListProps } from "../types"
import { useCollectionList } from "../hooks/useCollectionList"

import FilterContainer, { FilterContainerHandle } from "./FilterContainer"
import {
  categories,
  subcategories,
  brands,
  shippingMethod,
  gender,
  sizeOptions,
  sizeTypes,
  condition,
  countries,
} from "./filterData"
import ProductGrid from "./ProductGrid"
import FilterChips from "./FilterChips"

const CollectionList: React.FC<CollectionListProps> = () => {
  const filterRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const filterContainerRef = useRef<FilterContainerHandle>(null)
  const {
    open,
    generateChips,
    handleChipRemove,
    list,
    setOpen,
    pathname,
    router,
  } = useCollectionList()

  const currentPathname = usePathname()

  const handleClearAll = useCallback(() => {
    if (filterContainerRef.current) {
      filterContainerRef.current.handleReset()
    }
  }, [])

  const chips = useMemo(() => generateChips(), [generateChips])
  const hasChips = chips.length > 0

  const generateBreadcrumbItems = useCallback(() => {
    const pathParts = currentPathname?.split("/").filter(Boolean)
    const breadcrumbItems = [{ name: "Home", path: "/" }]

    for (let i = 0; i < Math.min(pathParts?.length || 0, 2); i++) {
      const part = pathParts?.[i] || ""
      if (part.startsWith("[") && part.endsWith("]")) {
        // Jika bagian path adalah parameter dinamis (seperti [id]), hentikan loop
        break
      }
      breadcrumbItems.push({
        name: part.charAt(0).toUpperCase() + part.slice(1),
        path: `/${pathParts?.slice(0, i + 1).join("/")}`,
      })
    }

    return breadcrumbItems
  }, [currentPathname])

  const breadcrumbItems = useMemo(
    () => generateBreadcrumbItems(),
    [generateBreadcrumbItems],
  )

  return (
    <div className="mb-8" ref={contentRef}>
      <Header />
      <div className="container mx-auto mb-8">
        <Breadcrumb listItem={breadcrumbItems} type="slash" />
      </div>
      <div className="container mx-auto grid grid-cols-12 gap-6">
        <SidebarFilter
          filterRef={filterRef}
          filterContainerRef={filterContainerRef}
        />
        <div className="col-span-9">
          <div className={cx(["flex justify-between", hasChips && "mb-4"])}>
            <ResultCount count={80} />
            <SortDropdown open={open} setOpen={setOpen} />
          </div>
          <FilterChips
            generateChips={generateChips}
            handleChipRemove={handleChipRemove}
            pathname={pathname || ""}
            router={router}
            onClearAll={handleClearAll}
          />
          <ProductGrid list={list} country={countries} />
        </div>
      </div>
    </div>
  )
}

const Header: React.FC = () => (
  <>
    <div className="my-8 text-center">
      <Heading heading="4" textStyle="bold">
        Similar Products
      </Heading>
      <div className="mt-2 text-base">
        Lorem ipsum dolor sit amet consectetur.
      </div>
    </div>
    <div className="mb-8 w-full">
      <Divider orientation="horizontal" />
    </div>
  </>
)

interface SidebarFilterProps {
  filterRef: React.RefObject<HTMLDivElement | null>
  filterContainerRef: React.RefObject<FilterContainerHandle | null>
}

const SidebarFilter: React.FC<SidebarFilterProps> = ({
  filterRef,
  filterContainerRef,
}) => (
  <div className="col-span-3">
    <div
      ref={filterRef}
      className="hide-scrollbar sticky top-[150px] h-[650px] overflow-y-auto"
    >
      <FilterContainer
        ref={filterContainerRef}
        categories={categories}
        subcategories={subcategories}
        brands={brands}
        shippingMethod={shippingMethod}
        gender={gender}
        sizeOptions={sizeOptions}
        sizeTypes={sizeTypes}
        condition={condition}
      />
    </div>
  </div>
)

interface ResultCountProps {
  count: number
}

const ResultCount: React.FC<ResultCountProps> = ({ count }) => (
  <div className="text-base text-gray-b-65">
    <span className="mr-1 font-bold">{count}</span>results
  </div>
)

export default CollectionList
