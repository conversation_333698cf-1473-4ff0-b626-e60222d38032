import { useState, useCallback } from "react"
import { useSearchParams, usePathname, useRouter } from "next/navigation"
import { getSimilarProductList } from "@utils/productDetail"

interface Chip {
  key: string
  value: string
}

export const useCollectionList = () => {
  const [open, setOpen] = useState(false)
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const router = useRouter()
  const list = getSimilarProductList()

  const generateChips = useCallback((): Chip[] => {
    const chips: Chip[] = []
    searchParams?.forEach((value, key) => {
      if (!value) return
      value.split(",").forEach((v) => {
        chips.push({ key, value: v })
      })
    })
    return chips
  }, [searchParams])

  const updateSearchParams = useCallback(
    (key: string, newValues: string[]) => {
      const params = new URLSearchParams(searchParams?.toString() || "")

      // Jika tidak ada values, hapus key dan redirect ke home jika tidak ada params lain
      if (newValues.length === 0) {
        params.delete(key)
        if (params.toString() === "") {
          router.push("/")
          return
        }
      } else {
        params.set(key, newValues.join(","))
      }

      router.push(`${pathname}?${params.toString()}`)
    },
    [searchParams, router, pathname],
  )

  const handleChipRemove = useCallback(
    (key: string, value: string) => {
      const currentValue = searchParams?.get(key)
      if (!currentValue) {
        router.push("/")
        return
      }
      const newValues = currentValue.split(",").filter((v) => v !== value)
      updateSearchParams(key, newValues)
    },
    [searchParams, updateSearchParams, router],
  )

  const toggleOpen = useCallback(() => setOpen((prev) => !prev), [])

  const clearAllChips = useCallback(() => {
    router.push("/")
  }, [router])

  return {
    open,
    setOpen,
    toggleOpen,
    generateChips,
    handleChipRemove,
    clearAllChips,
    list,
    pathname,
    router,
  }
}
