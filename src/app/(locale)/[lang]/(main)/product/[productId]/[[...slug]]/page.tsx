import React from "react"
import { Metada<PERSON> } from "next"
import ProductDetailContainer from "@app/components/ProductDetailContainer"
import { prepareProductDetailData } from "@utils/productDetail"

export interface DetailProps {
  params: Promise<{
    productId: string
  }>
}

async function getProductData(productId: number) {
  const result = await prepareProductDetailData(productId, true)
  return result
}

export async function generateMetadata(props: DetailProps): Promise<Metadata> {
  const params = await props.params
  const productId = parseInt(params.productId, 10)
  const { product } = await getProductData(productId)

  return {
    title: product?.name || "product",
  }
}

export default async function Detail(props: DetailProps) {
  const params = await props.params
  const productId = parseInt(params.productId, 10)
  const { product } = await getProductData(productId)

  return <ProductDetailContainer product={product} />
}
