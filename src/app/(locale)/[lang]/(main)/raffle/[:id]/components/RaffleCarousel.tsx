"use client"

import React from "react"
import Image from "next/image"
import { Carousel } from "@kickavenue/ui/components/Carousel/Carousel"
import { TRaffle } from "types/raffle.type"
import { convertS3UrlToCloudFront } from "@utils/misc"

interface RaffleCarouselProps {
  items: TRaffle[]
  onItemClick?: (item: TRaffle) => void
}

const RaffleCarousel = ({ items, onItemClick }: RaffleCarouselProps) => {
  const renderItem = (item: TRaffle) => {
    const imageUrl = convertS3UrlToCloudFront(item?.banner)

    return (
      <div
        key={item.ID}
        className="relative h-[450px] w-full cursor-pointer"
        onClick={() => onItemClick?.(item)}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            onItemClick?.(item)
          }
        }}
        role="button"
        tabIndex={0}
      >
        <Image
          alt={item.name || "Raffle image"}
          src={imageUrl}
          width={1200}
          height={450}
          className="size-full object-cover"
          priority
        />
      </div>
    )
  }

  if (!items?.length) {
    return null
  }

  return (
    <Carousel
      size="lg"
      variant="line"
      className="h-[450px] w-full"
      withBackground
      isLoop
      isInfinite
      loopIntervalMs={5000}
      classNameNext="z-10"
      classNamePrev="z-10"
    >
      {items.map(renderItem)}
    </Carousel>
  )
}

export default RaffleCarousel
