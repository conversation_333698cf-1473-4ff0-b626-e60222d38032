import { <PERSON><PERSON>, <PERSON><PERSON>, Head<PERSON> } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import Image from "next/image"
import CountdownTime from "@app/components/CountdownTimer"
import QuantityInput from "@app/components/InputQuantity"
import { useRaffleStore } from "@stores/raffleStore"
import { convertS3UrlToCloudFront, formatPrice } from "@utils/misc"
import { TRaffle } from "types/raffle.type"

import { useSubmitRaffleEntry } from "../hooks/useSubmitRaffleEntry"
import { CardRaffleProps } from "../type"

interface ICardRaffleProps extends CardRaffleProps {
  activeTab: "ONGOING" | "UPCOMING" | "HISTORY"
  items: TRaffle
}

const RaffleHeader = ({
  name,
  description,
  endTime,
}: {
  name: string
  description: string
  endTime: string
}) => (
  <>
    <Heading heading="4" textStyle="bold">
      {name}
    </Heading>
    <div className="mt-2 text-base">{description}</div>
    <CountdownTime endTime={endTime} />
  </>
)

const RaffleImage = ({
  name,
  imageUrl,
}: {
  name: string
  imageUrl: string
}) => (
  <div className="size-[230px] rounded-xl bg-gray-w-95">
    <Image
      src={convertS3UrlToCloudFront(imageUrl)}
      width={230}
      height={230}
      alt={name}
      className="mt-8 rounded-xl"
    />
  </div>
)

const RaffleStats = ({
  remainingTickets,
  eligibleTicket,
}: {
  remainingTickets: number
  eligibleTicket: number
}) => (
  <div className="mt-3 flex items-center justify-between">
    <Badge
      size="md"
      text={`${remainingTickets} Ticket Earned`}
      type="information"
    />
    <div className="text-sm">{eligibleTicket} Entries</div>
  </div>
)

const CardRaffle = ({ items, activeTab: activeTabProp }: ICardRaffleProps) => {
  const { isSubmitting } = useSubmitRaffleEntry()
  const { ticketQuantities, setTicketQuantity } = useRaffleStore()
  const activeTab = activeTabProp as "ONGOING" | "UPCOMING" | "HISTORY"
  const statusUpcoming = activeTab === "UPCOMING"
  const statusHistory = activeTab === "HISTORY"

  const handleQuantityChange = (itemId: number, quantity: number) => {
    setTicketQuantity(itemId, quantity)
  }

  const renderActionButton = (item: TRaffle) => {
    if (!item.is_active || statusUpcoming) {
      return (
        <Button
          variant="secondary"
          size="sm"
          className="mt-3 !w-full !text-base !font-medium"
          disabled
        >
          Coming Soon
        </Button>
      )
    }
    return (
      <QuantityInput
        initialValue={ticketQuantities[item.ID] || 0}
        min={0}
        max={item.total_ticket_all_user}
        onChange={(value) => handleQuantityChange(item.ID, value)}
        size="sm"
        variant="default"
        className="mt-3"
        disabled={isSubmitting}
      />
    )
  }

  return (
    <>
      {statusHistory && statusUpcoming && (
        <RaffleHeader
          name={items.name}
          description={items.description}
          endTime={items.end_time}
        />
      )}
      <div
        className={cx(
          "grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5",
          statusHistory && "opacity-50",
        )}
      >
        <div className="w-full max-w-[230px]">
          <RaffleImage
            name={items.name}
            imageUrl="https://kickavenue-media-assets-staging.s3.ap-southeast-1.amazonaws.com/staging/kickavenue-assets/item/23122024060155_0-adidas-handball-spezial-night-indigo-if7087-ka.png"
          />
          {!statusUpcoming && (
            <RaffleStats
              remainingTickets={items.remaining_tickets}
              eligibleTicket={items.eligible_ticket}
            />
          )}
          <div className="mt-2">
            <div className="mt-1 text-base">{items.name}</div>
          </div>
          {!statusHistory && (
            <div className="mt-2">
              {!statusUpcoming && (
                <>
                  <div className="text-sm text-gray">Purchase Price</div>
                  <Heading heading="5" textStyle="bold" className="mt-2">
                    {formatPrice(
                      items.base_ticket_price.min_unit_val,
                      null,
                      "IDR",
                    )}
                  </Heading>
                </>
              )}
              {renderActionButton(items)}
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default CardRaffle
