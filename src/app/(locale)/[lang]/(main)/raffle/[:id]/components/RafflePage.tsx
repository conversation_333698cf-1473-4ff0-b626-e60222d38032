import { Head<PERSON>, Tab, <PERSON>vide<PERSON>, <PERSON><PERSON> } from "@kickavenue/ui/components"
import Empty from "@kickavenue/ui/components/Empty"
import { TRaffle } from "types/raffle.type"
import { useRaffleStore } from "@stores/raffleStore"

import useGetRaffleStatus from "../hooks/useGetRaffleStatus"

import CardRaffle from "./CardRaffle"

const RafflePage = () => {
  const { activeTab, setActiveTab } = useRaffleStore()

  const { data: raffleData } = useGetRaffleStatus({
    status: activeTab,
    enable: true,
  })

  const renderData = () => {
    if (!raffleData?.Data?.Data?.content) {
      return (
        <div className="mt-8 flex justify-center">
          <Empty
            title="No Raffles at the Moment"
            subText="Stay tuned for exciting upcoming raffles! Check back soon to participate and win exclusive items."
            className="[&>img]:w-[200px]"
            actionButton={<Button>Explore Other Offers</Button>}
          />
        </div>
      )
    }

    return raffleData?.Data?.Data?.content?.map((group: TRaffle) => (
      <div className="mt-8" key={group.ID}>
        <CardRaffle items={group} activeTab={activeTab} />
      </div>
    ))
  }

  return (
    <div className="mx-auto mt-8 w-full max-w-[1440px]">
      <div className="px-xxl">
        <Heading heading="4" textStyle="bold">
          Raffle
        </Heading>
        <Tab className="mt-8 !gap-x-xl lg:!w-full lg:!justify-start">
          {["ONGOING", "UPCOMING", "HISTORY"].map((item) => (
            <Button
              data-active={item === activeTab}
              onClick={() =>
                setActiveTab(item as "ONGOING" | "UPCOMING" | "HISTORY")
              }
              variant="link"
              key={item}
              className="whitespace-nowrap !p-0 !text-gray-b-65"
            >
              {item}
            </Button>
          ))}
        </Tab>
        <Divider />
        {renderData()}
      </div>
    </div>
  )
}

export default RafflePage
