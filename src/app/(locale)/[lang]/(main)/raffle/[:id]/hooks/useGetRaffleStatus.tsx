import { useQuery } from "@tanstack/react-query"
import { GetStatusRaffle } from "@application/usecases/getStatusRaffle"
import { RaffleRepositoryImpl } from "@infrastructure/repositories/raffleRepository"
import { TRaffleResponse } from "types/raffle.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { useRaffleStore } from "@stores/raffleStore"

const useGetRaffleStatus = ({
  status,
  enable = true,
  onSuccess,
}: {
  status: "HISTORY" | "ONGOING" | "UPCOMING"
  enable?: boolean
  onSuccess?: (size: TRaffleResponse) => void
}) => {
  const { setRaffleData } = useRaffleStore()

  const getStatusRaffle = async () => {
    const r = new RaffleRepositoryImpl()
    const u = new GetStatusRaffle(r)
    const res = await u.execute(status)
    onSuccess?.(res)
    setRaffleData(res)
    return res
  }

  const { data, isLoading } = useQuery({
    queryKey: [QueryKeysConstant.GET_STATUS_RAFFLE, status],
    queryFn: getStatusRaffle,
    enabled: Boolean(status) && enable,
  })

  return { data, isLoading }
}

export default useGetRaffleStatus
