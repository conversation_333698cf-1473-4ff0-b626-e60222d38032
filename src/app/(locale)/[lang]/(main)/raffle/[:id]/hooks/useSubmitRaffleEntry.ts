/* eslint-disable @typescript-eslint/naming-convention */

import { useState } from "react"
import { CreateRaffleEntry } from "@application/usecases/createRaffleEntry"
import { RaffleEntryApiRepository } from "@infrastructure/repositories/raffleEntryApiRepository"

export const useSubmitRaffleEntry = () => {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const submitRaffleEntry = async (params: {
    raffle_item_id: number
    user_id: number
    used_ticket_count: number
    winner: boolean
  }) => {
    try {
      setIsSubmitting(true)
      const raffleEntryRepository = new RaffleEntryApiRepository()
      const createRaffleEntry = new CreateRaffleEntry(raffleEntryRepository)
      await createRaffleEntry.execute(params)
    } finally {
      setIsSubmitting(false)
    }
  }

  return {
    submitRaffleEntry,
    isSubmitting,
  }
}
