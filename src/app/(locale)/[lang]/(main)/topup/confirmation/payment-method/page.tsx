"use client"

import {
  <PERSON>,
  But<PERSON>,
  IconArrowLeftOutline,
  IconInfoCircleBulk,
  RadioButton,
  Divider,
} from "@kickavenue/ui/components"
import { useCallback, useState } from "react"
import { useRouter } from "next/navigation"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import useFetchPaymentMethods from "@app/hooks/useFetchPaymentMethods"
import { usePaymentStore } from "stores/paymentStore"
import { getIconFromFinanceProvider } from "@components/Payment/payment.utils"
import ListItem from "@components/shared/ListItem"
import { EPaymentMethodType } from "types/paymentMethod.type"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { capitalizeFirstLetterForBank } from "@utils/misc"
import { getPaymentMethodByType } from "@utils/paymentMethod.utils"
import { objectToQueryParams } from "@utils/query.utils"
import { TOPUP_CONFIRMATION_FROM_PAYMENT_METHOD_PAGE } from "@components/TopUpConfirmation/constants"
import { PaymentType } from "types/payment.typs"

const TOP_UP_CONFIRMATION_PATH = objectToQueryParams({
  path: PageRouteConstant.TOPUP_CONFIRMATION,
  query: {
    from: TOPUP_CONFIRMATION_FROM_PAYMENT_METHOD_PAGE,
  },
})

const TopUpPaymentMethodPage = () => {
  const router = useRouter()
  const { paymentType, setPayment } = usePaymentStore()
  const chosenPaymentType = paymentType?.id

  const [saveTempPaymentType, setSaveTempPaymentType] =
    useState<PaymentType | null>(paymentType || null)

  const { data, isLoading } = useFetchPaymentMethods({
    enabled: true,
  })

  const listVAPaymentMethod = getPaymentMethodByType(
    data ?? [],
    EPaymentMethodType.VirtualAccount,
  )

  const handleBack = useCallback(
    () => router.push(TOP_UP_CONFIRMATION_PATH),
    [router],
  )

  const handleConfirm = useCallback(() => {
    setPayment({
      id: saveTempPaymentType?.id,
      name: saveTempPaymentType?.name || "-",
      paymentMethod: EPaymentMethodType.VirtualAccount,
    })

    router.push(TOP_UP_CONFIRMATION_PATH)
  }, [router, saveTempPaymentType, setPayment])

  if (isLoading) {
    return (
      <div className="bg-gray-w-95 p-xxl pt-lg md:flex md:justify-center">
        <SpinnerLoading />
      </div>
    )
  }

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="w-full p-base text-center text-heading-4 font-bold">
        Select Payment
      </div>
      <div className="bg-gray-w-95 p-xxl pt-lg md:flex md:justify-center">
        <div className="rounded-base bg-white md:w-[612px] md:p-lg">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 rounded-sm bg-[#EAF4FF] p-3 shadow-base">
              <div className="text-base font-bold">
                <IconInfoCircleBulk className="size-6 text-[#2E90FA]" />
              </div>
              <Text size="sm" state="primary" type="regular">
                Your payment will be verified by the payment gateway
              </Text>
            </div>
            <Text size="sm" state="primary" type="bold" className="mt-6">
              Virtual Account
            </Text>
            <div className="mt-4 flex flex-col gap-4">
              {listVAPaymentMethod.map((bank, index) => (
                <ListItem
                  key={bank.id}
                  title={capitalizeFirstLetterForBank(bank.name)}
                  leadingIcon={getIconFromFinanceProvider(bank.name)}
                  withDivider={index !== listVAPaymentMethod.length - 1}
                  trailingIcon={
                    <RadioButton
                      name="bank"
                      value={bank.id}
                      checked={
                        saveTempPaymentType?.id === bank.id ||
                        chosenPaymentType === bank.id
                      }
                      onChange={() => setSaveTempPaymentType(bank)}
                    />
                  }
                />
              ))}
            </div>
          </div>
          <div className="md:-mx-lg">
            <Divider orientation="horizontal" />
          </div>
          <div className="flex w-full justify-center gap-base pt-lg">
            <Button
              size="lg"
              variant="secondary"
              className="!w-full"
              IconLeft={IconArrowLeftOutline}
              onClick={handleBack}
            >
              Back
            </Button>
            <Button
              size="lg"
              variant="primary"
              className="!w-full"
              disabled={!saveTempPaymentType && !chosenPaymentType}
              onClick={handleConfirm}
            >
              Confirm Payment
            </Button>
          </div>
        </div>
      </div>
    </AuthRedirectWrapper>
  )
}

export default TopUpPaymentMethodPage
