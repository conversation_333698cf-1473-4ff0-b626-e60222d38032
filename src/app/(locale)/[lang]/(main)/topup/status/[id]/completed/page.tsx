/* eslint-disable react-hooks/exhaustive-deps */
"use client"

import { useEffect } from "react"
import { snakeCase } from "lodash"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import TopUpKickCreditCompletedPayment from "@components/TopUpKickCredit/CompletedPayment/TopUpKickCreditCompletedPayment"
import TopUpSummaryModal from "@components/TopUpKickCredit/components/TopUpSummaryModal"
import useFetchTopUpData from "@components/TopUpKickCredit/hooks/useFetchTopUpData"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"
import { EBalanceType } from "types/balance.type"

const TopUpSuccessPage = () => {
  const { data: topUpData, isLoading } = useFetchTopUpData()
  const { member } = useMemberStore()

  useEffect(() => {
    event({
      action: "user_top_up_success",
      params: {
        [snakeCase("disbursement_no")]: topUpData?.invoiceNumber,
        [snakeCase("payment_method")]: topUpData?.paymentMethodName,
        [snakeCase("top_up_amount")]: String(topUpData?.amount || ""),
        [snakeCase("top_up_type")]: EBalanceType.KickCredit,
      },
      userId: String(member?.id || ""),
    })
  }, [])

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="bg-gray-w-95 p-4 md:flex md:justify-center">
        <TopUpKickCreditCompletedPayment
          topUpData={topUpData}
          isLoading={isLoading}
        />
      </div>

      <TopUpSummaryModal topUpData={topUpData} />
    </AuthRedirectWrapper>
  )
}

export default TopUpSuccessPage
