"use client"

import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import useFetchTopUpData from "@components/TopUpKickCredit/hooks/useFetchTopUpData"
import TopUpKickCreditPendingPayment from "@components/TopUpKickCredit/PendingPayment/TopUpKickCreditPendingPayment"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { TopUpStatus } from "types/topup.type"
import TopUpSummaryModal from "@components/TopUpKickCredit/components/TopUpSummaryModal"

const TopUpAwaitingPage = () => {
  const router = useRouter()
  const { data: topUpData, isLoading, refetch, isError } = useFetchTopUpData()

  useEffect(() => {
    if (topUpData?.status === TopUpStatus.Completed) {
      router.push(`/topup/status/${topUpData.id}/completed`)
    }
  }, [topUpData, router])

  useEffect(() => {
    if (isError) router.push(PageRouteConstant.PROFILE_KICK_CREDIT)
  }, [isError, router])

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="bg-gray-w-95 p-4 md:flex md:justify-center">
        <TopUpKickCreditPendingPayment
          topUpData={topUpData}
          isLoading={isLoading}
          refetchTransaction={refetch}
        />
      </div>

      <TopUpSummaryModal topUpData={topUpData} />
    </AuthRedirectWrapper>
  )
}

export default TopUpAwaitingPage
