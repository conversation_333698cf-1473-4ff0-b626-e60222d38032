import { Metadata } from "next"
import { getProductDetail } from "@utils/productDetail"
import SellingContent from "@components/Selling/SellingContent"

export interface SellingProps {
  params: Promise<{
    id: string
    sizeid: string
  }>
}

export async function generateMetadata(props: SellingProps): Promise<Metadata> {
  const params = await props.params
  const productId = parseInt(params.id, 10)
  const product = await getProductDetail(productId)

  return {
    title: product?.name || "product",
  }
}

export default async function SellingPage() {
  return <SellingContent />
}
