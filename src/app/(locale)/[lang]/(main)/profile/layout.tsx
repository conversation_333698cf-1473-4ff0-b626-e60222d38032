'use client'
import { ReactNode } from "react"
import { SidebarProfile } from "@app/components/Profile/SidebarProfile"
import Wallet from "@app/components/Profile/Wallet"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { cx } from "class-variance-authority"
import { useSidebarStore } from "stores/sidebarCollapseStore"


const ProfileLayout: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isCollapse } = useSidebarStore()
  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="bg-gray-w-95 lg:flex lg:justify-center">
        <div className="flex w-full flex-col gap-base p-sm sm:flex-row lg:p-xl xl:max-w-[calc(100vw-80px)] xl:gap-lg xl:px-0">
          <div className={cx("relative flex size-full shrink-0 flex-col gap-base", isCollapse ? "sm:w-[56px]" : "sm:w-[294px]")}>
            <Wallet />
            <SidebarProfile />
          </div>
          <div
            className={cx(
              "w-full max-w-[calc(100vw)] rounded-base bg-white",
              isCollapse ? "sm:max-w-[calc(100vw-(56px+24px+16px))]" : "sm:max-w-[calc(100vw-(294px+24px+16px))]",
              isCollapse ? "lg:m-0 lg:h-full lg:max-w-[calc(100vw)]" : "lg:m-0 lg:h-full lg:max-w-[calc(100vw-398px)]",
            )}
          >
            {children}
          </div>
        </div>
      </div>
    </AuthRedirectWrapper>
  )
}

export default ProfileLayout
