import MessageCenter from "@app/components/MessageCenter/MessageCenter"

import { messageHooks } from "./hooks/useMessage"
import { notFound } from "next/navigation"

export default async function MessageCenterPage() {
  const { prepareAllMessages } = messageHooks()
  const { allMessages, unreadMessages } = await prepareAllMessages()

  notFound()

  return (
    <MessageCenter allMessages={allMessages} unreadMessages={unreadMessages} />
  )
}
