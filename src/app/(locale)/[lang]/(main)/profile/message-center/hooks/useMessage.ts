import { GetMessages } from "@application/usecases/getMessages"
import { MessageApiRepository } from "@infrastructure/repositories/messageApiRepository"

export const messageHooks = () => {
  function getAllMessages() {
    const repository = new MessageApiRepository()
    const usecase = new GetMessages(repository)
    return usecase.getAllMessages()
  }

  function getUnreadMessages() {
    const repository = new MessageApiRepository()
    const usecase = new GetMessages(repository)
    return usecase.getUnreadMessages()
  }

  async function prepareAllMessages() {
    const allMessages = await getAllMessages()
    const unreadMessages = await getUnreadMessages()

    return {
      allMessages,
      unreadMessages,
    }
  }

  return {
    prepareAllMessages,
  }
}
