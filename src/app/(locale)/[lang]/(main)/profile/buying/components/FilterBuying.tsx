import React, { useCallback, useMemo, useRef, useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button, IconFilterOutline, Search } from "@kickavenue/ui/components"
import debounce from "lodash/debounce"

interface FilterBuyingProps {
  setOpenFilterOfferPrice: (open: boolean) => void
}

export default function FilterBuying({
  setOpenFilterOfferPrice,
}: FilterBuyingProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const lastSearchValueRef = useRef("")
  const [searchValue, setSearchValue] = useState("")

  useEffect(() => {
    const queryValue = searchParams?.get("q") || ""
    setSearchValue(queryValue)
    lastSearchValueRef.current = queryValue
  }, [searchParams])

  const updateSearchParam = useCallback(
    (value: string) => {
      if (value === lastSearchValueRef.current) {
        return
      }
      lastSearchValueRef.current = value

      const current = new URLSearchParams(
        Array.from(searchParams?.entries() || []),
      )

      if (value) {
        current.set("q", value)
      } else {
        current.delete("q")
      }

      const search = current.toString()
      const query = search ? `?${search}` : ""

      router.push(`${window.location.pathname}${query}`)
    },
    [router, searchParams],
  )

  const debouncedSearch = useMemo(
    () => debounce(updateSearchParam, 300),
    [updateSearchParam],
  )

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      setSearchValue(newValue)
      debouncedSearch(newValue)
    },
    [debouncedSearch],
  )

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key !== "Enter") {
        return
      }
      debouncedSearch.cancel()
      updateSearchParam((e.target as HTMLInputElement).value)
    },
    [updateSearchParam, debouncedSearch],
  )

  return (
    <div className="mt-6 grid grid-cols-12">
      <div className="col-span-12 grid grid-cols-12 md:col-span-8">
        <Search
          placeholder="Search"
          className="col-span-9 !h-[41px] !border-gray-w-80 md:col-span-6"
          onChange={handleSearchChange}
          onKeyPress={handleKeyPress}
          value={searchValue}
        />
        <Button
          variant="secondary"
          IconLeft={IconFilterOutline}
          className="col-span-3 !ml-2 !p-3 !text-sm md:col-span-2"
          size="md"
          onClick={() => setOpenFilterOfferPrice(true)}
        >
          Filter
        </Button>
      </div>
    </div>
  )
}
