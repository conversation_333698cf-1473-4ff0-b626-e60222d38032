import { signIn } from "next-auth/react"
import { Router } from "next/router"
import { AuthService } from "@application/services/authService"

interface AuthHandlersProps {
  email: string
  password: string
  setError: (error: string) => void
  setIsLoading: (isLoading: boolean) => void
  authService: AuthService
  router: Router
}

export default function useAuthHandlers({
  email,
  password,
  setError,
  setIsLoading,
  authService,
  router,
}: AuthHandlersProps) {
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await authService.login(email, password)

      if (result.Code === 0) {
        try {
          const payload: any = {
            redirect: false,
            email,
            password,
            callbackUrl: "/",
            accessToken: result.Data.access_token,
            refreshToken: result.Data.refresh_token,
          }

          const signInResult = await signIn("credentials", payload)

          if (signInResult?.error) {
            setError(signInResult.error)
          } else if (signInResult?.ok) {
            router.push("/")
          }
        } catch (signInError) {
          setError("Failed to sign in. Please try again.")
        }
      } else {
        setError(result.Message || "An error occurred during login")
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return { handleSubmit }
}
