import { signOut } from "next-auth/react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useQueryClient } from "@tanstack/react-query"
import { AuthService } from "@application/services/authService"
import { useMemberStore } from "stores/memberStore"
import { useSearchStore } from "stores/searchStore"
import { TMember } from "types/member.type"
import { event } from "@lib/gtag"

import useFormState from "./useFormState"
import useAuthHandlers from "./useAuthHandlers"
import useSocialLogin from "./useSocialLogin"

const useAuthService = () => new AuthService()

// Helper function to clear non-auth browser storage
const clearNonAuthStorage = () => {
  try {
    // Only clear non-auth related localStorage items
    const keysToRemove: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && !key.includes("next-auth") && !key.includes("auth")) {
        keysToRemove.push(key)
      }
    }
    keysToRemove.forEach((key) => localStorage.removeItem(key))

    // Clear sessionStorage (temporary data)
    sessionStorage.clear()
  } catch (storageError) {
    console.warn("Storage clear error:", storageError)
  }
}

// Helper function to handle Cognito logout
const handleCognitoLogout = async () => {
  try {
    const response = await fetch("/api/auth/cognito-logout", {
      method: "POST",
      headers: {
        contentType: "application/json",
      },
      body: JSON.stringify({}),
    })

    if (response.ok) {
      const data = await response.json()

      if (data.success && data.logoutUrl) {
        // Redirect to Cognito logout URL - this will clear the Cognito session
        // and then redirect back to our app
        window.location.href = data.logoutUrl
        // Indicate that redirect happened
        return true
      }
    }
  } catch (error) {
    console.error("Cognito logout API call failed:", error)
  }
  // Indicate no redirect happened
  return false
}

export function useLoginForm() {
  const {
    email,
    setEmail,
    password,
    setPassword,
    error,
    setError,
    isLoading,
    setIsLoading,
  } = useFormState()
  const router = useRouter() as any
  const authService = useAuthService()
  const { setMember, member } = useMemberStore()
  const { clearProductWishlist } = useSearchStore()
  const queryClient = useQueryClient()

  const isFormValid = email.trim() !== "" && password.trim() !== ""

  const handleEmailChange = (newEmail: string) => setEmail(newEmail)
  const handlePasswordChange = (newPassword: string) => setPassword(newPassword)

  const { handleSubmit } = useAuthHandlers({
    email,
    password,
    setError,
    setIsLoading,
    authService,
    router,
  })

  const { handleSocialLogin } = useSocialLogin()

  const handleSignOut = async () => {
    setIsLoading(true)
    setError("")

    try {
      event({
        action: "user_logged_out",
        params: {
          email: member?.email || "",
          province: member.provinceName,
        },
        userId: String(member?.id || ""),
      })

      // Clear local state first
      setMember({} as TMember)
      clearProductWishlist()
      queryClient.clear()

      // Clear non-auth browser storage (NextAuth handles auth cookies)
      clearNonAuthStorage()

      // Sign out from NextAuth (this will clear auth cookies securely)
      await signOut({ redirect: false })

      // Always attempt Cognito logout
      const cognitoRedirected = await handleCognitoLogout()

      // If Cognito logout fails or is not needed, redirect to login
      if (!cognitoRedirected) {
        router.push("/login")
      }
    } catch (logoutError) {
      setError(
        `An unexpected error occurred during sign out. Please try again. Details: ${
          logoutError instanceof Error
            ? logoutError.message
            : String(logoutError)
        }`,
      )
      // Even if there's an error, try to redirect to login
      router.push("/login")
    } finally {
      setIsLoading(false)
    }
  }

  return {
    email,
    password,
    error,
    isLoading,
    isFormValid,
    handleEmailChange,
    handlePasswordChange,
    handleSubmit,
    handleSocialLogin,
    handleSignOut,
  }
}
