import React from "react"
import {
  Button,
  IconAppleColor,
  IconGoogleColor,
  IconFacebookColor,
} from "@kickavenue/ui/components"

import useSocialLogin from "../hooks/useSocialLogin"

export const SocialButton: React.FC = () => {
  const { handleSocialLogin } = useSocialLogin()

  return (
    <div className="max-w-full">
      <Button
        size="md"
        variant="secondary"
        className="mt-2 !w-full"
        onClick={() => handleSocialLogin("google")}
      >
        <IconGoogleColor className="mr-2" /> <div>Sign in with Google</div>
      </Button>
      <Button
        size="md"
        variant="secondary"
        className="mt-2 !w-full"
        onClick={() => handleSocialLogin("apple")}
      >
        <IconAppleColor className="mr-2" /> <div>Sign in with Apple</div>
      </Button>
      <Button
        size="md"
        variant="secondary"
        className="mt-2 !w-full"
        onClick={() => handleSocialLogin("facebook")}
      >
        <IconFacebookColor className="mr-2" /> <div>Sign in with Facebook</div>
      </Button>
    </div>
  )
}
