"use client"

import React, { useEffect, type JS<PERSON> } from "react"
import Link from "next/link"
import { Head<PERSON>, But<PERSON> } from "@kickavenue/ui/components"
import { useSearchParams } from "next/navigation"
import { useTranslations } from "@app/i18n"
import { AuthSwitchPrompt } from "@app/components"
import EmailInput from "@app/components/InputEmail"
import PasswordInput from "@app/components/InputPassword"
import { getErrorMessageNextAuth } from "@utils/misc"
import useToast from "@app/hooks/useToast"

import { useLoginForm } from "../hooks/useLoginForm"

import AlertLogin from "./AlertLogin"
import { SocialButton } from "./SocialButton"

export default function LoginForm(): JSX.Element {
  const searchParams = useSearchParams()
  const errorNextAuth = searchParams?.get("error")
  const t = useTranslations()
  const {
    email,
    password,
    error,
    isLoading,
    isFormValid,
    handleEmail<PERSON><PERSON><PERSON>,
    handlePassword<PERSON>hange,
    handleSubmit,
  } = useLoginForm()
  const { setShowToast } = useToast()

  useEffect(() => {
    if (!errorNextAuth) return
    const message = getErrorMessageNextAuth(errorNextAuth as string)
    setShowToast(true, message, "danger")
  }, [errorNextAuth, setShowToast])

  return (
    <div className="mx-auto mb-80 mt-8 max-w-md px-6 md:px-0">
      <div className="mb-8 text-center">
        <Heading heading="4" textStyle="bold">
          {t("login")}
        </Heading>
      </div>
      <AlertLogin error={error} />
      <form onSubmit={handleSubmit}>
        <EmailInput
          onChange={handleEmailChange}
          value={email}
          className="mb-4"
        />
        <PasswordInput
          value={password}
          onChange={handlePasswordChange}
          label="Password"
          className="mt-2"
          showRequirements={false}
        />
        <div className="mb-4 text-right">
          <Link href="/forgot-password">
            <Button size="sm" variant="link" className="!p-0">
              Forgot Password?
            </Button>
          </Link>
        </div>
        <div className="mt-6 w-full">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            type="submit"
            disabled={isLoading || !isFormValid}
          >
            Login
          </Button>
        </div>
      </form>
      <div className="mb-6 mt-4 text-center text-sm">Or continue using</div>
      <SocialButton />
      <AuthSwitchPrompt
        promptText="Don't have an account?"
        linkText={t("register")}
        linkHref="/register"
      />
    </div>
  )
}
