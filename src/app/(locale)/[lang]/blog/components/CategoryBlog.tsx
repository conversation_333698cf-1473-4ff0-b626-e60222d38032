"use client"

import Image from "next/image"
import { Heading, But<PERSON> } from "@kickavenue/ui/components"
import { getSimilarProductList } from "@utils/productDetail"
import CenterWrapper from "@shared/CenterWrapper"

import CardBlog from "./CardBlog"
import DataCarouselBlog from "./DataCarouselBlog"

interface CategoryBlogProps {
  image?: boolean
}

const CategoryBlog = ({ image = true }: CategoryBlogProps) => {
  const list = getSimilarProductList()

  const renderItem = () => (
    <CardBlog
      title="ZODIAC and STANLEY Team Up to Release Special Edition IceFlow Flip Straw Tumbler"
      date="By <PERSON> Delaney - Sep 18, 2024"
      slug="#"
      image="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    />
  )

  return (
    <div className="mx-auto mt-8 w-full">
      {image && (
        <div className="relative aspect-[16/5] w-full">
          <Image
            src="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            fill
            style={{ objectFit: "cover" }}
            alt="image"
          />
        </div>
      )}
      <div className="container mx-auto mt-8">
        <CenterWrapper className="!p-0 !px-sm md:!grid-cols-2">
          <Heading heading="4" textStyle="bold" className="!p-0">
            Card
          </Heading>
          <div className="text-right">
            <Button size="md" variant="link">
              View All
            </Button>
          </div>
        </CenterWrapper>
        <CenterWrapper className="!p-0 !px-sm">
          <div className="col-span-12 text-gray">
            Lorem ipsum dolor sit amet consectetur.
          </div>
        </CenterWrapper>
      </div>
      <DataCarouselBlog data={list} renderItem={renderItem} itemsPerSlide={4} />
    </div>
  )
}

export default CategoryBlog
