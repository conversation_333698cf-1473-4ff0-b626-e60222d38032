/**
 * Authentication Configuration
 * Centralized NextAuth and session management settings
 */

const isTestingMode = process.env.NODE_ENV === "development"
const isProduction = process.env.NODE_ENV === "production"

export const AUTH_CONFIG = {
  // NextAuth session settings (aligned with NextAuth options)
  SESSION: {
    MAX_AGE: 10 * 60 * 60, // 10 hours (matches NextAuth config)
    UPDATE_AGE: 5 * 60, // 5 minutes (matches NextAuth config)
    STRATEGY: "jwt" as const,
  },

  // Token expiration settings
  TOKEN: {
    EXPIRATION: isTestingMode
      ? 1 * 60 * 1000 // 1 minute for testing
      : 10 * 60 * 60 * 1000, // 10 hours for production
  },

  // Client-side session refetch interval
  CLIENT: {
    REFETCH_INTERVAL: isTestingMode
      ? 20 // 20 seconds for testing
      : 5 * 60, // 5 minutes for production
  },

  // Security settings
  SECURITY: {
    USE_SECURE_COOKIES: isProduction,
    SAME_SITE: "lax" as const,
    HTTP_ONLY: true,
  },

  // Environment flags
  IS_TESTING_MODE: isTestingMode,
  IS_PRODUCTION: isProduction,
} as const

export default AUTH_CONFIG
