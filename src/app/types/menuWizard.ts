/* eslint-disable @typescript-eslint/naming-convention */

export interface MenuWizardResponse {
  code: number
  status: string
  data: MenuWizard
  message: string
}

export interface MenuWizard {
  id: number
  title: string
  pageType: string
  menuDefault: boolean
  startTime: string
  endTime: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  menuWizardSections: MenuWizardSection[]
}

export interface MenuWizardSection {
  id: number
  menuWizardId: number
  contentType: string
  sequence: number
  backgroundColor: string
  title: string
  description: string
  actionTitle: string
  contentId: number
  image: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  keyword: string
  sectionContent: SectionContent | null
}

export interface SectionContent {
  banner: string
  id: number
  type: string
  value: string
  excludedItemId: string
  name: string
  slug: string
  imagePortrait: string
  imageLandscape: string
  description: string
  isActive: boolean
  startTime: string
  endTime: string
  defaultSort: string
  sellerId: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  items: Item[]
  redirectionRule: string
}

export interface Item {
  id: number
  name: string
  skuCode: string
  description: string
  colorway: string
  gender: string
  retailPrice: Price
  countryId: number
  releaseDate: string
  tag: string
  weight: number
  dimension: string
  sizeChartId: number
  marketplaceLink: string
  isAutoScrape: boolean
  commissionFee: Price
  isActive: boolean
  isAddOn: boolean
  isVoucherApplicable: boolean
  isReceiveSellRequest: boolean
  isCanMakeOffer: boolean
  isHotItem: boolean
  isNonPurchaseable: boolean
  images: string[]
  seoTitleIna: string
  seoKeywordIna: string[] | null
  seoDescriptionIna: string
  seoTitleEng: string
  seoKeywordEng: string[] | null
  seoDescriptionEng: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
}

export interface Price {
  amount: string
  min_unit_val: number
  amount_text: string
}
