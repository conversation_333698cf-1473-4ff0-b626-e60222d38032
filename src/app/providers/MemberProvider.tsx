"use client"

import { useCallback, useEffect } from "react"
import useQuery from "@app/hooks/useQuery"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { useMemberStore } from "stores/memberStore"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { event } from "@lib/gtag"
import { convertS3UrlToCloudFront } from "@utils/misc"
import useAuthSession from "@app/hooks/useAuthSession"
import { signOut } from "next-auth/react"

export const MemberProvider = () => {
  const { sessionUser, isError } = useAuthSession()
  const { setMember, setLoading } = useMemberStore()

  const fetchMember = useCallback(async () => {
    const memberRepository = new MemberApiRepository()
    setLoading(true)
    const res = await memberRepository.getByMy()
    setMember({
      ...res,
      image: convertS3UrlToCloudFront(res.image),
    })
    setLoading(false)

    event({
      action: "user_logged_in",
      params: {
        email: res.email || "",
        province: res.provinceName,
      },
      userId: String(res.id) || "",
    })

    return res
  }, [setMember, setLoading])

  useQuery({
    queryKey: [QueryKeysConstant.GET_LOGGED_IN_MEMBER, sessionUser?.email],
    queryFn: fetchMember,
    enabled: Boolean(sessionUser?.email),
    // Set custom staleTime for user's profile to 30 minutes
    staleTime: 30 * 60 * 1000,
  })

  // logout when session is expired
  useEffect(() => {
    if (isError) {
      signOut({ callbackUrl: "/login" })
    }

    // Debug logging for session state
    if (process.env.NODE_ENV === "development") {
      console.log("====== SESSION DEBUG START =======")
      console.log("current time  ", new Date())
      console.log(
        "sessionUser?.accessToken ",
        sessionUser?.accessToken ? "***TOKEN_PRESENT***" : "NO_TOKEN",
      )
      console.log(
        "sessionUser?.refreshToken ",
        sessionUser?.refreshToken
          ? "***REFRESH_TOKEN_PRESENT***"
          : "NO_REFRESH_TOKEN",
      )
      console.log(
        "sessionUser?.accessTokenExpires ",
        new Date(sessionUser?.accessTokenExpires ?? 0),
      )
      console.log("isError ", isError)
      console.log("====== SESSION DEBUG END =======")
    }
  }, [isError, sessionUser])

  return <></>
}
