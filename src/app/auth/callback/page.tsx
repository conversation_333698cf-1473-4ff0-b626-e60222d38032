import { Suspense } from "react"
import SpinnerLoading from "@components/shared/SpinnerLoading"

import CallbackContent from "./CallbackContent"

export default function CallbackPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <SpinnerLoading />
            <p className="text-gray-600 mt-4">Loading...</p>
          </div>
        </div>
      }
    >
      <CallbackContent />
    </Suspense>
  )
}
