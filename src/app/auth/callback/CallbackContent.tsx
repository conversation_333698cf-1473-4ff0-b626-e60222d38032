"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import SpinnerLoading from "@components/shared/SpinnerLoading"

export default function CallbackContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const code = searchParams?.get("code")
    const state = searchParams?.get("state")

    if (!code) {
      setError("No authorization code received")
      return
    }

    const syncToken = async () => {
      try {
        const gatewayUrl =
          process.env.NEXT_PUBLIC_GATEWAY_BASE_URL ||
          "https://revampstaging.kickavenue.com"
        const response = await fetch(
          `${gatewayUrl}/api/v1/auth/callback?code=${code}&state=${state}`,
          {
            method: "GET",
            credentials: "include",
            headers: {
              Accept: "application/json",
            },
          },
        )

        if (!response.ok) {
          const data = await response.json().catch(() => ({}))
          throw new Error(data.message || "Failed to sync token")
        }

        router.push("/")
      } catch (err: any) {
        setError(err.message || "Failed to sync token")
      }
    }

    syncToken()
  }, [searchParams, router])

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl text-red-500 mb-4">Authentication Error</h1>
          <p className="text-gray-600">{error}</p>
          <button
            type="button"
            onClick={() => router.push("/login")}
            className="bg-primary mt-4 rounded-lg px-4 py-2 text-white"
          >
            Back to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <SpinnerLoading />
        <p className="text-gray-600 mt-4">Syncing authentication...</p>
      </div>
    </div>
  )
}
