/* eslint-disable @typescript-eslint/naming-convention */

import { NextRequest, NextResponse } from "next/server"
import { apiHttpClient } from "@infrastructure/providers/apiHttpClient"
import { extractUserClaimsFromToken } from "@utils/jwt"

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json()
    const authHeader = request.headers.get('authorization')
    const userIDHeader = request.headers.get('x-userid') || request.headers.get('X-UserID')

    if (!password) {
      return NextResponse.json(
        {
          code: 400,
          status: "error",
          message: "Password is required",
          data: null,
        },
        { status: 400 },
      )
    }

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (authHeader) {
      headers["Authorization"] = authHeader
      
      // Extract user ID from JWT token if not provided in header
      if (!userIDHeader && authHeader.startsWith('Bearer ')) {
        try {
          const token = authHeader.substring(7) // Remove 'Bearer ' prefix
          const claims = extractUserClaimsFromToken(token)
          if (claims?.userId) {
            headers["X-UserID"] = String(claims.userId)
          }
        } catch (error) {
          console.error('Debug - Failed to extract user ID from JWT:', error)
        }
      }
    }

    if (userIDHeader) {
      headers["X-UserID"] = userIDHeader
    }

    const response = await apiHttpClient.post(
      "/api/v1/user/verify-password",
      { password },
      { headers },
    )

    // Handle 204 No Content response
    if (response.status === 204) {
      return new NextResponse(null, { status: 204 })
    }
    
    return NextResponse.json(response.data, { status: response.status })
  } catch (error: any) {
    console.error('Debug - Error details:', {
      status: error.response?.status,
      message: error.response?.data?.message,
      data: error.response?.data
    })
    
    return NextResponse.json(
      {
        code: error.response?.status || 500,
        status: "error",
        message: error.response?.data?.message || "Failed to verify password",
        data: null,
      },
      { status: error.response?.status || 500 },
    )
  }
}
