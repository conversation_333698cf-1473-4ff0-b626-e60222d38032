import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)

  const debugInfo = {
    timestamp: new Date().toISOString(),
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      COGNITO_CLIENT_ID: process.env.COGNITO_CLIENT_ID ? "SET" : "NOT SET",
      COGNITO_DOMAIN: process.env.COGNITO_DOMAIN ? "SET" : "NOT SET",
      COGNITO_ISSUER: process.env.COGNITO_ISSUER ? "SET" : "NOT SET",
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    },
    request: {
      url: request.url,
      headers: Object.fromEntries(request.headers.entries()),
      searchParams: Object.fromEntries(searchParams.entries()),
    },
    appleConfig: {
      expectedRedirectUri:
        "https://" + process.env.COGNITO_CLIENT_ID + "/oauth2/idpresponse",
      clientId: "com.kickavenue.gateway-dev",
    },
  }

  return NextResponse.json(debugInfo)
}
