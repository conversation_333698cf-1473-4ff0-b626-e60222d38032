/* eslint-disable @typescript-eslint/naming-convention */

import NextAuth, { NextAuthOptions } from "next-auth"
import CognitoProvider from "next-auth/providers/cognito"
import CredentialsProvider from "next-auth/providers/credentials"
import {
  extractProviderFromCognitoToken,
  extractUserClaimsFromToken,
} from "@utils/jwt"
import { convertToCamelCase } from "@utils/misc"
import { AUTH_CONFIG } from "@app/config/auth"
import { refreshStateManager } from "@infrastructure/auth/refreshStateManager"

declare module "next-auth/jwt" {
  interface JWT {
    provider?: string
    accessToken?: string
    refreshToken?: string
    accessTokenExpires?: number
    accessTokenExpiresIn?: number
  }
}

const BASE_URL = process.env.NEXT_PUBLIC_GATEWAY_BASE_URL
const TOKEN_EXPIRATION_TIME = 10 * 10 * 60 * 1000 // 10 hours

// --- Helpers ---
function buildToken(token: any, overrides: Partial<any> = {}) {
  return { ...token, ...overrides }
}

async function verifyCognitoToken(idToken: string, provider: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_GATEWAY_BASE_URL}/api/v1/auth/verify-cognito-token`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ token: idToken, provider }),
      },
    )

    if (!response.ok) {
      throw new Error(`Gateway verification failed: ${await response.text()}`)
    }

    return await response.json()
  } catch (err) {
    console.error("verifyCognitoToken error:", err)
    return null
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
        accessToken: { label: "Access Token", type: "text" },
        refreshToken: { label: "Refresh Token", type: "text" },
        expiresIn: { label: "Expires In", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.accessToken) {
          throw new Error("Invalid credentials")
        }

        // Extract user information from the JWT token
        const userClaims = extractUserClaimsFromToken(credentials.accessToken)

        // Calculate expiration time from JWT or use provided expiresIn
        let accessTokenExpires: number
        if (credentials.expiresIn) {
          accessTokenExpires = Date.now() + parseInt(credentials.expiresIn, 10)
        } else {
          accessTokenExpires = Date.now() + TOKEN_EXPIRATION_TIME
        }

        const user = {
          id: userClaims?.userId?.toString() || credentials.email,
          email: userClaims?.email || credentials.email,
          name: userClaims?.username || credentials.email.split("@")[0],
          accessToken: credentials.accessToken,
          refreshToken: credentials.refreshToken,
          accessTokenExpires,
          accessTokenExpiresIn: parseInt(credentials.expiresIn, 10),
        }

        return user
      },
    }),
    CognitoProvider({
      clientId: process.env.COGNITO_CLIENT_ID!,
      clientSecret: "",
      issuer: process.env.COGNITO_ISSUER!,
      client: {
        token_endpoint_auth_method: "none",
      },
      authorization: {
        params: {
          scope: "openid email phone profile",
          prompt: "select_account",
          response_type: "code",
        },
      },
      checks: ["nonce"],
    }),
  ],
  callbacks: {
    async jwt({ token, account, user }) {
      if (account && user) {
        if (account.provider === "credentials") {
          const userClaims = user.accessToken
            ? extractUserClaimsFromToken(user.accessToken)
            : null

          return buildToken(token, {
            ...user,
            ...userClaims,
            id: userClaims?.userId?.toString() || user.id,
            email: userClaims?.email || user.email,
            name: userClaims?.username || user.name,
            provider: "credentials",
          })
        }

        if (account.provider === "cognito" && account.id_token) {
          const provider =
            extractProviderFromCognitoToken(account.id_token) || "unknown"

          if (provider !== "unknown") {
            const responseData = await verifyCognitoToken(
              account.id_token,
              provider,
            )

            if (responseData?.Data?.access_token) {
              const claims = extractUserClaimsFromToken(
                responseData.Data.access_token,
              )

              return buildToken(token, {
                ...user,
                id: claims?.userId?.toString() || user.id,
                email: claims?.email ?? user.email,
                name: claims?.username ?? user.name,
                accessToken: responseData.Data.access_token,
                refreshToken: responseData.Data.refresh_token,
                provider,
              })
            }
          }

          return buildToken(token, {
            ...user,
            id: token.id,
            provider,
          })
        }

        return buildToken(token, {
          ...user,
          id: token.id ?? user.id,
          provider: account.provider,
        })
      }

      // --- Subsequent requests ---
      const tokenExpires = token.accessTokenExpires || 0

      // Debug: Check if we have stale token data and force refresh state reset
      if (AUTH_CONFIG.IS_TESTING_MODE) {
        const now = Date.now()
        if (now > tokenExpires && token.refreshToken) {
          console.log("⚠️ WARNING: Using stale token data!")
          console.log(
            "  Token expired:",
            Math.round((now - tokenExpires) / 1000),
            "seconds ago",
          )
          console.log("  This might be from a previous JWT callback")
          console.log("  Forcing refresh state reset...")

          // Force reset refresh state to allow fresh attempt
          refreshStateManager.forceReset("stale token data detected")
        }
      }

      // Check if refresh is needed using state manager
      if (AUTH_CONFIG.IS_TESTING_MODE) {
        const now = Date.now()
        console.log("🔍 Token expiry check:")
        console.log("  Current time:", new Date(now).toISOString())
        console.log("  Token expires:", new Date(tokenExpires).toISOString())
        console.log("  Is expired:", now > tokenExpires)
        console.log(
          "  Time until expiry:",
          Math.round((tokenExpires - now) / 1000),
          "seconds",
        )
        console.log(
          "  Current refresh token (last 8):",
          token.refreshToken?.slice(-8) || "none",
        )
      }

      if (
        !refreshStateManager.shouldRefresh(tokenExpires, token.refreshToken)
      ) {
        if (AUTH_CONFIG.IS_TESTING_MODE) {
          console.log("🔍 shouldRefresh returned false, keeping current token")
        }
        return token
      }

      // Check if max attempts reached
      const status = refreshStateManager.getStatus()
      if (status.refreshAttempts >= status.maxAttempts) {
        console.error("🚫 Max refresh attempts reached, forcing logout")
        return {
          ...token,
          error: "MaxRefreshAttemptsExceeded",
        }
      }

      try {
        // Use state manager to handle refresh with proper mutex
        const newToken = await refreshStateManager.handleRefresh(
          token,
          refreshAccessTokenSafely,
        )

        if (AUTH_CONFIG.IS_TESTING_MODE) {
          console.log("🔄 JWT Callback: Token refresh completed")
          console.log(
            "🔍 Old refresh token (last 8):",
            token.refreshToken?.slice(-8) || "none",
          )
          console.log(
            "🔍 New refresh token (last 8):",
            newToken.refreshToken?.slice(-8) || "none",
          )
        }

        return newToken
      } catch (error) {
        console.error("❌ Token refresh failed:", error)
        return {
          ...token,
          error: "RefreshAccessTokenError",
        }
      }
    },
    async session({ session, token }) {
      if (token.error) {
        return { ...session, error: token.error as string, user: null }
      }

      session.user = {
        ...session.user,
        ...token,
        provider: token.provider ?? "unknown",
      }

      return {
        ...session,
        accessTokenExpires: token.accessTokenExpires,
      }
    },
    async redirect({ url, baseUrl }) {
      // Handle successful login redirects
      if (url.startsWith("/")) {
        // Relative URLs are safe
        return `${baseUrl}${url}`
      } else if (new URL(url).origin === baseUrl) {
        // Same origin URLs are safe
        return url
      }
      // Default to homepage for external URLs
      return baseUrl
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: AUTH_CONFIG.SESSION.STRATEGY,
    maxAge: AUTH_CONFIG.SESSION.MAX_AGE,
    updateAge: AUTH_CONFIG.SESSION.UPDATE_AGE,
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: AUTH_CONFIG.SECURITY.HTTP_ONLY,
        sameSite: AUTH_CONFIG.SECURITY.SAME_SITE,
        path: "/",
        secure: AUTH_CONFIG.SECURITY.USE_SECURE_COOKIES,
        maxAge: AUTH_CONFIG.SESSION.MAX_AGE,
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        httpOnly: AUTH_CONFIG.SECURITY.HTTP_ONLY,
        sameSite: AUTH_CONFIG.SECURITY.SAME_SITE,
        path: "/",
        secure: AUTH_CONFIG.SECURITY.USE_SECURE_COOKIES,
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: AUTH_CONFIG.SECURITY.HTTP_ONLY,
        sameSite: AUTH_CONFIG.SECURITY.SAME_SITE,
        path: "/",
        secure: AUTH_CONFIG.SECURITY.USE_SECURE_COOKIES,
      },
    },
  },
  useSecureCookies: AUTH_CONFIG.SECURITY.USE_SECURE_COOKIES,
  secret: process.env.NEXTAUTH_SECRET,
}

async function refreshAccessTokenSafely(token: any, now: number) {
  try {
    // Validate required token data
    if (!token?.refreshToken) {
      throw new Error("No refresh token available")
    }

    if (AUTH_CONFIG.IS_TESTING_MODE) {
      const status = refreshStateManager.getStatus()
      console.log("🔄 Starting token refresh at:", new Date().toISOString())
      console.log(
        "📊 Refresh attempts:",
        status.refreshAttempts,
        "/",
        status.maxAttempts,
      )
      console.log(
        "⏰ Time since last refresh:",
        now - status.lastRefreshTime,
        "ms",
      )
      console.log(
        "🕒 Time since last successful refresh:",
        now - status.lastSuccessfulRefresh,
        "ms",
      )
      console.log(
        "🔑 Refresh token (last 8 chars):",
        token?.refreshToken?.slice(-8) || "none",
      )
    }

    const refreshTokenUrl = `${BASE_URL}/api/v1/user/auth/refresh-token`
    const response = await fetch(refreshTokenUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token?.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        refresh_token: token?.refreshToken,
      }),
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    const resJson = await response.json()

    if (!response.ok) {
      // Handle specific error cases
      if (response.status === 400) {
        console.error("🚫 Refresh token already used or invalid (400)")
        throw new Error("Refresh token already used")
      }
      if (response.status === 401) {
        console.error("🚫 Refresh token expired or invalid (401)")
        throw new Error("Refresh token expired or invalid")
      }
      if (response.status === 429) {
        console.error("🚫 Too many refresh attempts (429)")
        throw new Error("Too many refresh attempts")
      }
      console.error(`🚫 Token refresh failed with status: ${response.status}`)
      throw new Error(`Token refresh failed: ${response.status}`)
    }

    const newResJson = convertToCamelCase(resJson) as any
    const data: any = newResJson?.data ? newResJson.data : newResJson

    // Validate response data
    if (!data?.accessToken) {
      throw new Error("Invalid refresh response: missing access token")
    }

    const expiresIn = data?.expiresIn ?? AUTH_CONFIG.TOKEN.EXPIRATION

    const refreshedToken = {
      ...token,
      accessToken: data.accessToken,
      refreshToken: data.refreshToken || token.refreshToken,
      accessTokenExpires: now + Number(expiresIn),
      accessTokenExpiresIn: Number(expiresIn),
      error: undefined, // Clear any previous errors
    }

    if (AUTH_CONFIG.IS_TESTING_MODE) {
      console.log(
        "🔍 API Response refresh token (last 8):",
        (data.refreshToken || "none").slice(-8),
      )
      console.log(
        "🔍 Final refresh token (last 8):",
        refreshedToken.refreshToken?.slice(-8) || "none",
      )
    }

    if (AUTH_CONFIG.IS_TESTING_MODE) {
      console.log("✅ Token refresh successful at:", new Date().toISOString())
      console.log(
        "🕒 New token expires at:",
        new Date(refreshedToken.accessTokenExpires).toISOString(),
      )
    }

    return refreshedToken
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown refresh error"

    if (AUTH_CONFIG.IS_TESTING_MODE) {
      console.error("❌ Token refresh failed:", errorMessage)
    }

    // Return error state that NextAuth can handle
    throw new Error(errorMessage)
  }
}

export default NextAuth(authOptions)
