import { NextRequest, NextResponse } from "next/server"

export async function GET() {
  try {
    // Construct the Cognito logout URL
    const cognitoDomain = process.env.COGNITO_DOMAIN || ""
    const clientId =
      process.env.COGNITO_CLIENT_ID || "ids9bfohmg9vh58sp56uhlqd1"
    const logoutUri = process.env.NEXTAUTH_URL || "http://localhost:4000"

    // Ensure cognitoDomain doesn't already have https:// prefix
    const cleanCognitoDomain = cognitoDomain.replace(/^https?:\/\//, "")
    const cognitoLogoutUrl = `https://${cleanCognitoDomain}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(logoutUri)}`

    return NextResponse.json({
      success: true,
      logoutUrl: cognitoLogoutUrl,
      redirect: false,
      debug: {
        cleanCognitoDomain,
        clientId,
        logoutUri,
        cognitoIssuer: process.env.COGNITO_ISSUER,
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Failed to generate logout URL",
        details: error,
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { logoutUrl } = body

    // If a logout URL is provided, redirect to it
    if (logoutUrl) {
      return NextResponse.json({
        success: true,
        logoutUrl,
        redirect: true,
      })
    }

    // Construct the Cognito logout URL
    const cognitoDomain = process.env.COGNITO_DOMAIN || ""
    const clientId =
      process.env.COGNITO_CLIENT_ID || "ids9bfohmg9vh58sp56uhlqd1"
    const logoutUri = process.env.NEXTAUTH_URL || "http://localhost:4000"

    // Ensure cognitoDomain doesn't already have https:// prefix
    const cleanCognitoDomain = cognitoDomain.replace(/^https?:\/\//, "")
    const cognitoLogoutUrl = `https://${cleanCognitoDomain}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(logoutUri)}`

    return NextResponse.json({
      success: true,
      logoutUrl: cognitoLogoutUrl,
      redirect: false,
      debug: {
        cleanCognitoDomain,
        clientId,
        logoutUri,
        cognitoIssuer: process.env.COGNITO_ISSUER,
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Failed to generate logout URL",
        details: error,
      },
      { status: 500 },
    )
  }
}
