import { NextRequest, NextResponse } from "next/server"

const { NEXT_PUBLIC_GATEWAY_BASE_URL, NEXT_PUBLIC_FRONTEND_URL } = process.env

// Disable static generation for this route
export const dynamic = "force-dynamic"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const code = searchParams.get("code")
    const state = searchParams.get("state")
    const error = searchParams.get("error")
    const debug = searchParams.get("debug")

    // If debug mode is enabled, redirect to debug page
    if (debug === "true") {
      return NextResponse.redirect(
        `${NEXT_PUBLIC_FRONTEND_URL}/auth/debug?${searchParams.toString()}`,
      )
    }

    // Check for OAuth errors
    if (error) {
      return NextResponse.redirect(
        `${NEXT_PUBLIC_FRONTEND_URL}/login?error=oauth_error&message=${error}`,
      )
    }

    if (!code) {
      return NextResponse.redirect(
        `${NEXT_PUBLIC_FRONTEND_URL}/login?error=no_code`,
      )
    }

    // Validate state parameter
    const storedState = request.cookies.get("oauth_state")?.value
    if (!storedState || storedState !== state) {
      return NextResponse.redirect(
        `${NEXT_PUBLIC_FRONTEND_URL}/login?error=invalid_state`,
      )
    }

    // Redirect to the Gateway Service callback endpoint
    // NEXT_PUBLIC_GATEWAY_BASE_URL points to the Gateway Service (backend)
    const gatewayCallbackUrl = `${NEXT_PUBLIC_GATEWAY_BASE_URL}/api/v1/auth/callback?code=${code}&state=${state}`

    const response = NextResponse.redirect(gatewayCallbackUrl)

    // Clear the OAuth state cookie
    response.cookies.set("oauth_state", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0,
    })

    return response
  } catch (error) {
    return NextResponse.redirect(
      `${NEXT_PUBLIC_FRONTEND_URL}/login?error=callback_failed`,
    )
  }
}
