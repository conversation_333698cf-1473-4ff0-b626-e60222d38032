import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  const envVars = {
    NEXT_PUBLIC_GATEWAY_BASE_URL: process.env.NEXT_PUBLIC_GATEWAY_BASE_URL,
    NEXT_PUBLIC_CONSOLE_BASE_URL: process.env.NEXT_PUBLIC_CONSOLE_BASE_URL,
    NEXT_PUBLIC_FRONTEND_URL: process.env.NEXT_PUBLIC_FRONTEND_URL,
    NEXT_PUBLIC_GA_ID: process.env.NEXT_PUBLIC_GA_ID,
    NEXT_PUBLIC_MIDTRANS_CLIENT_KEY: process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY,
    NODE_ENV: process.env.NODE_ENV,
  }

  // Also get the resolved values from the config
  let resolvedUrls = {}
  try {
    const { GATEWAY_BASE_URL, CONSOLE_BASE_URL } = await import(
      "@app/config/api"
    )
    resolvedUrls = {
      GATEWAY_BASE_URL,
      CONS<PERSON><PERSON>_BASE_URL,
    }
  } catch (error) {
    resolvedUrls = { error: "Could not resolve API config" }
  }

  const debugInfo = {
    timestamp: new Date().toISOString(),
    environment: envVars,
    resolvedUrls,
    request: {
      url: request.url,
      headers: Object.fromEntries(request.headers.entries()),
    },
  }

  return NextResponse.json(debugInfo)
}
