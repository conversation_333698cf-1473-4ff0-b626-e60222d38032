"use client"

import { QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { SessionProvider } from "next-auth/react"
import { ReactNode } from "react"
import { Al<PERSON> } from "@kickavenue/ui/dist/src/components"
import { I18nProvider } from "@app/i18n"
import useToast from "@hooks/useToast"
import { queryClient } from "@lib/query-client"

import { MemberProvider } from "./providers/MemberProvider"
import { useAutoScrollToTop } from "./hooks/useAutoScrollToTop"

interface ProvidersProps {
  children: ReactNode
  lang?: string
}

export function Providers({ children, lang }: ProvidersProps) {
  const { showToast, message, variant } = useToast()

  useAutoScrollToTop()

  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider
        refetchInterval={20}
        refetchOnWindowFocus={false}
        refetchWhenOffline={false}
      >
        <MemberProvider />
        {showToast && (
          <Alert
            className="fixed left-1/2 top-4 z-[9999] -translate-x-1/2"
            subTitle={message}
            variant={variant}
            isIcon
          />
        )}
        <I18nProvider lang={lang}>{children}</I18nProvider>
      </SessionProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}
