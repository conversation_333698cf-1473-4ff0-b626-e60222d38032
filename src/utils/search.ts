import { GetAllCategory } from "@application/usecases/getAllCategory"
import { CategoryApiRepository } from "@infrastructure/repositories/categoryApiRepository"
import { TFilterProducts, TProductFilterKey } from "types/product.type"
import { TSearchParams } from "types/searchParams.type"
import { TItemCondition, TItemConditionFilter } from "types/listingItem.type"
import { MiscConstant } from "@constants/misc"

import { stringToNumberArray, stringToStringArray } from "./string.utils"

const { PriceRange } = TProductFilterKey

export function getPriceRangeFilter(filterValue?: string) {
  function getPrice(price?: string, fallback?: string) {
    if (!price) return fallback || "0"
    if (isNaN(Number(price))) return fallback || "0"
    return Number(price).toLocaleString("en-US")
  }

  if (!filterValue) return ""
  const priceRange = filterValue?.split("-")

  const lowerBound = getPrice(priceRange?.[0], "0")
  const upperBound = getPrice(priceRange?.[1], "~")

  return `IDR ${lowerBound} - ${upperBound}`
}

export function getAppliedFilters(filter: TSearchParams) {
  const appliedFilters = [] as Record<string, string>[]
  const skippedKeys = [
    "categoryID",
    "brandID",
    "subcategoryID",
    "sizeID",
    "sortBy",
  ]
  for (const [key, value] of Object.entries(filter)) {
    if (skippedKeys.includes(key)) {
      continue
    }
    if (key === PriceRange && value) {
      appliedFilters.push({ key, value: getPriceRangeFilter(value) })
      continue
    }
    if (key === "keyword" && value) {
      appliedFilters.push({ key, value: `Search: ${value}` })
      continue
    }
    if ((value as string).includes(",")) {
      const values = (value as string).split(",")
      for (const v of values) {
        appliedFilters.push({ key, value: v })
      }
      continue
    }
    appliedFilters.push({ key, value })
  }
  if (appliedFilters.length === 0) {
    return []
  }
  return [{ key: "clear-all", value: "Clear All" }, ...appliedFilters]
}

export function getCategories() {
  const r = new CategoryApiRepository()
  const u = new GetAllCategory(r)

  return u.execute({})
}

export function buildSearchFilter(
  searchParams: TSearchParams,
): TFilterProducts {
  const filter: TFilterProducts = {}

  function upperAndUnderScore(value: string) {
    return value.toUpperCase()?.replace(/[ -]/g, "_")
  }

  function mapCondition(condition: string) {
    if (condition === TItemConditionFilter.NnPercentPerfect) {
      return TItemCondition.Perfect
    }
    return upperAndUnderScore(condition)
  }

  if (searchParams?.keyword) filter.search = searchParams.keyword
  if (searchParams?.gender) {
    filter.gender = stringToStringArray(searchParams.gender)?.map(
      upperAndUnderScore,
    )
  }
  if (searchParams?.shippingMethod) {
    filter.shippingMethod = stringToStringArray(
      searchParams.shippingMethod,
    )?.map((i) => i?.toUpperCase())
  }
  if (searchParams?.categoryID) {
    filter.categoryID = stringToNumberArray(searchParams.categoryID)
  }
  if (searchParams?.subcategoryID) {
    filter.subcategoryID = stringToNumberArray(searchParams.subcategoryID)
  }
  if (searchParams?.sizeID) {
    filter.sizeID = stringToNumberArray(searchParams.sizeID)
  }
  if (searchParams?.condition) {
    filter.condition = stringToStringArray(searchParams.condition)?.map(
      mapCondition,
    )
  }

  if (searchParams?.priceRange) {
    const [priceLow, priceHigh] = searchParams?.priceRange?.split("-") || [0, 0]
    filter.priceLow = priceLow ? Number(priceLow) : undefined
    filter.priceHigh = priceHigh ? Number(priceHigh) : undefined
  }

  if (searchParams?.categoryId) {
    filter.categoryId = stringToNumberArray(searchParams.categoryId)
  }
  if (searchParams?.brandId) {
    filter.brandId = stringToNumberArray(searchParams.brandId)
  }
  if (searchParams?.subcategoryId) {
    filter.subcategoryId = stringToNumberArray(searchParams.subcategoryId)
  }
  if (searchParams?.sizeId) {
    filter.sizeId = stringToNumberArray(searchParams.sizeId)
  }

  filter.sortBy =
    searchParams?.sortBy || MiscConstant.SORT_BY_OPTIONS.NEWLY_ADDED.value

  return {
    ...filter,
    categoryName: searchParams?.category ? [searchParams?.category] : undefined,
  }
}
