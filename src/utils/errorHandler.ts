import { AxiosError } from "axios"
import { HttpStatusCode } from "types/network.type"

/**
 * Centralized error handling utility for authentication and API errors
 * Provides consistent error handling patterns across the application
 */
export class ErrorHandler {
  /**
   * Handle authentication errors consistently
   * @param error - Error object
   * @param context - Context where the error occurred
   * @returns Standardized error response
   */
  static handleAuthError(
    error: any,
    context: string = "authentication",
  ): {
    success: false
    error: string
    statusCode?: number
    context: string
  } {
    console.error(`ErrorHandler: ${context} error:`, error)

    // Handle Axios errors
    if (error instanceof AxiosError) {
      const status = error.response?.status
      const message = error.response?.data?.Message || error.message

      switch (status) {
        case HttpStatusCode.Unauthorized:
          return {
            success: false,
            error: "Authentication failed. Please log in again.",
            statusCode: status,
            context,
          }
        case HttpStatusCode.Forbidden:
          return {
            success: false,
            error:
              "Access denied. You don't have permission to perform this action.",
            statusCode: status,
            context,
          }
        case HttpStatusCode.BadRequest:
          return {
            success: false,
            error: message || "Invalid request. Please check your input.",
            statusCode: status,
            context,
          }
        case HttpStatusCode.InternalServerError:
          return {
            success: false,
            error: "Server error. Please try again later.",
            statusCode: status,
            context,
          }
        default:
          return {
            success: false,
            error: message || "An unexpected error occurred.",
            statusCode: status,
            context,
          }
      }
    }

    // Handle network errors
    if (error.request && !error.response) {
      return {
        success: false,
        error: "Network error. Please check your connection and try again.",
        context,
      }
    }

    // Handle other errors
    return {
      success: false,
      error: error.message || "An unexpected error occurred.",
      context,
    }
  }

  /**
   * Handle token refresh errors specifically
   * @param error - Error object
   * @returns Standardized error response
   */
  static handleTokenRefreshError(error: any): {
    success: false
    error: string
    shouldRedirectToLogin: boolean
  } {
    console.error("ErrorHandler: Token refresh error:", error)

    if (error instanceof AxiosError) {
      const status = error.response?.status

      switch (status) {
        case HttpStatusCode.Unauthorized:
        case HttpStatusCode.Forbidden:
          return {
            success: false,
            error: "Session expired. Please log in again.",
            shouldRedirectToLogin: true,
          }
        case HttpStatusCode.BadRequest:
          return {
            success: false,
            error: "Invalid refresh token. Please log in again.",
            shouldRedirectToLogin: true,
          }
        default:
          return {
            success: false,
            error: "Token refresh failed. Please try again.",
            shouldRedirectToLogin: false,
          }
      }
    }

    return {
      success: false,
      error: "Token refresh failed. Please try again.",
      shouldRedirectToLogin: false,
    }
  }

  /**
   * Handle API response errors consistently
   * @param response - API response object
   * @param context - Context where the error occurred
   * @returns Standardized error response
   */
  static handleApiResponseError(
    response: any,
    context: string = "API",
  ): {
    success: false
    error: string
    statusCode?: number
    context: string
  } {
    const message =
      response?.Message || response?.message || "An error occurred"
    const statusCode = response?.Code || response?.statusCode

    return {
      success: false,
      error: message,
      statusCode,
      context,
    }
  }

  /**
   * Log error with context for debugging
   * @param error - Error object
   * @param context - Context where the error occurred
   * @param additionalInfo - Additional information to log
   */
  static logError(
    error: any,
    context: string,
    additionalInfo?: Record<string, any>,
  ): void {
    const errorInfo = {
      context,
      error: error.message || error,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      ...additionalInfo,
    }

    console.error(`ErrorHandler: ${context}`, errorInfo)

    // In production, you might want to send this to a logging service
    if (process.env.NODE_ENV === "production") {
      // Example: send to logging service
      // loggingService.log(errorInfo)
    }
  }
}
