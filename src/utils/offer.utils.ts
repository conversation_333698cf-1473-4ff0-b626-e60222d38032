import { snakeCase } from "lodash"
import { TOffer, TOfferFilter } from "types/offer.type"
import { TUpdatePriceAction } from "types/misc.type"
import { EBuyingOfferFilterTabName } from "types/buyingOffer.type"
import { TItemCondition, TItemConditionFilter } from "types/listingItem.type"

import {
  isEmpty,
  buildSortByQueryString,
  formatPrice,
  formatStringWithSpaces,
} from "./misc"

export function upperAndUnderScore(value: string) {
  return value.toUpperCase()?.replace(/[ -]/g, "_")
}

export function mapCondition(condition: string) {
  if (condition === TItemConditionFilter.NnPercentPerfect) {
    return TItemCondition.Perfect
  }
  return upperAndUnderScore(condition)
}

export function getListParams(filter?: TOfferFilter) {
  if (!filter) return {}

  const skippedFields = ["sort", "sortBy", "totalPages", "pageSize"]
  const filterParams = {} as any

  Object.keys(filter).forEach((key) => {
    const typedKey = key as keyof TOfferFilter
    if (!skippedFields.includes(typedKey) && !isEmpty(filter[typedKey])) {
      filterParams[snakeCase(typedKey)] = filter[typedKey]
    }
  })

  if (filter.sortBy) {
    filterParams.sort = buildSortByQueryString(filter.sortBy)
  }

  if (!isEmpty(filter.pageSize)) {
    filterParams.pageSize = filter.pageSize
  }

  return filterParams
}

export function getUpdatePriceSuccessMsg({
  selectedRowKeys,
  updatePrice,
  updatePriceAction,
}: {
  selectedRowKeys: number[]
  updatePriceAction: TUpdatePriceAction
  updatePrice: number
}) {
  const type = updatePriceAction === "update-to" ? "to" : "by"

  return `Updated offer prices for ${selectedRowKeys.length} items ${type} ${formatPrice(updatePrice, null, "IDR")}`
}

export function getOfferCondition(offer?: TOffer | null) {
  if (!offer) return ""

  if (offer.isNewNoDefect) {
    return "Brand New, Perfect Box"
  }

  return "99% Perfect"
}

export function getOfferConditionNew(offer?: TOffer | null) {
  if (!offer) return ""

  const itemCondition =
    formatStringWithSpaces(offer?.itemCondition || "") || "-"
  const packagingCondition =
    formatStringWithSpaces(offer?.packagingCondition || "") || "-"

  return `${itemCondition}, ${packagingCondition}`
}

export function enumerizeFilterTabName(tabName: string) {
  return tabName
    ?.replaceAll(" ", "_")
    ?.toUpperCase() as EBuyingOfferFilterTabName
}

export function getConditionFilter(query?: string | null) {
  if (!query) return {}

  const filter: Partial<TOfferFilter> = {}
  if (query.includes(TItemConditionFilter.BrandNew)) {
    filter.isNewNoDefect = true
  }
  if (query.includes(TItemConditionFilter.NnPercentPerfect)) {
    filter.isNewNoDefect = false
  }
  return filter
}
