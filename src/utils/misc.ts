import { AxiosError } from "axios"
import camelCase from "lodash/camelCase"
import capitalize from "lodash/capitalize"
import snakeCase from "lodash/snakeCase"
import kebabCase from "lodash/kebabCase"
import moment, { Moment } from "moment"
import { FilterItem } from "@components/Filter/types"
import { BANK_ACRONYMS } from "@constants/bank"
import { MiscConstant } from "@constants/misc"
import { TSort } from "types/apiResponse.type"
import { TBuyingOfferFilter } from "types/buyingOffer.type"
import { TCountry } from "types/country.type"
import { TListingItemFilter } from "types/listingItem.type"
import {
  BreadcrumbItem,
  EOtpInputDirection,
  EOtpInputType,
  TConsignmentDashboardTab,
  TSellingDashboardTab,
} from "types/misc.type"
import { Product } from "types/product.type"
import { TSize } from "types/size.type"
import { TStripePrice } from "types/stripe.type"
import { TTransactionDetailFilter } from "types/transactionDetail.type"

import { formatCurrency, formatNumberWithSeparator } from "./separator"
import nextConfig from "next.config"

export function getBreadCrumbProductDetail(): BreadcrumbItem[] {
  return [
    { name: "Sneakers", path: "/" },
    { name: "On Running", path: "/product" },
    { name: "Cloudmonster All Black", path: "/product/detail" },
  ]
}

export function convertToCamelCase(
  obj: unknown,
  seen = new WeakSet(),
): unknown {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => convertToCamelCase(item, seen))
  } else if (typeof obj === "object") {
    // Check for circular references
    if (seen.has(obj)) {
      return "[Circular Reference]"
    }
    seen.add(obj)

    const result: Record<string, unknown> = {}
    for (const key of Object.keys(obj)) {
      const camelKey = camelCase(key)
      result[camelKey] = convertToCamelCase(
        (obj as Record<string, unknown>)[key],
        seen,
      )
    }
    return result
  }
  return obj
}

export function convertToSnakeCase(
  obj: unknown,
  seen = new WeakSet(),
): unknown {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => convertToSnakeCase(item, seen))
  } else if (typeof obj === "object") {
    // Check for circular references
    if (seen.has(obj)) {
      return "[Circular Reference]"
    }
    seen.add(obj)

    const result: Record<string, unknown> = {}
    for (const key of Object.keys(obj)) {
      const snakeKey = snakeCase(key)
      result[snakeKey] = convertToSnakeCase(
        (obj as Record<string, unknown>)[key],
        seen,
      )
    }
    return result
  }
  return obj
}

export function convertText(str: string) {
  return {
    toCamelCase: () => camelCase(str),
    toKebabCase: () => kebabCase(str),
    toSnakeCase: () => snakeCase(str),
    toSnakeUPCase: () => snakeCase(str).toUpperCase(),
  }
}

export function formatDate(
  date?: string | Moment | null,
  format?: string,
): string {
  if (!date) {
    return "-"
  }

  if (typeof date === "string") {
    const d = new Date(date)
    return d.toLocaleDateString("en-gb")
  }

  return moment(date).format(format || MiscConstant.DATE_FORMAT)
}

export function formatDateWIB(date?: string | Moment, format?: string): string {
  if (!date) {
    return "-"
  }
  const f = format || "DD MMMM YYYY, HH:mm"
  return `${moment.utc(date).utcOffset(7).format(f)} WIB`
}

export function formatDateFull(date: string): string {
  const d = new Date(date)
  const options: Intl.DateTimeFormatOptions = {
    day: "numeric",
    month: "long",
    year: "numeric",
  }
  return d.toLocaleDateString("en-gb", options)
}

export function getHeaderTopMenus(): string[] {
  return ["Home", "Market", "Sell"]
}

export function camelToSnake(str: string): string {
  return str.replace(/([A-Z])/g, "_$1").toUpperCase()
}

export function dashToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

export function capitalizeFirstLatter(str: string): string {
  const arr = str?.split(" ")
  const capitalized = arr?.map((word) => capitalize(word))
  return capitalized?.join(" ")
}

export function capitalizeWords(str: string): string {
  const capitalized = str
    .split(" ")
    ?.map((word) => capitalize(word))
    .join(" ")
    .split("_")
    .map((word) => capitalize(word))
  return capitalized?.join(" ")
}

export function capitalizeFirstLetterForBank(str: string): string {
  if (!str) return ""

  const arr = str.split(" ")
  const capitalized = arr.map((word) => {
    const upperWord = word.toUpperCase()
    return BANK_ACRONYMS.includes(upperWord) ? upperWord : capitalize(word)
  })

  return capitalized.join(" ")
}

export function checkValidUrl(url?: string): boolean {
  if (!url) {
    return false
  }

  try {
    const res = new URL(url)
    return Boolean(res)
  } catch (e) {
    return false
  }
}

export const splitPhoneNumber = (phoneNumber: string) => {
  const regex = /^(\+\d{1,2})(\d+)/
  const matches = phoneNumber?.match(regex)
  if (matches) {
    return {
      number: matches[2],
      countryCode: { label: matches[1], value: matches[1] },
    }
  }
  return {
    number: "",
    countryCode: { label: "", value: "" },
  }
}

export type Item = Record<string, unknown>

interface ItemList {
  label: string
  value: unknown
}

export const convertToItemList = (
  items: Item[],
  valueProperty: string,
  labelProperty: string,
): ItemList[] => {
  return items?.map((item) => ({
    label: String(item[labelProperty]),
    value: String(item[valueProperty]),
  }))
}

export function getSellingDashboardTabs() {
  const tabs = [
    { key: "current", value: "Current" },
    { key: "in-progress", value: "In Progress" },
    { key: "selling-history", value: "History" },
  ] as { key: TSellingDashboardTab; value: string }[]
  return tabs
}

export function getConsignmentDashboardTabs() {
  const tabs = [
    { key: "pending", value: "Pending" },
    { key: "active", value: "Active" },
    { key: "cg-in-progress", value: "In Progress" },
    { key: "history", value: "History" },
  ] as { key: TConsignmentDashboardTab; value: string }[]
  return tabs
}

export function formatDateObj(date: Date | undefined | null): string {
  if (!date) {
    return "-"
  }
  return moment(date).format(MiscConstant.DATE_FORMAT)
}

export function formatDateHourMinute(date: Date | string): string {
  return moment(date).format("YYYY-MM-DD HH:mm:ss")
}

export function formatStringWithSpaces(value?: string, fallback?: string) {
  if (!value) {
    return fallback || ""
  }
  const spaced = value?.replace(/_/g, " ")
  const lower = spaced?.toLowerCase()
  return capitalizeFirstLatter(lower)
}

function isDomainAllowed(url: string): boolean {
  const allowedDomains =
    nextConfig.images?.remotePatterns?.map((pattern) => pattern.hostname) || []

  try {
    const domain = new URL(url).hostname
    return allowedDomains.includes(domain)
  } catch {
    return false
  }
}

export function isValidUrl(url: string) {
  try {
    // eslint-disable-next-line no-new
    new URL(url)
    return isDomainAllowed(url)
  } catch (e) {
    return false
  }
}

export function getProductImageUrl(product: Product | string) {
  let url = ""
  if (typeof product === "string") {
    url = product
  } else if (product?.images && product.images instanceof Array) {
    url = product?.images?.[0] as string
  } else if (product?.image) {
    url = product?.image
  }

  if (!isValidUrl(url)) {
    return "https://d5ibtax54de3q.cloudfront.net/eyJidWNrZXQiOiJraWNrYXZlbnVlLWFzc2V0cyIsImtleSI6InByb2R1Y3RzLzEwNjk4NC9lZGIwMWEyM2JlY2I4YzM3MTRmYzFkN2EyMTNmNzM2Ny5wbmciLCJlZGl0cyI6eyJyZXNpemUiOnsid2lkdGgiOjE0MDB9fX0="
  }

  if (url.includes(MiscConstant.S3_BUCKET_SUFFIX)) {
    const cloudFrontUrl = convertS3UrlToCloudFront(url)
    return cloudFrontUrl
  }

  return url
}

export function mapFilterItem(
  item: Record<string, unknown>,
  idKey: string,
  nameKey: string,
): FilterItem {
  return {
    id: item?.[idKey] as string | number,
    name: item?.[nameKey] as string,
  }
}

export function logError(error: AxiosError) {
  if (process.env.NODE_ENV !== "development") {
    return
  }

  const logDetail = {
    status: error.response?.status,
    statusText: error.response?.statusText,
    url: error.config?.url,
    method: error.config?.method?.toUpperCase(),
    message: error.message,
    ...(error.response?.data || {}),
  }

  // Skip logging for common auth errors after logout (401/403)
  if (logDetail.status === 401 || logDetail.status === 403) {
    console.warn(
      `API call failed (${logDetail.status}): ${logDetail.url} - This may be expected after logout`,
    )
    return
  }

  // Only log if there's meaningful error data
  if (Object.keys(logDetail).length > 0 && logDetail.status) {
    console.error("API call error:", logDetail)
  } else {
    console.error("API call error: Unknown error occurred", error)
  }
}

export function getSellerListingFilterParams(params: TListingItemFilter) {
  if (!params) {
    return {}
  }

  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    totalPages,
    sortBy,
    pageSize,
    hasQuantity,
    consignmentToggle,
    categories,
    isAddOn,
    isActive,
    ...rest
  } = params
  const filter = Object.fromEntries(
    Object.entries(rest).filter(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ([_, value]) => !isEmpty(value),
    ),
  )
  const result = {
    ...filter,
    ...(sortBy && { sortBy }),
    ...(pageSize && { pageSize }),
    ...(isAddOn && { isAddOn }),
    ...(isActive && { isActive }),
    hasQuantity,
    consignmentToggle,
  } as any

  if (categories) {
    result.categoryName = categories
  }
  return result
}

export function getTransactionDetailFilterParams(
  params: TTransactionDetailFilter | TBuyingOfferFilter,
) {
  if (!params) {
    return {}
  }
  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    totalPages,
    sortBy,
    categoryId,
    brandId,
    ...rest
  } = params
  const filter = Object.fromEntries(
    Object.entries(rest).filter(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ([_, value]) => !isEmpty(value),
    ),
  )
  const result = {
    ...(convertToCamelCase(filter) as Record<string, unknown>),
    ...(sortBy && { sortBy }),
    ...(categoryId && { categoryID: categoryId }),
    ...(brandId && { brandID: brandId }),
  }
  return result
}

export function joinArrayToString(array: string[]) {
  if (array?.length === 1) {
    return array[0]
  }
  return array.join(",")
}

export const convertKgToGrams = (value: number | undefined) => {
  if (value) {
    return value * 1000
  }
  return 0
}

export function formatPrice(
  price: number,
  country: TCountry | null | undefined = null,
  currency = "IDR",
  fallback?: string,
): string {
  if (!country && !currency) {
    return fallback || "-"
  }

  if (!price) {
    return fallback || "-"
  }

  let symbol = "-"

  if (country?.currency) {
    symbol = country?.currency
  } else if (currency) {
    symbol = currency
  }

  return `${symbol} ${price?.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`
}

export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export function isEmpty(value: any) {
  return value === null || value === undefined || value === ""
}

export function convertS3UrlToCloudFront(s3Url: string): string {
  const cloudFrontDomain =
    MiscConstant.CONVERT_S3_URL_TO_CLOUD_FRONT.CLOUD_FRONT_DOMAIN
  const s3BaseUrl = MiscConstant.CONVERT_S3_URL_TO_CLOUD_FRONT.S3_BASE_URL

  if (s3Url?.startsWith(s3BaseUrl)) {
    const convertedUrl = s3Url.replace(s3BaseUrl, cloudFrontDomain)
    return convertedUrl
  }

  return s3Url
}

export function getSizeTypeLabel(sizeType: string, size?: TSize) {
  const sizeByType = size?.[sizeType as keyof TSize] as string
  if (sizeByType?.toLowerCase()?.includes("all")) {
    return ""
  }
  return sizeType?.toUpperCase()
}

export function formatStripePrice(
  price?: TStripePrice | null,
  currency = "IDR",
  separator = ",",
) {
  const minUnitVal = price?.minUnitVal

  if (!price || !minUnitVal) {
    return `${currency} 0`
  }

  const formattedAmount =
    currency.toUpperCase() === "IDR"
      ? (minUnitVal / 1000).toFixed(0)
      : (minUnitVal / 1000).toFixed(3)
  const formattedPrice = formatCurrency(
    Number(formattedAmount),
    separator,
    currency,
  )
  return formattedPrice
}

export function formatPriceMinUnitVal(price: number) {
  const formatedPrice = price / MiscConstant.DIVIDER_PRICER
  return Number(formatedPrice.toFixed(3))
}

export const handleNumericInput = (
  e: React.KeyboardEvent<HTMLInputElement>,
) => {
  if (!/[0-9]/.test(e.key) && e.key !== "Backspace" && e.key !== "Delete") {
    e.preventDefault()
  }
}

export function buildSortByQueryString(sortParams: TSort[]): string {
  return sortParams
    .map((param) => `${param.sortBy},${param.sortOrder}`)
    .join(";")
}

export function buildQueryStringBySortBy(sortBy: string): TSort[] {
  return sortBy.split(";").map((sort) => {
    const [sortBy, sortOrder] = sort.split(",")
    return { sortBy, sortOrder }
  }) as TSort[]
}

export function toPayloadFormData<T>(
  data: Record<string, any>,
  excludeKeys: (keyof T)[] = [],
) {
  const formData = new FormData()
  // separate exluded keys from data
  const includedData = Object.fromEntries(
    Object.entries(data).filter(
      ([key]) => !excludeKeys.includes(key as keyof T),
    ),
  )
  const payload = includedData
  formData.append("payload", JSON.stringify(payload))
  return formData
}

export function moveOtpInputFocus({
  index,
  type,
  direction,
}: {
  index: number
  type: EOtpInputType
  direction: EOtpInputDirection
}) {
  const dataIndex =
    direction === EOtpInputDirection.Next ? index + 1 : index - 1
  const nextInput = document.querySelector(
    `input[name=${type}-otp-${dataIndex}]`,
  ) as HTMLInputElement
  nextInput?.focus()
}

export const updateUrlWithLanguage = (langCode: string): string => {
  const currentUrl = window.location.href
  const domain = window.location.origin
  const path = currentUrl.replace(domain, "")

  const cleanPath = path.replace(/^\/[a-z]{2}(?=\/|$)/, "")

  return `${domain}/${langCode}${cleanPath}`
}

export function getMinUnitVal(price?: TStripePrice | null) {
  if (!price || !price.minUnitVal) {
    return 0
  }
  return price.minUnitVal / MiscConstant.DIVIDER_PRICER
}

export function getStripAmount(stripe?: TStripePrice | number | string | null) {
  if (!stripe) {
    return 0
  }
  if (typeof stripe === "string" && !isNaN(Number(stripe))) {
    return Number(stripe) / MiscConstant.DIVIDER_PRICER
  }
  if (typeof stripe === "number") {
    return stripe / MiscConstant.DIVIDER_PRICER
  }
  return (stripe as TStripePrice)?.minUnitVal / MiscConstant.DIVIDER_PRICER
}

export function makeStripeAmount(amount?: number) {
  if (!amount) {
    return 0
  }
  return amount * MiscConstant.DIVIDER_PRICER
}

export function formatCurrencyStripe({
  price,
  currency = "IDR",
  separator = ",",
  type = "text",
}: {
  price?: TStripePrice | null
  currency?: string
  separator?: string
  type?: "text" | "minUnitVal"
}) {
  if (!price) {
    return "-"
  }
  if (type === "minUnitVal") {
    return formatStripePrice(price, currency, separator)
  }
  return `${currency} ${formatNumberWithSeparator(
    Number(price?.amountText),
    separator,
  )}`
}

export const handleFileChange = async (
  event: React.ChangeEvent<HTMLInputElement> | null,
  index: number,
  files: string[],
  setFiles: (files: string[]) => void,
  setOverallAppearanceImage: (files: string[]) => void,
  setShowToast: (
    show: boolean,
    message: string,
    type?: "success" | "danger",
  ) => void,
) => {
  if (!event?.target?.files) return

  const uploadedFiles = Array.from(event.target.files)
  const maxSizeByte = 5 * 1024 * 1024
  const maxFiles = 10

  // Check if adding these files would exceed the limit
  const currentFileCount = files.filter(Boolean).length
  if (currentFileCount + uploadedFiles.length > maxFiles) {
    setShowToast(true, "Maximum 10 images allowed", "danger")
    return
  }

  // Check file sizes
  const oversizedFiles = uploadedFiles.filter((file) => file.size > maxSizeByte)
  if (oversizedFiles.length > 0) {
    setShowToast(true, "Some images exceed 2MB limit", "danger")
    return
  }

  try {
    const fileReadPromises = uploadedFiles.map((file) => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader()
        reader.onloadend = () => {
          resolve(reader.result as string)
        }
        reader.onerror = reject
        reader.readAsDataURL(file)
      })
    })

    const base64Results = await Promise.all(fileReadPromises)

    // Create a new array with existing files
    const newFiles = [...files]
    while (newFiles.length < 10) {
      newFiles.push("")
    }

    // For first upload or when clicking empty slot, use that slot
    let currentIndex = index
    if (!files.some(Boolean) || !files[index]) {
      for (const base64 of base64Results) {
        if (currentIndex < 10) {
          newFiles[currentIndex] = base64
          currentIndex++
        }
      }
    } else {
      // Find first empty slot after the clicked position
      for (const base64 of base64Results) {
        while (currentIndex < 10 && newFiles[currentIndex] !== "") {
          currentIndex++
        }
        if (currentIndex < 10) {
          newFiles[currentIndex] = base64
          currentIndex++
        }
      }
    }

    setFiles(newFiles)
    setOverallAppearanceImage(newFiles)
  } catch (error) {
    setShowToast(true, "Failed to process images", "danger")
  }
}

export const handleFileDelete = (
  index: number,
  files: string[],
  setFiles: (files: string[]) => void,
  setOverallAppearanceImage: (files: string[]) => void,
) => {
  const newFiles = [...files]
  while (newFiles.length < 10) {
    newFiles.push("")
  }
  newFiles[index] = ""
  setFiles(newFiles)
  setOverallAppearanceImage(newFiles)
}
export function isEmptyArray(array?: any[]) {
  return !array || array?.length === 0
}

export function capitalizeFirstLetterMultipleWords(text: string) {
  const words = text?.split(" ")
  const capitalizedWords = words?.map((word) => capitalizeFirstLatter(word))
  return capitalizedWords?.join(" ")
}

export function mapSortToString(sort: TSort[]) {
  return sort?.map((param) => `${param.sortBy},${param.sortOrder}`).join(";")
}

export function getErrorMessageNextAuth(error: string) {
  switch (error) {
    case "Signin":
      return "Sign in failed. Please try again later."
    case "OAuthSignin":
      return "Sign in with OAuth failed. Check the provider configuration."
    case "OAuthCallback":
      return "Failed to connect to the OAuth provider."
    case "OAuthCreateAccount":
      return "Failed to create an account using OAuth."
    case "EmailCreateAccount":
      return "Failed to create an account using email."
    case "Callback":
      return "An error occurred during the callback."
    case "OAuthAccountNotLinked":
      return "This account is already linked with another provider. Use the same login method."
    case "EmailSignin":
      return "Failed to send login email."
    case "CredentialsSignin":
      return "Incorrect credentials. Please check again."
    case "SessionRequired":
      return "You must be logged in to access this page."
    case "AccessDenied":
      return "Access denied. You do not have permission."
    case "Verification":
      return "The verification link is invalid or has expired."
    case "Configuration":
      return "Server configuration error."
    case "no_code":
      return "No authorization code received. Please try again."
    case "invalid_state":
      return "Invalid OAuth state. Please try again."
    case "oauth_error":
      return "OAuth authentication failed. Please try again."
    case "callback_failed":
      return "Authentication callback failed. Please try again."
    case "Default":
    default:
      return "An unknown error occurred."
  }
}
