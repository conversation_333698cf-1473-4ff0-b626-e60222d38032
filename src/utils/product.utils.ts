import {
  PriceType,
  Product,
  ProductInfoType,
  TFilterProducts,
} from "types/product.type"
import { TSelectOption } from "types/misc.type"
import { TCategory } from "types/category.type"
import { TBrandItem } from "types/brand.type"
import { TItemSummary } from "types/itemSummary.type"
import { TSize } from "types/size.type"
import { ItemSummary } from "@domain/entities/ItemSummary"

import {
  formatCurrencyStripe,
  formatPrice,
  getStripAmount,
  isEmpty,
} from "./misc"

const { UnderRetail, None } = ProductInfoType

export function getListParams(params?: TFilterProducts) {
  if (!params) {
    return {}
  }
  const skipKeys = ["totalPages"]
  const filter = {} as any
  Object.entries(params).forEach(([key, value]) => {
    if (!isEmpty(value) && !skipKeys.includes(key)) {
      filter[key] = value
    }
  })
  return filter
}

export function mapFilterParamsToOption({
  params,
  labelKey,
  valueKey,
}: {
  params?: string | null
  labelKey: string
  valueKey: string
}): TSelectOption[] {
  if (!params) return []
  const urlParams = new URLSearchParams(params)
  const labels = urlParams.get(labelKey)
  const values = urlParams.get(valueKey)

  if (!labels || !values) return []

  const labelsArray = labels.split(",")
  const valuesArray = values.split(",")

  return labelsArray.map((label, index) => ({
    label,
    value: valuesArray[index],
  }))
}

export function mapCategoryToOption(category: TCategory): TSelectOption {
  return {
    label: category.name as string,
    value: category.id?.toString(),
  }
}

export function mapBrandToOption(brand: TBrandItem): TSelectOption {
  return {
    label: brand.name as string,
    value: brand.id?.toString(),
  }
}

export function mapOptionsToCategories(options: TSelectOption[]): TCategory[] {
  return options.map((option) => ({
    id: Number(option.value),
    name: option.label,
  }))
}

export function isFilterSelected(
  selectedFilters: TSelectOption[],
  filter: TSelectOption,
) {
  return selectedFilters.some((f) => f.value === filter.value)
}

export function getNewSelectedFilters(
  prevSelected: TSelectOption[],
  filter: TSelectOption,
) {
  return isFilterSelected(prevSelected, filter)
    ? prevSelected.filter((f) => f.value !== filter.value)
    : [...prevSelected, filter]
}

export function concatBrand(data?: TBrandItem[] | Product) {
  if (!data) return ""
  if (Array.isArray(data)) {
    return data?.map((brand) => brand.name).join(", ")
  }
  return data?.brands?.map((brand) => brand.name).join(", ")
}

function convertPriceToNumber(price?: PriceType): number {
  if (!price) return Infinity
  if (typeof price === "number") return price
  return getStripAmount(price)
}

export function isShowUnderRetail(
  retailPrice?: PriceType,
  lowestAsk?: PriceType,
) {
  if (!retailPrice || !lowestAsk) return false

  const retailPriceNumber = convertPriceToNumber(retailPrice)
  const lowestAskNumber = convertPriceToNumber(lowestAsk)

  return lowestAskNumber < retailPriceNumber
}

export function formatRetailPrice(retailPrice?: PriceType) {
  if (!retailPrice) return "-"
  if (typeof retailPrice === "number") {
    return formatPrice(retailPrice, null, "IDR")
  }
  return formatCurrencyStripe({
    price: retailPrice,
    type: "minUnitVal",
  })
}

export function getProductAdditionalInfo(product: Product) {
  const showUnderRetail = isShowUnderRetail(
    product?.retailPrice,
    product?.itemListing?.lowestAsk,
  )

  const result = {
    type: None,
    title: "",
    subtitle: "",
    tooltipText: "",
  }

  if (showUnderRetail) {
    result.type = UnderRetail
    result.title = "Price is Under Retail"
    result.subtitle = "Get the best deals before it’s gone!"
    result.tooltipText = ""
  }

  return result
}

export function createSizeData(sizeId: number) {
  return {
    id: sizeId,
    name: "Size",
    sizeChartId: 1,
    us: "1",
    eu: "1",
    uk: "1",
    cm: "1",
  } as TSize
}

export function getItemSizeHighestOffer({
  itemSummary,
  size,
  shippingType,
}: {
  itemSummary?: TItemSummary
  size?: TSize | number
  shippingType: keyof TItemSummary["highestOffer"] &
    keyof TItemSummary["lowestAsk"]
}) {
  if (!itemSummary || !size) return null
  const sizeId = typeof size === "number" ? size : size?.id
  return itemSummary?.highestOffer?.[shippingType]?.[sizeId]
}

export function getItemSizeLowestAsk({
  itemSummary,
  size,
  shippingType,
}: {
  itemSummary?: TItemSummary
  size: TSize | number
  shippingType: keyof TItemSummary["lowestAsk"] &
    keyof TItemSummary["highestOffer"]
}) {
  if (!itemSummary || !size) return null
  const sizeId = typeof size === "number" ? size : size?.id
  return itemSummary?.lowestAsk?.[shippingType]?.[sizeId]
}

export function mapItemSummaryToRawType(
  itemSummary?: ItemSummary | null,
): TItemSummary {
  if (!itemSummary) return {} as TItemSummary
  return {
    lowestAsk: {
      expressBrandNewNoDefect:
        itemSummary.data.lowestAsk.expressBrandNewNoDefect,
      expressUsed: itemSummary.data.lowestAsk.expressUsed,
      standardBrandNewNoDefect:
        itemSummary.data.lowestAsk.standardBrandNewNoDefect,
      standardBrandNewDefect: itemSummary.data.lowestAsk.standardBrandNewDefect,
      standardUsed: itemSummary.data.lowestAsk.standardUsed,
      preOrderBrandNewNoDefect:
        itemSummary.data.lowestAsk.preOrderBrandNewNoDefect,
      expressBrandNewDefect: itemSummary.data.lowestAsk.expressBrandNewDefect,
    },
    highestOffer: {
      expressBrandNewNoDefect:
        itemSummary.data.highestOffer.expressBrandNewNoDefect,
      standardBrandNewNoDefect:
        itemSummary.data.highestOffer.standardBrandNewNoDefect,
      preOrderBrandNewNoDefect:
        itemSummary.data.highestOffer.preOrderBrandNewNoDefect,
    },
  }
}
