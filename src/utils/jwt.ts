/**
 * JWT Utility Functions
 * Simple JWT decoding without verification (for client-side use)
 */

type JWTPayload = Record<string, any>

/**
 * Decode a JWT token and return the payload
 * Note: This does NOT verify the signature - only decodes the payload
 */
export function decodeJWT(token: string): JWTPayload | null {
  try {
    // Split the token into parts
    const parts = token.split(".")
    if (parts.length !== 3) {
      return null
    }

    // Decode the payload (second part)
    const payload = parts[1]

    // Add padding if needed for base64url decoding
    const paddedPayload = payload + "=".repeat((4 - (payload.length % 4)) % 4)

    // Decode base64url to base64, then decode
    const base64Payload = paddedPayload.replace(/-/g, "+").replace(/_/g, "/")
    const decodedPayload = atob(base64Payload)

    // Parse JSON
    return JSON.parse(decodedPayload)
  } catch {
    // Return null on any error (invalid token format, etc.)
    return null
  }
}

/**
 * Extract the provider from a Cognito JWT token
 * Looks for the 'cognito:username' field and extracts the provider prefix
 */
export function extractProviderFromCognitoToken(token: string): string | null {
  const payload = decodeJWT(token)
  if (!payload) {
    return null
  }

  // Check for cognito:username field
  const cognitoUsername = payload["cognito:username"]
  if (!cognitoUsername || typeof cognitoUsername !== "string") {
    return null
  }

  // Extract provider from username (e.g., "google_110625212000903107372" -> "google")
  const providerMatch = cognitoUsername.match(/^([^_]+)_/)
  if (providerMatch) {
    return providerMatch[1]
  }

  return null
}

/**
 * Get all available providers from a Cognito JWT token
 * Returns an array of provider names found in the token
 */
export function getProvidersFromCognitoToken(token: string): string[] {
  const payload = decodeJWT(token)
  if (!payload) {
    return []
  }

  const providers: string[] = []

  // Check cognito:username
  const cognitoUsername = payload["cognito:username"]
  if (cognitoUsername && typeof cognitoUsername === "string") {
    const providerMatch = cognitoUsername.match(/^([^_]+)_/)
    if (providerMatch) {
      providers.push(providerMatch[1])
    }
  }

  // Check identities array
  const identities = payload.identities
  if (Array.isArray(identities)) {
    identities.forEach((identity: any) => {
      if (identity.providerName && typeof identity.providerName === "string") {
        providers.push(identity.providerName)
      }
    })
  }

  // Remove duplicates and return
  return [...new Set(providers)]
}

/**
 * Extract user claims from a JWT token for credentials login
 * Returns user information from the token payload
 */
export function extractUserClaimsFromToken(token: string): {
  userId: number
  username: string
  email: string
  role: string
  exp?: number
} | null {
  const payload = decodeJWT(token)
  if (!payload) {
    return null
  }

  return {
    userId: (payload.user_id as number) || 0,
    username: (payload.username as string) || "",
    email: (payload.email as string) || "",
    role: (payload.role as string) || "",
    exp: payload.exp as number,
  }
}
