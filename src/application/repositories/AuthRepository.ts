import { User } from "@domain/entities/User"

export interface AuthRepository {
  login(email: string, password: string): Promise<User>
  logout(): Promise<void>
  resetPassword(
    email: string,
    password: string,
    passwordConfirmation: string,
    token: string,
  ): Promise<void>
  verifyResetToken(email: string, token: string): Promise<void>
  requestPasswordReset(email: string): Promise<void>
}
