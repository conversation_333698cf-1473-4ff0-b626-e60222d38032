/* eslint-disable @typescript-eslint/naming-convention */

import { RegisterData } from "types/register.type"
import { gatewayHttpClient } from "@infrastructure/providers/httpClient"
import { <PERSON>rror<PERSON>and<PERSON> } from "@utils/errorHandler"

// Types for consistent error handling
export interface AuthServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
  statusCode?: number
}

export class AuthService {
  async login(email: string, password: string): Promise<any> {
    try {
      const response = await gatewayHttpClient.post("/api/v1/auth/login", {
        email,
        password,
      })

      return response.data
    } catch (error: any) {
      const errorResult = ErrorHandler.handleAuthError(error, "login")
      return {
        Code: errorResult.statusCode || 500,
        Message: errorResult.error,
        Status: "error",
        Data: null,
      }
    }
  }

  async register(data: RegisterData): Promise<AuthServiceResult> {
    try {
      const response = await gatewayHttpClient.post("/api/v1/auth/otp/create", {
        email: data.email,
        phone_number: data.phoneNumber,
      })

      const responseData = response.data

      if (response.status === 200) {
        return {
          success: true,
          data: responseData,
        }
      }

      // Handle non-200 status codes
      return ErrorHandler.handleApiResponseError(responseData, "registration")
    } catch (error: any) {
      return ErrorHandler.handleAuthError(error, "registration")
    }
  }

  async resendOtp(
    to: string,
    type: "EMAIL" | "SMS",
  ): Promise<AuthServiceResult> {
    try {
      const response = await gatewayHttpClient.post("/api/v1/auth/otp/resend", {
        to,
        type,
      })

      if (response.status === 200) {
        return {
          success: true,
          data: response.data,
        }
      }

      // Handle non-200 status codes
      return ErrorHandler.handleApiResponseError(response.data, "resend_otp")
    } catch (error: any) {
      return ErrorHandler.handleAuthError(error, "resend_otp")
    }
  }

  async checkLoginStatus(): Promise<boolean> {
    const response = await gatewayHttpClient.get("/api/v1/auth/status")
    const data = await response.data
    return data.isLoggedIn
  }

  async cognitoLogout(): Promise<void> {
    try {
      const response = await fetch("/api/auth/cognito-logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        throw new Error(`API call failed with status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error("Received error response from logout API endpoint")
      }

      // If no logout URL is provided, we're just doing local logout
      if (!data.logoutUrl) {
        return
      }

      // If a logout URL is provided, call it
      try {
        await fetch(data.logoutUrl, {
          method: "GET",
          mode: "no-cors",
        })
      } catch (fetchError) {
        // Remote logout call failed, this might be expected
      }
    } catch (error) {
      // The Cognito logout process failed
    }
  }
}
