import { User } from "@domain/entities/User"
import { AuthRepository } from "@application/repositories/AuthRepository"

export class AuthUseCase {
  constructor(private authRepository: AuthRepository) {}

  async login(email: string, password: string): Promise<User> {
    // Anda bisa menambahkan validasi atau logika bisnis tambahan di sini
    if (!email || !password) {
      throw new Error("Email and password are required")
    }
    return this.authRepository.login(email, password)
  }

  async logout(): Promise<void> {
    return this.authRepository.logout()
  }

  // Anda bisa menambahkan metode lain sesuai dengan kebutuhan aplikasi Anda
  // async register(email: string, password: string): Promise<User> {
  //   // Validasi email dan password
  //   // Panggil repository.register
  // }

  // async forgotPassword(email: string): Promise<void> {
  //   // Validasi email
  //   // Panggil repository.forgotPassword
  // }

  async resetPassword(
    email: string,
    password: string,
    passwordConfirmation: string,
    token: string,
  ): Promise<void> {
    return this.authRepository.resetPassword(
      email,
      password,
      passwordConfirmation,
      token,
    )
  }

  async verifyResetToken(email: string, token: string): Promise<void> {
    return this.authRepository.verifyResetToken(email, token)
  }

  async requestPasswordReset(email: string): Promise<void> {
    return this.authRepository.requestPasswordReset(email)
  }
}
