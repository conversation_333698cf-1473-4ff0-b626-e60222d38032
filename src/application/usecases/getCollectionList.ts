import { Collection } from "@app/types/collection"
import { CollectionRepository } from "@domain/interfaces/repositories/collectionRepository"

export class GetCollectionListUseCase {
    constructor(private readonly collectionRepository: CollectionRepository) { }

    async execute(filter: Record<string, string>): Promise<Collection> {
        return this.collectionRepository.getCollection(filter)
    }
}