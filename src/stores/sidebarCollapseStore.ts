import { create } from "zustand"
import { persist } from 'zustand/middleware'

interface SidebarState {
    isCollapse: boolean
    setCollapse: (collapse: boolean) => void
}

export const useSidebarStore = create(
    persist<SidebarState>(
        (set) => ({
            isCollapse: false,
            setCollapse: (isCollapse) => set({ isCollapse }),
        }), {
        name: "sidebar-collapse"
    }))
