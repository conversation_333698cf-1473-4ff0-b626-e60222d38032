import { create } from "zustand"
import { TMember } from "types/member.type"

interface MemberState {
  member: TMember
  isLoading: boolean
}
interface MemberActions {
  setMember: (member: TMember) => void
  setLoading: (isLoading: boolean) => void
}

const initialMemberState: MemberState = {
  member: {} as TMember,
  isLoading: false,
}

export const useMemberStore = create<MemberState & MemberActions>()((set) => ({
  ...initialMemberState,
  setMember: (member) => set(() => ({ member })),
  setLoading: (isLoading) => set(() => ({ isLoading })),
}))
