import { gatewayHttpClient } from "@infrastructure/providers/httpClient"
import {
  VerifyCognitoTokenRequest,
  VerifyCognitoTokenResponse,
} from "types/auth.type"

export const authApi = {
  async login(email: string, password: string) {
    const response = await gatewayHttpClient.post("/api/v1/auth/login", {
      email,
      password,
    })
    return response.data
  },
  verifyCognitoToken: (req: VerifyCognitoTokenRequest) => {
    return gatewayHttpClient.post<VerifyCognitoTokenResponse>(
      "/api/v1/auth/verify-cognito-token",
      req,
    )
  },
}
