import { convertToCamelCase, convertToSnakeCase } from "@utils/misc"
import {
  consoleHttpClient,
  serverGatewayHttpClient,
} from "@infrastructure/providers/httpClient"
import { TApiResponse } from "types/apiResponse.type"
import { extractUserClaimsFromToken } from "@utils/jwt"
import { tokenRefreshQueue } from "@infrastructure/auth/tokenRefreshQueue"

export const sellerRegistrationApi = {
  create: async (formData: FormData | any): Promise<TApiResponse<any>> => {
    try {
      let finalFormData: FormData

      if (formData instanceof FormData) {
        finalFormData = formData
      } else {
        // Convert object to FormData with proper payload structure
        finalFormData = new FormData()

        // Convert the data to snake_case and wrap in payload field
        const snakeCaseData = convertToSnakeCase(formData)
        finalFormData.append("payload", JSON.stringify(snakeCaseData))

        // Add image file if present
        if (formData.sellerVerificationImage instanceof File) {
          finalFormData.append(
            "seller_verification_image",
            formData.sellerVerificationImage,
          )
        }
      }

      const response = await serverGatewayHttpClient.post(
        "/api/v1/member/seller-verification/create",
        finalFormData,
      )
      const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
      return apiResponse
    } catch (error: any) {
      // Extract error message from Axios response
      let message = "Something went wrong"

      // Try to get message from response data
      if (error?.response?.data) {
        const responseData = error.response.data
        if (responseData.Message) {
          message = responseData.Message
        } else if (responseData.message) {
          message = responseData.message
        } else if (responseData.error) {
          message = responseData.error
        }
      } else if (error?.message) {
        message = error.message
      }

      throw new Error(message)
    }
  },

  createOtp: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    try {
      // API expects JSON body with snake_case: { phone_number: "..." }
      const payload =
        typeof formData === "string" ? { phone_number: formData } : formData

      const response = await serverGatewayHttpClient.post(
        "/api/v1/member/seller-verification/otp/create",
        payload,
      )
      const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
      return apiResponse
    } catch (error: any) {
      // Extract error message from Axios response
      let message = "Something went wrong"

      // Try to get message from response data
      if (error?.response?.data) {
        const responseData = error.response.data
        if (responseData.Message) {
          message = responseData.Message
        } else if (responseData.message) {
          message = responseData.message
        } else if (responseData.error) {
          message = responseData.error
        }
      } else if (error?.message) {
        message = error.message
      }

      throw new Error(message)
    }
  },

  resendOtp: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    try {
      const payload =
        typeof formData === "string" ? { type: "SMS", to: formData } : formData
      const response = await serverGatewayHttpClient.post(
        "/api/v1/member/seller-verification/otp/resend",
        payload,
      )

      // Axios returns 204 with empty data; normalize to success shape
      if (response.status === 204) {
        return {
          code: 204,
          status: "success",
          message: "OTP resent successfully",
          data: null,
        } as TApiResponse<any>
      }

      const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
      return apiResponse
    } catch (error: any) {
      // Extract error message from Axios response
      let message = "Something went wrong"

      // Try to get message from response data
      if (error?.response?.data) {
        const responseData = error.response.data
        if (responseData.Message) {
          message = responseData.Message
        } else if (responseData.message) {
          message = responseData.message
        } else if (responseData.error) {
          message = responseData.error
        }
      } else if (error?.message) {
        message = error.message
      }

      // Create a custom error that preserves the original error structure
      const customError = new Error(message) as any
      customError.response = error.response
      customError.isAxiosError = true

      throw customError
    }
  },

  verifyOtp: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    try {
      const payload =
        typeof formData === "string"
          ? { requests: { code: formData, type: "SMS" } }
          : formData

      const response = await serverGatewayHttpClient.post(
        "/api/v1/member/seller-verification/otp/verify",
        payload,
      )
      const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
      return apiResponse
    } catch (error: any) {
      // Extract error message from Axios response
      let message = "Something went wrong"

      // Try to get message from response data
      if (error?.response?.data) {
        const responseData = error.response.data
        if (responseData.Message) {
          message = responseData.Message
        } else if (responseData.message) {
          message = responseData.message
        } else if (responseData.error) {
          message = responseData.error
        }
      } else if (error?.message) {
        message = error.message
      }

      // Create a custom error that preserves the original error structure
      const customError = new Error(message) as any
      customError.response = error.response
      customError.isAxiosError = true

      throw customError
    }
  },

  verifyPassword: async (password: string): Promise<TApiResponse<any>> => {
    try {
      // Add x-user-id header from JWT claims
      const token = await tokenRefreshQueue.getValidToken()
      let userIdHeader: string | undefined
      if (token && token !== "oauth_token") {
        const claims = extractUserClaimsFromToken(token)
        if (claims?.userId) {
          userIdHeader = String(claims.userId)
        }
      }

      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      }
      if (userIdHeader) {
        headers["X-UserID"] = userIdHeader
      }

      // Add authorization header
      if (token && token !== "oauth_token") {
        headers["Authorization"] = `Bearer ${token}`
      }

      // Call local Next.js API route to avoid CORS issues
      const response = await fetch("/api/proxy-auth/verify-password", {
        method: "POST",
        headers,
        body: JSON.stringify({ password }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to verify password")
      }

      // Handle 204 No Content response
      if (response.status === 204) {
        return {
          code: 204,
          status: "success",
          message: "Password verified successfully",
          data: null,
        } as TApiResponse<any>
      }

      const data = await response.json()
      const apiResponse = convertToCamelCase(data) as TApiResponse<any>
      return apiResponse
    } catch (error) {
      let message
      if (error instanceof Error) message = error.message
      else message = String(error)
      throw new Error(message)
    }
  },

  getAll: async (filter?: any): Promise<TApiResponse<any>> => {
    try {
      const response = await serverGatewayHttpClient.get(
        "/api/v1/member/seller-verification/get",
        {
          params: {
            pageSize: 100,
            ...filter,
          },
        },
      )
      const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
      return apiResponse
    } catch (error) {
      let message
      if (error instanceof Error) message = error.message
      else message = String(error)
      throw new Error(message)
    }
  },

  register: async (sellerData: any): Promise<TApiResponse<any>> => {
    const response = await serverGatewayHttpClient.post(
      "/api/v1/member/seller/register",
      sellerData,
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
    return apiResponse
  },

  uploadDocument: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    const headers: Record<string, string> = {}

    if (typeof formData === "string") {
      headers["Content-Type"] = "application/json"
    }

    const response = await consoleHttpClient.post(
      "/seller-registration/upload-document",
      formData,
      { headers },
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
    return apiResponse
  },

  uploadBankAccount: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    const headers: Record<string, string> = {}

    if (typeof formData === "string") {
      headers["Content-Type"] = "application/json"
    }

    const response = await consoleHttpClient.post(
      "/seller-registration/upload-bank-account",
      formData,
      { headers },
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
    return apiResponse
  },

  uploadIdentity: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    const headers: Record<string, string> = {}

    if (typeof formData === "string") {
      headers["Content-Type"] = "application/json"
    }

    const response = await consoleHttpClient.post(
      "/seller-registration/upload-identity",
      formData,
      { headers },
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
    return apiResponse
  },

  uploadTax: async (
    formData: FormData | string,
  ): Promise<TApiResponse<any>> => {
    const headers: Record<string, string> = {}

    if (typeof formData === "string") {
      headers["Content-Type"] = "application/json"
    }

    const response = await consoleHttpClient.post(
      "/seller-registration/upload-tax",
      formData,
      { headers },
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>
    return apiResponse
  },
}
