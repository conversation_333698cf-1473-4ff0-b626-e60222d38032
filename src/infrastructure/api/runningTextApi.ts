import { convertToCamelCase } from "@utils/misc"
import { consoleHttpClient } from "@infrastructure/providers/httpClient"
import { TApiResponse, TPaginatedData } from "types/apiResponse.type"
import { IRunningText } from "types/runningText"
import { MiscConstant } from "@constants/misc"

export const runningTextApi = {
  getCurrent: async (): Promise<TApiResponse<IRunningText>> => {
    const response = await consoleHttpClient.get("/running-text/get-current")
    const apiResponse = convertToCamelCase(
      response.data,
    ) as TApiResponse<IRunningText>

    return {
      ...apiResponse,
      data: {
        ...apiResponse.data,
        content: (apiResponse.data?.content as IRunningText[])?.map((item) => ({
          ...item,
          redirectUrl:
            item.redirectUrl === MiscConstant.EMPTY_KEY
              ? null
              : item.redirectUrl,
        })) as IRunningText[],
      } as TPaginatedData<IRunningText>,
    }
  },
}
