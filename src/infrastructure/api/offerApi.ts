import { snakeCase } from "lodash"
import { convertToCamelCase, convertToSnakeCase } from "@utils/misc"
import { gatewayHttpClient } from "@infrastructure/providers/httpClient"
import {
  TCreateOfferPayload,
  TOffer,
  TOfferFilter,
  TOfferV2,
  TSellNowResponse,
} from "types/offer.type"
import { TApiResponse, TPaginatedData } from "types/apiResponse.type"
import { getListParams, mapCondition } from "@utils/offer.utils"

export const offerApi = {
  create: async (body: TCreateOfferPayload) => {
    await gatewayHttpClient.post(
      "/api/v1/transaction/offer/create",
      convertToSnakeCase(body),
    )
  },

  getAll: async (
    filter?: TOfferFilter,
  ): Promise<TApiResponse<TPaginatedData<TOffer>>> => {
    try {
      const response = await gatewayHttpClient.get(
        "/api/v1/transaction/offer/get",
        {
          params: getListParams(filter),
        },
      )
      return convertToCamelCase(response.data) as TApiResponse<
        TPaginatedData<TOffer>
      >
    } catch (error) {
      return {
        code: 500,
        status: "",
        message: "",
        data: {},
      } as TApiResponse<TPaginatedData<TOffer>>
    }
  },

  getMy: async (
    filter?: TOfferFilter,
  ): Promise<TApiResponse<TPaginatedData<TOfferV2>>> => {
    try {
      const unusedKeys = ["condition", "price_to", "price_from"]
      const params = {
        ...getListParams(filter),
        createddateStart: filter?.createddateStart,
        createddateEnd: filter?.createddateEnd,
        priceFrom: filter?.priceFrom,
        priceTo: filter?.priceTo,
        item_condition: filter?.condition
          ? mapCondition(filter.condition)
          : null,
      }

      unusedKeys.forEach((key) => {
        if (key in params) delete params[key]
      })

      const response = await gatewayHttpClient.get(
        "/api/v1/transaction/offer/get/my",
        {
          params,
          paramsSerializer: { indexes: null },
        },
      )
      return convertToCamelCase(response.data) as TApiResponse<
        TPaginatedData<TOfferV2>
      >
    } catch (error) {
      return {
        code: 500,
        status: "",
        message: "",
        data: {
          content: [],
          totalPages: 0,
          totalElements: 0,
          size: 0,
          number: 0,
          first: false,
          last: false,
          pageSize: 0,
          page: 0,
          totalSize: 0,
        },
      } as TApiResponse<TPaginatedData<TOfferV2>>
    }
  },

  getById: async (id: number) => {
    try {
      const response = await gatewayHttpClient.get(
        `/api/v1/transaction/offer/get/${id}`,
      )
      return convertToCamelCase(response.data) as TApiResponse<TOffer>
    } catch (error) {
      return {
        code: 200,
        status: "success",
        message: "Mock data",
        data: {},
      } as TApiResponse<TOffer>
    }
  },

  sellNow: async (id: number): Promise<TApiResponse<TSellNowResponse>> => {
    try {
      const response = await gatewayHttpClient.post(
        "/api/v1/transaction/transaction/sell-now",
        {
          [snakeCase("offerId")]: id,
        },
      )
      return convertToCamelCase(response.data) as TApiResponse<TSellNowResponse>
    } catch (error) {
      console.warn(`Failed to sell now for offer ${id}:`, error)
      return {
        code: 500,
        status: "error",
        message: "Failed to process sell now request",
        data: {},
      } as TApiResponse<TSellNowResponse>
    }
  },
}
