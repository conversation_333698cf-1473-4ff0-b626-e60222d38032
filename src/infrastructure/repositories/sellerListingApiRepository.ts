import { SellerListingRepository } from "@domain/interfaces/SellerListingRepository"
import { sellerListingApi } from "@infrastructure/api/sellerListingApi"
import {
  TSellerListingBulk,
  SellerListingResponseBulk,
  SellerListing,
  SellerListingResponse,
} from "types/sellerListing"

export class SellerListingApiRepository implements SellerListingRepository {
  async createBulk(
    body: TSellerListingBulk,
  ): Promise<SellerListingResponseBulk> {
    const resp = await sellerListingApi.createBulk(body)
    return Promise.resolve(resp as SellerListingResponseBulk)
  }

  async createBulkBrandNew(
    body: TSellerListingBulk,
  ): Promise<SellerListingResponseBulk> {
    const resp = await sellerListingApi.createBulkBrandNew(body)
    return Promise.resolve(resp as SellerListingResponseBulk)
  }

  async create(
    body: SellerListing,
    overallAppearanceImage?: File[],
    expiredAt?: string,
  ) {
    const resp = await sellerListingApi.create(
      body,
      overallAppearanceImage,
      expiredAt,
    )
    return Promise.resolve((resp as { data: SellerListingResponse }).data)
  }

  async getById(id: number) {
    const resp = await sellerListingApi.getById(id)
    return Promise.resolve(resp.data as SellerListing)
  }
}
