import { Brand } from "@domain/entities/Brand"
import { BrandRepository } from "@domain/interfaces/BrandRepository"
import { brandApi } from "@infrastructure/api/brandApi"
import { TBrand, TBrandItem, TBrandFilter } from "types/brand.type"

export class BrandApiRepository implements BrandRepository {
  async getById(id: number) {
    const resp = await brandApi.getById(id)
    const brandData = (resp as any)?.data as TBrandItem
    return Promise.resolve(new Brand(brandData))
  }

  async getAll(filter?: TBrandFilter) {
    const resp = await brandApi.getAll(filter)
    const paginatedData = (resp as any)?.data as TBrand

    return Promise.resolve(paginatedData)
  }
}
