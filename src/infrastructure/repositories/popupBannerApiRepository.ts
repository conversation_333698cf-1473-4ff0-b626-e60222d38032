import { PopupBannerRepository } from "@domain/interfaces/PopupBannerRepository"
import { popupBannerApi } from "@infrastructure/api/popupBannerApi"
import { IBasePaginatedApiResponse } from "types/apiResponse.type"
import { IPopupBannerData, IPopupBannerParams } from "types/popupBanner.type"

export class PopupBannerApiRepository implements PopupBannerRepository {
  async getCurrent(
    params: Partial<IPopupBannerParams>,
  ): Promise<IBasePaginatedApiResponse<IPopupBannerData>> {
    const res = await popupBannerApi.getCurrent(params)
    return Promise.resolve(res as IBasePaginatedApiResponse<IPopupBannerData>)
  }
}
