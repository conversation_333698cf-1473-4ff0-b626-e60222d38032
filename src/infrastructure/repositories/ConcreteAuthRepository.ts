/* eslint-disable @typescript-eslint/naming-convention */

// src/infrastructure/repositories/ConcreteAuthRepository.ts
import { AuthRepository } from "@application/repositories/AuthRepository"
import { User } from "@domain/entities/User"
import { GATEWAY_BASE_URL } from "@app/config/api"

export class ConcreteAuthRepository implements AuthRepository {
  async login(email: string, password: string): Promise<User> {
    const response = await fetch(`${GATEWAY_BASE_URL}/api/v1/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    })

    if (!response.ok) {
      throw new Error("Login failed")
    }

    const userData = await response.json()
    return userData as User
  }

  async logout(): Promise<void> {
    const response = await fetch(`${GATEWAY_BASE_URL}/api/v1/auth/logout`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error("Logout failed")
    }
  }

  async resetPassword(
    email: string,
    password: string,
    passwordConfirmation: string,
    token: string,
  ): Promise<void> {
    const response = await fetch(
      `${GATEWAY_BASE_URL}/api/v1/auth/forget-password/password-update`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          password_confirmation: passwordConfirmation,
          token,
        }),
      },
    )

    if (!response.ok) {
      throw new Error("Failed to reset password")
    }
  }

  async requestPasswordReset(email: string): Promise<void> {
    const response = await fetch(
      `${GATEWAY_BASE_URL}/api/v1/auth/forget-password/reset`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      },
    )

    if (!response.ok) {
      const errorData = await response.json()

      if (
        errorData?.Code === 101005 &&
        errorData?.Message === "Your requested Item is not found"
      ) {
        throw new Error("Email not found")
      } else {
        throw new Error("System error")
      }
    }
  }

  async verifyResetToken(email: string, token: string): Promise<void> {
    const response = await fetch(
      `${GATEWAY_BASE_URL}/api/v1/auth/forget-password/verify?email=${email}&token=${token}`,
      {
        method: "GET",
      },
    )

    if (response.status === 401) {
      throw new Error("Token is invalid")
    }

    if (!response.ok) {
      throw new Error("Failed to verify reset token")
    }
  }
}
