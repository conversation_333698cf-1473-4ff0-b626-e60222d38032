/* eslint-disable @typescript-eslint/naming-convention */

import axios, { AxiosError, AxiosInstance } from "axios"
import { logError } from "@utils/misc"
import { GATEWAY_BASE_URL, CONSOLE_BASE_URL } from "@app/config/api"
import { tokenRefreshQueue } from "@infrastructure/auth/tokenRefreshQueue"

// ============================================================================
// GATEWAY SERVICE HTTP CLIENTS
// ============================================================================

// Client-side HTTP client for Gateway Service
export const createClientSideGatewayClient = (
  baseURL: string = GATEWAY_BASE_URL,
): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  })

  // Request interceptor - add auth token with queue protection
  client.interceptors.request.use(
    async (config) => {
      try {
        // Use token refresh queue to prevent race conditions
        const token = await tokenRefreshQueue.getValidToken()
        if (token && token !== "oauth_token") {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.error("Failed to get valid token for request:", error)
        // Continue without token - let the API handle unauthorized requests
      }
      return config
    },
    (error) => Promise.reject(error),
  )

  // Response interceptor - handle auth errors
  client.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      logError(error)
      return Promise.reject(error)
    },
  )

  return client
}

// Server/Client token-attaching Gateway client using getAppToken
export const createGatewayClientWithAppToken = (
  baseURL: string = GATEWAY_BASE_URL,
): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000,
  })

  client.interceptors.request.use(
    async (config) => {
      try {
        // Use token refresh queue for server-side requests too
        const token = await tokenRefreshQueue.getValidToken()
        if (token && token !== "oauth_token") {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.error("Failed to get valid token for server request:", error)
        // Continue without token - let the API handle unauthorized requests
      }
      return config
    },
    (error) => Promise.reject(error),
  )

  client.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      logError(error)
      return Promise.reject(error)
    },
  )

  return client
}

// No token gateway HTTP client (for public endpoints)
export const createNoTokenGatewayClient = (
  baseURL: string = GATEWAY_BASE_URL,
): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  })

  client.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      logError(error)
      return Promise.reject(error)
    },
  )

  return client
}

// ============================================================================
// CONSOLE SERVICE HTTP CLIENTS
// ============================================================================

// Console Service HTTP client
export const createConsoleHttpClient = (
  baseURL: string = CONSOLE_BASE_URL,
): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  })

  // Request interceptor - add auth token for console service
  client.interceptors.request.use(
    async (config) => {
      try {
        // Use token refresh queue to prevent race conditions
        const token = await tokenRefreshQueue.getValidToken()
        if (token && token !== "oauth_token") {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.error("Failed to get valid token for request:", error)
        // Continue without token - let the API handle unauthorized requests
      }
      return config
    },
    (error) => Promise.reject(error),
  )

  // Response interceptor - handle auth errors
  client.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      logError(error)
      return Promise.reject(error)
    },
  )

  return client
}

// ============================================================================
// PRE-CONFIGURED INSTANCES
// ============================================================================

// Gateway Service instances
export const gatewayHttpClient = createClientSideGatewayClient()
export const serverGatewayHttpClient = createGatewayClientWithAppToken()
export const noTokenGatewayHttpClient = createNoTokenGatewayClient()

// Console Service instances
export const consoleHttpClient = createConsoleHttpClient()
