/* eslint-disable no-warning-comments */
"use server"

import { revalidatePath } from "next/cache"
import { serverTokenManager } from "@infrastructure/auth/serverTokenManager"
import { AddressApiRepository } from "@infrastructure/repositories/addressApiRepository"
import { addressSchema } from "@domain/validation/addressSchema"
import { TAddress } from "types/address.type"

export const create = async (
  prevState: any,
  formData: FormData,
): Promise<{ data: TAddress } | { errors: any }> => {
  try {
    // Validate form data
    const validationResult = addressSchema.safeParse({
      // Map form fields to schema field names
      markAs: formData.get("title"),
      mobileNumber: `${formData.get("country_code") ?? ""}${
        formData.get("phone_number") ?? ""
      }`,
      // Schema's "address" represents pinpoint; fall back to address_detail like the API layer
      address:
        (formData.get("pinpoint") as FormDataEntryValue | null) ||
        (formData.get("address_detail") as FormDataEntryValue | null) ||
        "",
      city: formData.get("city_id"),
      province: formData.get("province_id"),
      country: formData.get("country_id"),
      // Schema uses postalCode (UI reads zipCode; UI handles this separately)
      postalCode: formData.get("zip_code"),
      addressDetail: formData.get("address_detail"),
      notes: formData.get("notes"),
    })

    if (!validationResult.success) {
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.warn(
          "⚠️ [AddressAction.create] Validation failed",
          validationResult.error.flatten().fieldErrors,
        )
      }
      return {
        errors: validationResult.error.flatten().fieldErrors,
      }
    }

    // Get member ID from server session
    const memberId = await serverTokenManager.getUserId()
    if (!memberId) {
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error(
          "❌ [AddressAction.create] Missing user ID; ensure you are logged in",
        )
      }
      return {
        errors: {
          submit: "You must be logged in to create an address.",
        },
      }
    }

    // Create address
    const addressRepository = new AddressApiRepository()
    const result = await addressRepository.create(memberId, formData)

    return { data: result }
  } catch (error) {
    if (process.env.NODE_ENV !== "production") {
      // eslint-disable-next-line no-console
      console.error("💥 [AddressAction.create] Error", error)
    }
    return {
      errors: {
        submit: "Failed to create address. Please try again.",
      },
    }
  }
}

export async function edit(prevState: any, formData: FormData) {
  const validatedFields = addressSchema.safeParse({
    address: formData.get("pinpoint"),
    addressDetail: formData.get("address_detail"),
    // TODO integrate firstname for next enhancement
    // firstName: formData.get("first_name"),
    city: formData.get("city_id"),
    mobileNumber: `${formData.get("country_code")}${formData.get("phone_number")}`,
    province: formData.get("province_id"),
    markAs: formData.get("title"),
    district: formData.get("district_id"),
    postalCode: formData.get("zip_code"),
    notes: formData.get("notes") || "",
    country: formData.get("country_id"),
  })

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
    }
  }
  // Get member ID from server session instead of form data
  const memberId = await serverTokenManager.getUserId()
  if (!memberId) {
    return {
      errors: {
        submit: "You must be logged in to edit an address.",
      },
    }
  }

  const id = formData.get("id")
  // Note: Member ID is available as memberId if needed by repository

  try {
    const UpdateAddress = new AddressApiRepository()
    const res = await UpdateAddress.update(Number(id), formData)
    revalidatePath("/profile")
    return res
  } catch (error) {
    return {
      errors: {
        submit: "Failed to update address. Please try again.",
      },
    }
  }
}
