/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Debug utility to trace token flow and refresh token changes
 * Only active in development mode
 */
export class TokenFlowDebugger {
  private static instance: TokenFlowDebugger
  private tokenHistory: Array<{
    timestamp: number
    event: string
    oldToken?: string
    newToken?: string
    details?: any
  }> = []

  public static getInstance(): TokenFlowDebugger {
    if (!TokenFlowDebugger.instance) {
      TokenFlowDebugger.instance = new TokenFlowDebugger()
    }
    return TokenFlowDebugger.instance
  }

  private constructor() {}

  /**
   * Log token change event
   */
  public logTokenChange(
    event: string,
    oldToken?: string,
    newToken?: string,
    details?: any,
  ): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    const entry = {
      timestamp: Date.now(),
      event,
      oldToken: oldToken?.slice(-8),
      newToken: newToken?.slice(-8),
      details,
    }

    this.tokenHistory.push(entry)

    // Keep only last 20 entries
    if (this.tokenHistory.length > 20) {
      this.tokenHistory = this.tokenHistory.slice(-20)
    }

    console.log(`🔍 Token Flow: ${event}`, {
      old: entry.oldToken || "none",
      new: entry.newToken || "none",
      ...details,
    })
  }

  /**
   * Get token history for debugging
   */
  public getTokenHistory(): typeof this.tokenHistory {
    return [...this.tokenHistory]
  }

  /**
   * Print token history summary
   */
  public printTokenHistory(): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    console.log("🔍 Token History Summary:")
    console.table(
      this.tokenHistory.map((entry) => ({
        Time: new Date(entry.timestamp).toISOString().substr(11, 8),
        Event: entry.event,
        "Old Token": entry.oldToken || "none",
        "New Token": entry.newToken || "none",
      })),
    )
  }

  /**
   * Detect potential issues in token flow
   */
  public detectIssues(): string[] {
    const issues: string[] = []

    // Check for repeated refresh token usage
    const recentEntries = this.tokenHistory.slice(-10)
    const refreshEvents = recentEntries.filter(
      (e) => e.event === "refresh_start",
    )

    if (refreshEvents.length > 1) {
      const tokens = refreshEvents.map((e) => e.oldToken)
      const duplicates = tokens.filter(
        (token, index) => tokens.indexOf(token) !== index && token !== "none",
      )

      if (duplicates.length > 0) {
        issues.push(
          `Duplicate refresh token usage detected: ${duplicates.join(", ")}`,
        )
      }
    }

    // Check for rapid refresh attempts
    const refreshTimes = refreshEvents.map((e) => e.timestamp)
    for (let i = 1; i < refreshTimes.length; i++) {
      const timeDiff = refreshTimes[i] - refreshTimes[i - 1]
      if (timeDiff < 2000) {
        // Less than 2 seconds
        issues.push(`Rapid refresh attempts detected: ${timeDiff}ms apart`)
      }
    }

    return issues
  }

  /**
   * Clear history
   */
  public clearHistory(): void {
    this.tokenHistory = []
    console.log("🔍 Token history cleared")
  }
}

// Export singleton instance
export const tokenFlowDebugger = TokenFlowDebugger.getInstance()

// Auto-detect issues periodically in development
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  setInterval(() => {
    const issues = tokenFlowDebugger.detectIssues()
    if (issues.length > 0) {
      console.warn("⚠️ Token Flow Issues Detected:")
      issues.forEach((issue) => console.warn(`  - ${issue}`))
    }
  }, 30000) // Check every 30 seconds
}
