const REFRESH_BUFFER_TIME = 5 * 60 * 1000 // 5 minutes buffer before expiry

// Global refresh state manager - singleton pattern for cross-instance sharing
class RefreshStateManager {
  private static instance: RefreshStateManager
  private isRefreshing = false
  private refreshPromise: Promise<any> | null = null
  private refreshAttempts = 0
  private lastRefreshTime = 0
  private lastSuccessfulRefresh = 0
  private lastUsedRefreshToken = ""
  private readonly MAX_REFRESH_ATTEMPTS = 3
  private readonly MIN_REFRESH_INTERVAL = 2000 // 2 seconds
  private readonly REFRESH_ATTEMPTS_RESET_TIME = 5 * 60 * 1000 // 5 minutes
  private readonly SUCCESS_COOLDOWN = 30 * 1000 // 30 seconds cooldown after successful refresh

  public static getInstance(): RefreshStateManager {
    if (!RefreshStateManager.instance) {
      RefreshStateManager.instance = new RefreshStateManager()
    }
    return RefreshStateManager.instance
  }

  private constructor() {}

  public shouldRefresh(
    tokenExpires: number,
    currentRefreshToken?: string,
  ): boolean {
    const now = Date.now()

    // Check if token is still valid (with buffer time)
    if (now < tokenExpires - REFRESH_BUFFER_TIME) {
      return false
    }

    // If token is actually expired (past expiry time), reset cooldown and tracking
    if (now > tokenExpires) {
      console.log("🔄 Token actually expired, resetting cooldown and tracking")
      this.lastSuccessfulRefresh = 0
      this.lastUsedRefreshToken = ""
    }

    // Check if this refresh token was already used recently
    // Only block if we're trying to use the SAME token that was just used for refresh
    if (
      currentRefreshToken &&
      this.lastUsedRefreshToken &&
      currentRefreshToken === this.lastUsedRefreshToken &&
      now - this.lastRefreshTime < this.MIN_REFRESH_INTERVAL
    ) {
      console.log("⏳ This refresh token was already used recently, skipping")
      console.log("🔍 Current token (last 8):", currentRefreshToken.slice(-8))
      console.log(
        "🔍 Last used token (last 8):",
        this.lastUsedRefreshToken.slice(-8),
      )
      return false
    }

    // Prevent refresh if we just had a successful refresh recently
    if (now - this.lastSuccessfulRefresh < this.SUCCESS_COOLDOWN) {
      console.log("⏳ Recent successful refresh, skipping (cooldown)")
      return false
    }

    // Reset refresh attempts if enough time has passed
    if (now - this.lastRefreshTime > this.REFRESH_ATTEMPTS_RESET_TIME) {
      this.refreshAttempts = 0
      console.log("🔄 Reset refresh attempts counter")
    }

    // Prevent too frequent refresh attempts
    if (now - this.lastRefreshTime < this.MIN_REFRESH_INTERVAL) {
      console.log("⏳ Refresh attempt too soon, skipping")
      return false
    }

    // Check refresh attempt limits
    if (this.refreshAttempts >= this.MAX_REFRESH_ATTEMPTS) {
      console.error("🚫 Max refresh attempts reached")
      return false
    }

    return true
  }

  public async handleRefresh(
    token: any,
    refreshFunction: (token: any, now: number) => Promise<any>,
  ): Promise<any> {
    const now = Date.now()

    // If refresh is already in progress, wait for it
    if (this.isRefreshing && this.refreshPromise) {
      try {
        console.log("⏳ Waiting for ongoing refresh...")
        const refreshedToken = await this.refreshPromise
        return refreshedToken
      } catch (error) {
        console.error("❌ Shared refresh failed:", error)
        throw error
      }
    }

    // Start refresh process
    if (!this.isRefreshing) {
      console.log("🔄 Starting new refresh process...")
      this.isRefreshing = true
      this.lastRefreshTime = now
      this.refreshAttempts++
      // Store the refresh token being used
      if (token?.refreshToken) {
        this.lastUsedRefreshToken = token.refreshToken
      }
      this.refreshPromise = refreshFunction(token, now)
    }

    try {
      const newToken = await this.refreshPromise
      // Reset attempts on success and record successful refresh time
      this.refreshAttempts = 0
      this.lastSuccessfulRefresh = Date.now()
      // Clear the used refresh token tracking since we got a new token
      // The new token should be allowed to be used for future refreshes
      this.lastUsedRefreshToken = ""
      console.log("🔄 Cleared tracked refresh token (refresh successful)")

      if (newToken?.refreshToken) {
        console.log(
          "🔄 New refresh token available (last 8 chars):",
          newToken.refreshToken.slice(-8),
        )
      }
      console.log(
        "✅ Refresh successful, setting cooldown until:",
        new Date(
          this.lastSuccessfulRefresh + this.SUCCESS_COOLDOWN,
        ).toISOString(),
      )
      return newToken
    } catch (error) {
      console.error("❌ Token refresh failed:", error)
      throw error
    } finally {
      // Reset refresh state
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  public getStatus() {
    return {
      isRefreshing: this.isRefreshing,
      refreshAttempts: this.refreshAttempts,
      maxAttempts: this.MAX_REFRESH_ATTEMPTS,
      lastRefreshTime: this.lastRefreshTime,
      lastSuccessfulRefresh: this.lastSuccessfulRefresh,
      cooldownUntil: this.lastSuccessfulRefresh + this.SUCCESS_COOLDOWN,
    }
  }

  /**
   * Force reset refresh state - use with caution
   * Only for handling stale token data scenarios
   */
  public forceReset(reason: string) {
    console.log(`🔄 Force resetting refresh state: ${reason}`)
    this.isRefreshing = false
    this.refreshPromise = null
    this.lastUsedRefreshToken = ""
    this.lastSuccessfulRefresh = 0
    this.refreshAttempts = 0
  }
}

export const refreshStateManager = RefreshStateManager.getInstance()
