/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Test utility to simulate token expiry scenarios
 * Only available in development mode
 */
export class TokenExpiryTester {
  private static instance: TokenExpiryTester

  public static getInstance(): TokenExpiryTester {
    if (!TokenExpiryTester.instance) {
      TokenExpiryTester.instance = new TokenExpiryTester()
    }
    return TokenExpiryTester.instance
  }

  private constructor() {}

  /**
   * Simulate token expiry scenarios for testing
   */
  public simulateTokenExpiry(): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    console.log("🧪 Starting token expiry simulation...")

    // Test scenario 1: Token about to expire (within buffer)
    const now = Date.now()
    const bufferTime = 5 * 1000 // 5 seconds
    const almostExpired = now + bufferTime - 1000 // 1 second before buffer

    console.log("📊 Test Scenario 1: Token about to expire")
    console.log("  Current time:", new Date(now).toISOString())
    console.log("  Token expires:", new Date(almostExpired).toISOString())
    console.log("  Should refresh:", now > almostExpired - bufferTime)

    // Test scenario 2: Token actually expired
    const expired = now - 1000 // 1 second ago

    console.log("📊 Test Scenario 2: Token actually expired")
    console.log("  Current time:", new Date(now).toISOString())
    console.log("  Token expires:", new Date(expired).toISOString())
    console.log("  Is expired:", now > expired)
    console.log("  Should refresh:", true)

    // Test scenario 3: Token still valid
    const valid = now + 10 * 60 * 1000 // 10 minutes from now

    console.log("📊 Test Scenario 3: Token still valid")
    console.log("  Current time:", new Date(now).toISOString())
    console.log("  Token expires:", new Date(valid).toISOString())
    console.log("  Is expired:", now > valid)
    console.log("  Should refresh:", now > valid - bufferTime)
  }

  /**
   * Check current token status
   */
  public async checkCurrentTokenStatus(): Promise<void> {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    try {
      const { getSession } = await import("next-auth/react")
      const session = await getSession()

      if (!session?.user?.accessToken) {
        console.log("🔍 No active session found")
        return
      }

      const now = Date.now()
      const tokenExpires = session.user.accessTokenExpires || 0
      const bufferTime = 5 * 1000 // 5 seconds

      console.log("🔍 Current Token Status:")
      console.log("  Current time:", new Date(now).toISOString())
      console.log("  Token expires:", new Date(tokenExpires).toISOString())
      console.log("  Is expired:", now > tokenExpires)
      console.log("  Within buffer:", now > tokenExpires - bufferTime)
      console.log(
        "  Time until expiry:",
        Math.round((tokenExpires - now) / 1000),
        "seconds",
      )
      console.log(
        "  Access token (last 8):",
        session.user.accessToken.slice(-8),
      )
      console.log(
        "  Refresh token (last 8):",
        session.user.refreshToken?.slice(-8) || "none",
      )
    } catch (error) {
      console.error("🔍 Failed to check token status:", error)
    }
  }

  /**
   * Monitor token expiry in real-time
   */
  public startTokenMonitoring(
    intervalMs: number = 5000,
  ): NodeJS.Timeout | null {
    if (process.env.NODE_ENV !== "development") {
      return null
    }

    console.log("🔍 Starting token monitoring...")

    return setInterval(async () => {
      await this.checkCurrentTokenStatus()
    }, intervalMs)
  }

  /**
   * Test refresh token tracking logic
   */
  public testRefreshTokenTracking(): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    console.log("🧪 Testing refresh token tracking logic...")

    // Simulate refresh token changes
    const tokens = [
      "old-refresh-token-12345678",
      "new-refresh-token-87654321",
      "newer-refresh-token-11111111",
    ]

    tokens.forEach((token, index) => {
      console.log(`📊 Step ${index + 1}: Using token ${token.slice(-8)}`)

      if (index > 0) {
        const previousToken = tokens[index - 1]
        const shouldBlock = token === previousToken
        console.log(`  Should block (same as previous): ${shouldBlock}`)
      }
    })
  }
}

// Export singleton instance
export const tokenExpiryTester = TokenExpiryTester.getInstance()

// Auto-start monitoring in development
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  // Start monitoring after a short delay
  setTimeout(() => {
    tokenExpiryTester.simulateTokenExpiry()
    tokenExpiryTester.testRefreshTokenTracking()
  }, 5000)
}
