/* eslint-disable @typescript-eslint/naming-convention */
"use server"

import { getServerSession } from "next-auth/next"
import { authOptions } from "@app/api/auth/[...nextauth]/options"
import { extractUserClaimsFromToken } from "@utils/jwt"
import AUTH_CONFIG from "@app/config/auth"

/**
 * Server-side Token Manager
 * Handles token retrieval and validation on the server side
 * Works with NextAuth server sessions and JWT tokens
 */
class ServerTokenManager {
  private static instance: ServerTokenManager

  public static getInstance(): ServerTokenManager {
    if (!ServerTokenManager.instance) {
      ServerTokenManager.instance = new ServerTokenManager()
    }
    return ServerTokenManager.instance
  }

  private constructor() {}

  /**
   * Get valid access token from server session
   * Returns the access token if available and valid
   */
  public async getValidToken(): Promise<string | undefined> {
    try {
      const session = await getServerSession(authOptions)

      if (!session?.user?.accessToken) {
        if (AUTH_CONFIG.IS_TESTING_MODE) {
          console.log("🔍 [ServerTokenManager] No access token in session")
        }
        return undefined
      }

      const token = session.user.accessToken
      const tokenExpires = session.user.accessTokenExpires || 0
      const now = Date.now()

      // Check if token is expired
      if (now >= tokenExpires) {
        if (AUTH_CONFIG.IS_TESTING_MODE) {
          console.log(
            "🔍 [ServerTokenManager] Token expired, cannot refresh on server side",
          )
          console.log(
            "  Token expired:",
            Math.round((now - tokenExpires) / 1000),
            "seconds ago",
          )
        }
        return undefined
      }

      // Check if token is about to expire (within 30 seconds)
      const bufferTime = 30 * 1000 // 30 seconds
      if (now >= tokenExpires - bufferTime) {
        if (AUTH_CONFIG.IS_TESTING_MODE) {
          console.log(
            "🔍 [ServerTokenManager] Token expires soon, may need refresh",
          )
          console.log(
            "  Time until expiry:",
            Math.round((tokenExpires - now) / 1000),
            "seconds",
          )
        }
      }

      return token
    } catch (error) {
      if (AUTH_CONFIG.IS_TESTING_MODE) {
        console.error("🔍 [ServerTokenManager] Error getting token:", error)
      }
      return undefined
    }
  }

  /**
   * Get user claims from the current session token
   * Returns user claims if token is valid
   */
  public async getUserClaims(): Promise<any> {
    try {
      const token = await this.getValidToken()
      if (!token || token === "oauth_token") {
        return null
      }

      const claims = extractUserClaimsFromToken(token)
      return claims
    } catch (error) {
      if (AUTH_CONFIG.IS_TESTING_MODE) {
        console.error("🔍 [ServerTokenManager] Error extracting claims:", error)
      }
      return null
    }
  }

  /**
   * Get user ID from the current session
   * Returns user ID if available
   */
  public async getUserId(): Promise<number | undefined> {
    try {
      const session = await getServerSession(authOptions)

      if (session?.user?.id) {
        return Number(session.user.id)
      }

      // Fallback: try to get from JWT claims
      const claims = await this.getUserClaims()
      if (claims?.userId) {
        return Number(claims.userId)
      }

      return undefined
    } catch (error) {
      if (AUTH_CONFIG.IS_TESTING_MODE) {
        console.error("🔍 [ServerTokenManager] Error getting user ID:", error)
      }
      return undefined
    }
  }

  /**
   * Check if user is authenticated
   * Returns true if valid session exists
   */
  public async isAuthenticated(): Promise<boolean> {
    try {
      const session = await getServerSession(authOptions)
      return !!session?.user
    } catch (error) {
      if (AUTH_CONFIG.IS_TESTING_MODE) {
        console.error(
          "🔍 [ServerTokenManager] Error checking authentication:",
          error,
        )
      }
      return false
    }
  }

  /**
   * Get session information for debugging
   */
  public async getSessionInfo(): Promise<any> {
    if (process.env.NODE_ENV !== "development") {
      return null
    }

    try {
      const session = await getServerSession(authOptions)
      if (!session) {
        return { status: "no_session" }
      }

      const tokenExpires = session.user?.accessTokenExpires || 0
      const now = Date.now()

      return {
        status: "authenticated",
        userId: session.user?.id,
        hasAccessToken: !!session.user?.accessToken,
        hasRefreshToken: !!session.user?.refreshToken,
        tokenExpires: new Date(tokenExpires).toISOString(),
        isExpired: now >= tokenExpires,
        timeUntilExpiry: Math.round((tokenExpires - now) / 1000),
      }
    } catch (error) {
      return {
        status: "error",
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }
}

// Export singleton instance
export const serverTokenManager = ServerTokenManager.getInstance()
