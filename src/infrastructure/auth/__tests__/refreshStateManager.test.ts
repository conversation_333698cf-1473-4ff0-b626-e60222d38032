/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Test for RefreshStateManager race condition protection
 * This test simulates the exact scenario from the logs
 */

// Mock the RefreshStateManager from NextAuth options
class MockRefreshStateManager {
  private static instance: MockRefreshStateManager
  private isRefreshing = false
  private refreshPromise: Promise<any> | null = null
  private refreshAttempts = 0
  private lastRefreshTime = 0
  private lastSuccessfulRefresh = 0
  private lastUsedRefreshToken = ""
  private readonly MAX_REFRESH_ATTEMPTS = 3
  private readonly MIN_REFRESH_INTERVAL = 2000 // 2 seconds
  private readonly REFRESH_ATTEMPTS_RESET_TIME = 5 * 60 * 1000 // 5 minutes
  private readonly SUCCESS_COOLDOWN = 30 * 1000 // 30 seconds

  public static getInstance(): MockRefreshStateManager {
    if (!MockRefreshStateManager.instance) {
      MockRefreshStateManager.instance = new MockRefreshStateManager()
    }
    return MockRefreshStateManager.instance
  }

  private constructor() {}

  public shouldRefresh(
    tokenExpires: number,
    currentRefreshToken?: string,
  ): boolean {
    const now = Date.now()

    // Check if token is still valid (with buffer time)
    const REFRESH_BUFFER_TIME = 5 * 1000 // 5 seconds
    if (now < tokenExpires - REFRESH_BUFFER_TIME) {
      return false
    }

    // Check if this refresh token was already used recently
    if (
      currentRefreshToken &&
      currentRefreshToken === this.lastUsedRefreshToken
    ) {
      console.log("⏳ This refresh token was already used recently, skipping")
      return false
    }

    // Prevent refresh if we just had a successful refresh recently
    if (now - this.lastSuccessfulRefresh < this.SUCCESS_COOLDOWN) {
      console.log("⏳ Recent successful refresh, skipping (cooldown)")
      return false
    }

    // Reset refresh attempts if enough time has passed
    if (now - this.lastRefreshTime > this.REFRESH_ATTEMPTS_RESET_TIME) {
      this.refreshAttempts = 0
      console.log("🔄 Reset refresh attempts counter")
    }

    // Prevent too frequent refresh attempts
    if (now - this.lastRefreshTime < this.MIN_REFRESH_INTERVAL) {
      console.log("⏳ Refresh attempt too soon, skipping")
      return false
    }

    // Check refresh attempt limits
    if (this.refreshAttempts >= this.MAX_REFRESH_ATTEMPTS) {
      console.error("🚫 Max refresh attempts reached")
      return false
    }

    return true
  }

  public async handleRefresh(
    token: any,
    refreshFunction: (token: any, now: number) => Promise<any>,
  ): Promise<any> {
    const now = Date.now()

    // If refresh is already in progress, wait for it
    if (this.isRefreshing && this.refreshPromise) {
      try {
        console.log("⏳ Waiting for ongoing refresh...")
        const refreshedToken = await this.refreshPromise
        return refreshedToken
      } catch (error) {
        console.error("❌ Shared refresh failed:", error)
        throw error
      }
    }

    // Start refresh process
    if (!this.isRefreshing) {
      console.log("🔄 Starting new refresh process...")
      this.isRefreshing = true
      this.lastRefreshTime = now
      this.refreshAttempts++
      // Store the refresh token being used
      if (token?.refreshToken) {
        this.lastUsedRefreshToken = token.refreshToken
      }
      this.refreshPromise = refreshFunction(token, now)
    }

    try {
      const newToken = await this.refreshPromise
      // Reset attempts on success and record successful refresh time
      this.refreshAttempts = 0
      this.lastSuccessfulRefresh = Date.now()
      // Clear the used refresh token since we got a new one
      this.lastUsedRefreshToken = ""
      console.log(
        "✅ Refresh successful, setting cooldown until:",
        new Date(
          this.lastSuccessfulRefresh + this.SUCCESS_COOLDOWN,
        ).toISOString(),
      )
      return newToken
    } catch (error) {
      console.error("❌ Token refresh failed:", error)
      throw error
    } finally {
      // Reset refresh state
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  public getStatus() {
    return {
      isRefreshing: this.isRefreshing,
      refreshAttempts: this.refreshAttempts,
      maxAttempts: this.MAX_REFRESH_ATTEMPTS,
      lastRefreshTime: this.lastRefreshTime,
      lastSuccessfulRefresh: this.lastSuccessfulRefresh,
      cooldownUntil: this.lastSuccessfulRefresh + this.SUCCESS_COOLDOWN,
    }
  }

  // Reset for testing
  public reset() {
    this.isRefreshing = false
    this.refreshPromise = null
    this.refreshAttempts = 0
    this.lastRefreshTime = 0
    this.lastSuccessfulRefresh = 0
    this.lastUsedRefreshToken = ""
  }
}

describe("RefreshStateManager Race Condition Protection", () => {
  let manager: MockRefreshStateManager
  let mockRefreshFunction: jest.Mock

  beforeEach(() => {
    manager = MockRefreshStateManager.getInstance()
    manager.reset()
    mockRefreshFunction = jest.fn()
  })

  it("should prevent concurrent refresh attempts with same refresh token", async () => {
    const now = Date.now()
    const expiredTokenExpires = now - 1000 // Expired 1 second ago
    const refreshToken = "test-refresh-token-12345678"

    const token = {
      refreshToken,
      accessTokenExpires: expiredTokenExpires,
    }

    // Mock successful refresh
    mockRefreshFunction.mockResolvedValue({
      ...token,
      accessToken: "new-access-token",
      accessTokenExpires: now + 60000, // Valid for 1 minute
    })

    // First call should proceed
    expect(manager.shouldRefresh(expiredTokenExpires, refreshToken)).toBe(true)

    // Start first refresh
    const firstRefresh = manager.handleRefresh(token, mockRefreshFunction)

    // Second call with same refresh token should be blocked
    expect(manager.shouldRefresh(expiredTokenExpires, refreshToken)).toBe(false)

    // Wait for first refresh to complete
    await firstRefresh

    // Verify refresh function was called only once
    expect(mockRefreshFunction).toHaveBeenCalledTimes(1)
  })

  it("should enforce cooldown period after successful refresh", async () => {
    const now = Date.now()
    const expiredTokenExpires = now - 1000
    const refreshToken = "test-refresh-token-87654321"

    const token = {
      refreshToken,
      accessTokenExpires: expiredTokenExpires,
    }

    mockRefreshFunction.mockResolvedValue({
      ...token,
      accessToken: "new-access-token",
      accessTokenExpires: now + 60000,
    })

    // First refresh
    await manager.handleRefresh(token, mockRefreshFunction)

    // Immediate second attempt should be blocked by cooldown
    expect(
      manager.shouldRefresh(expiredTokenExpires, "different-refresh-token"),
    ).toBe(false)

    // After cooldown period, should allow refresh again
    const futureExpires = now + 35000 // 35 seconds later (after 30s cooldown)
    expect(
      manager.shouldRefresh(futureExpires, "different-refresh-token"),
    ).toBe(true)
  })

  it("should handle stale token data by resetting state", () => {
    const now = Date.now()
    const expiredTokenExpires = now - 10000 // 10 seconds ago

    // Simulate stale token scenario
    manager.reset()

    // Should allow refresh for expired token
    expect(
      manager.shouldRefresh(expiredTokenExpires, "stale-refresh-token"),
    ).toBe(true)

    // Verify state was reset
    const status = manager.getStatus()
    expect(status.isRefreshing).toBe(false)
    expect(status.refreshAttempts).toBe(0)
  })
})

export { MockRefreshStateManager }
