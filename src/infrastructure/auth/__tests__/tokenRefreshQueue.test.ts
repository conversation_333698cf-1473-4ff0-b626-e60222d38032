/* eslint-disable @typescript-eslint/naming-convention */

import { tokenRefreshQueue } from "../tokenRefreshQueue"

// Mock NextAuth
jest.mock("next-auth/react", () => ({
  getSession: jest.fn(),
}))

describe("TokenRefreshQueue", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe("Race Condition Protection", () => {
    it("should handle concurrent token requests", async () => {
      const { getSession } = require("next-auth/react")
      
      // Mock session with expired token
      getSession.mockResolvedValue({
        user: {
          accessToken: "expired-token",
          accessTokenExpires: Date.now() - 1000, // Expired 1 second ago
        },
      })

      // Simulate concurrent requests
      const promises = [
        tokenRefreshQueue.getValidToken(),
        tokenRefreshQueue.getValidToken(),
        tokenRefreshQueue.getValidToken(),
      ]

      const results = await Promise.all(promises)

      // All should get the same result
      expect(results[0]).toBe(results[1])
      expect(results[1]).toBe(results[2])
      
      // getSession should be called minimal times (not 3 times)
      expect(getSession).toHaveBeenCalledTimes(1)
    })

    it("should return valid token without refresh", async () => {
      const { getSession } = require("next-auth/react")
      
      // Mock session with valid token
      const validToken = "valid-token"
      getSession.mockResolvedValue({
        user: {
          accessToken: validToken,
          accessTokenExpires: Date.now() + 10 * 60 * 1000, // Valid for 10 minutes
        },
      })

      const result = await tokenRefreshQueue.getValidToken()

      expect(result).toBe(validToken)
      expect(getSession).toHaveBeenCalledTimes(1)
    })

    it("should handle session errors gracefully", async () => {
      const { getSession } = require("next-auth/react")
      
      // Mock session error
      getSession.mockRejectedValue(new Error("Session error"))

      const result = await tokenRefreshQueue.getValidToken()

      expect(result).toBeUndefined()
    })
  })

  describe("Status Monitoring", () => {
    it("should provide refresh status", () => {
      const status = tokenRefreshQueue.getStatus()

      expect(status).toHaveProperty("isRefreshing")
      expect(status).toHaveProperty("pendingRequestsCount")
      expect(typeof status.isRefreshing).toBe("boolean")
      expect(typeof status.pendingRequestsCount).toBe("number")
    })
  })
})
