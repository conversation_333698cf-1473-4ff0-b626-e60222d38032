/* eslint-disable @typescript-eslint/naming-convention */

import { getSession } from "next-auth/react"

/**
 * Token Refresh Queue System
 * Prevents race conditions when multiple API calls need fresh tokens
 * Implements mutex pattern with request queuing
 */
class TokenRefreshQueue {
  private static instance: TokenRefreshQueue
  private isRefreshing = false
  private refreshPromise: Promise<string | undefined> | null = null
  private pendingRequests: Array<{
    resolve: (token: string | undefined) => void
    reject: (error: Error) => void
  }> = []

  public static getInstance(): TokenRefreshQueue {
    if (!TokenRefreshQueue.instance) {
      TokenRefreshQueue.instance = new TokenRefreshQueue()
    }
    return TokenRefreshQueue.instance
  }

  private constructor() {}

  /**
   * Get a valid access token, refreshing if necessary
   * Handles concurrent requests by queuing them
   */
  public async getValidToken(): Promise<string | undefined> {
    try {
      const session = await getSession()

      if (!session?.user?.accessToken) {
        return undefined
      }

      // Check if token is still valid (with 30 second buffer)
      const tokenExpires = session.user.accessTokenExpires || 0
      const bufferTime = 30 * 1000 // 30 second buffer
      const now = Date.now()

      if (now < tokenExpires - bufferTime) {
        return session.user.accessToken
      }

      // Token needs refresh - handle concurrent requests
      if (this.isRefreshing && this.refreshPromise) {
        // Queue this request
        return new Promise((resolve, reject) => {
          this.pendingRequests.push({ resolve, reject })
        })
      }

      // Start refresh process
      this.isRefreshing = true
      this.refreshPromise = this.refreshTokenFromSession()

      try {
        const newToken = await this.refreshPromise

        // Resolve all pending requests
        this.resolvePendingRequests(newToken)

        return newToken
      } catch (error) {
        // Reject all pending requests
        this.rejectPendingRequests(error as Error)
        throw error
      } finally {
        this.cleanup()
      }
    } catch (error) {
      console.error("TokenRefreshQueue: Failed to get valid token:", error)
      return undefined
    }
  }

  /**
   * Force refresh the session to get new token
   * This triggers NextAuth to refresh the token
   */
  private async refreshTokenFromSession(): Promise<string | undefined> {
    try {
      // Wait a bit to let NextAuth JWT callback handle the refresh
      await new Promise((resolve) => setTimeout(resolve, 100))

      // Get fresh session after JWT callback refresh
      const session = await getSession()

      if (!session?.user?.accessToken) {
        throw new Error("No access token after refresh")
      }

      if (process.env.NODE_ENV === "development") {
        console.log("🔄 TokenRefreshQueue: Session refreshed successfully")
      }

      return session.user.accessToken
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("❌ TokenRefreshQueue: Session refresh failed:", error)
      }
      throw error
    }
  }

  /**
   * Resolve all pending requests with the new token
   */
  private resolvePendingRequests(token: string | undefined): void {
    const requests = [...this.pendingRequests]
    this.pendingRequests = []

    requests.forEach(({ resolve }) => {
      resolve(token)
    })

    if (process.env.NODE_ENV === "development" && requests.length > 0) {
      console.log(
        `✅ TokenRefreshQueue: Resolved ${requests.length} pending requests`,
      )
    }
  }

  /**
   * Reject all pending requests with error
   */
  private rejectPendingRequests(error: Error): void {
    const requests = [...this.pendingRequests]
    this.pendingRequests = []

    requests.forEach(({ reject }) => {
      reject(error)
    })

    if (process.env.NODE_ENV === "development" && requests.length > 0) {
      console.log(
        `❌ TokenRefreshQueue: Rejected ${requests.length} pending requests`,
      )
    }
  }

  /**
   * Clean up refresh state
   */
  private cleanup(): void {
    this.isRefreshing = false
    this.refreshPromise = null
  }

  /**
   * Get current refresh status (for debugging)
   */
  public getStatus(): {
    isRefreshing: boolean
    pendingRequestsCount: number
  } {
    return {
      isRefreshing: this.isRefreshing,
      pendingRequestsCount: this.pendingRequests.length,
    }
  }
}

export const tokenRefreshQueue = TokenRefreshQueue.getInstance()
