/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Debug utility for monitoring token refresh state
 * Only available in development mode
 */
export class RefreshStateDebugger {
  private static instance: RefreshStateDebugger
  private intervalId: NodeJS.Timeout | null = null

  public static getInstance(): RefreshStateDebugger {
    if (!RefreshStateDebugger.instance) {
      RefreshStateDebugger.instance = new RefreshStateDebugger()
    }
    return RefreshStateDebugger.instance
  }

  private constructor() {}

  /**
   * Start monitoring refresh state (development only)
   */
  public startMonitoring(intervalMs: number = 5000): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    if (this.intervalId) {
      this.stopMonitoring()
    }

    console.log("🔍 Starting refresh state monitoring...")

    this.intervalId = setInterval(() => {
      this.logCurrentState()
    }, intervalMs)
  }

  /**
   * Stop monitoring refresh state
   */
  public stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      console.log("🔍 Stopped refresh state monitoring")
    }
  }

  /**
   * Log current refresh state
   */
  public logCurrentState(): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    try {
      // Import dynamically to avoid circular dependencies
      import("next-auth/react")
        .then(({ getSession }) => getSession())
        .then((session: any) => {
          if (session?.user?.accessToken) {
            const tokenExpires = session.user.accessTokenExpires || 0
            const now = Date.now()
            const timeUntilExpiry = tokenExpires - now
            const isExpired = timeUntilExpiry <= 0

            console.log("🔍 Token State:", {
              isExpired,
              timeUntilExpiry: `${Math.round(timeUntilExpiry / 1000)}s`,
              expiresAt: new Date(tokenExpires).toISOString(),
            })
          } else {
            console.log("🔍 No active session")
          }
        })
        .catch((error: any) => {
          console.error("🔍 Failed to get session for debugging:", error)
        })
    } catch (error) {
      console.error("🔍 Debug state error:", error)
    }
  }

  /**
   * Log token refresh event
   */
  public logRefreshEvent(
    event: "start" | "success" | "error",
    data?: any,
  ): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    const timestamp = new Date().toISOString()

    switch (event) {
      case "start":
        console.log(`🔄 [${timestamp}] Refresh started`, data)
        break
      case "success":
        console.log(`✅ [${timestamp}] Refresh successful`, data)
        break
      case "error":
        console.log(`❌ [${timestamp}] Refresh failed`, data)
        break
    }
  }

  /**
   * Check if multiple refresh attempts are happening
   */
  public detectRaceCondition(): void {
    if (process.env.NODE_ENV !== "development") {
      return
    }

    let refreshCount = 0
    const resetTime = 2000 // 2 seconds

    const originalConsoleLog = console.log
    console.log = (...args: any[]) => {
      const message = args.join(" ")

      if (message.includes("🔄 Starting new refresh process")) {
        refreshCount++

        if (refreshCount > 1) {
          console.warn(
            `⚠️ RACE CONDITION DETECTED: ${refreshCount} concurrent refresh attempts!`,
          )
        }

        // Reset counter after delay
        setTimeout(() => {
          refreshCount = 0
        }, resetTime)
      }

      originalConsoleLog.apply(console, args)
    }
  }
}

// Export singleton instance
export const refreshStateDebugger = RefreshStateDebugger.getInstance()

// Auto-start monitoring in development
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  // Start monitoring after a short delay to avoid startup noise
  setTimeout(() => {
    refreshStateDebugger.startMonitoring(10000) // Every 10 seconds
    refreshStateDebugger.detectRaceCondition()
  }, 3000)
}
