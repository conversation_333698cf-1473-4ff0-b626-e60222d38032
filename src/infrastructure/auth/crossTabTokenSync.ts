/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Cross-Tab Token Synchronization
 * Coordinates token refresh across multiple browser tabs/windows
 * Prevents multiple tabs from refreshing tokens simultaneously
 */
class CrossTabTokenSync {
  private static instance: CrossTabTokenSync
  private channel: BroadcastChannel | null = null
  private isSupported = false

  public static getInstance(): CrossTabTokenSync {
    if (!CrossTabTokenSync.instance) {
      CrossTabTokenSync.instance = new CrossTabTokenSync()
    }
    return CrossTabTokenSync.instance
  }

  private constructor() {
    this.initializeBroadcastChannel()
  }

  /**
   * Initialize BroadcastChannel if supported
   */
  private initializeBroadcastChannel(): void {
    try {
      if (typeof window !== "undefined" && "BroadcastChannel" in window) {
        this.channel = new BroadcastChannel("nextauth-token-sync")
        this.isSupported = true
        this.setupEventListeners()

        if (process.env.NODE_ENV === "development") {
          console.log("🔗 CrossTabTokenSync: BroadcastChannel initialized")
        }
      }
    } catch (error) {
      console.warn("CrossTabTokenSync: BroadcastChannel not supported:", error)
    }
  }

  /**
   * Setup event listeners for cross-tab communication
   */
  private setupEventListeners(): void {
    if (!this.channel) return

    this.channel.addEventListener("message", (event) => {
      const { type, data } = event.data

      switch (type) {
        case "TOKEN_REFRESH_START":
          this.handleTokenRefreshStart(data)
          break
        case "TOKEN_REFRESH_SUCCESS":
          this.handleTokenRefreshSuccess(data)
          break
        case "TOKEN_REFRESH_ERROR":
          this.handleTokenRefreshError(data)
          break
        default:
          break
      }
    })
  }

  /**
   * Broadcast that token refresh has started
   */
  public broadcastRefreshStart(tabId: string): void {
    if (!this.isSupported || !this.channel) return

    this.channel.postMessage({
      type: "TOKEN_REFRESH_START",
      data: { tabId, timestamp: Date.now() },
    })

    if (process.env.NODE_ENV === "development") {
      console.log("📡 CrossTabTokenSync: Broadcasted refresh start")
    }
  }

  /**
   * Broadcast successful token refresh
   */
  public broadcastRefreshSuccess(tabId: string, tokenData: any): void {
    if (!this.isSupported || !this.channel) return

    this.channel.postMessage({
      type: "TOKEN_REFRESH_SUCCESS",
      data: { tabId, tokenData, timestamp: Date.now() },
    })

    if (process.env.NODE_ENV === "development") {
      console.log("📡 CrossTabTokenSync: Broadcasted refresh success")
    }
  }

  /**
   * Broadcast token refresh error
   */
  public broadcastRefreshError(tabId: string, error: string): void {
    if (!this.isSupported || !this.channel) return

    this.channel.postMessage({
      type: "TOKEN_REFRESH_ERROR",
      data: { tabId, error, timestamp: Date.now() },
    })

    if (process.env.NODE_ENV === "development") {
      console.log("📡 CrossTabTokenSync: Broadcasted refresh error")
    }
  }

  /**
   * Handle token refresh start from another tab
   */
  private handleTokenRefreshStart(data: any): void {
    if (process.env.NODE_ENV === "development") {
      console.log(
        "📡 CrossTabTokenSync: Another tab started token refresh:",
        data.tabId,
      )
    }
    // Could implement logic to pause local refresh attempts
  }

  /**
   * Handle successful token refresh from another tab
   */
  private handleTokenRefreshSuccess(data: any): void {
    if (process.env.NODE_ENV === "development") {
      console.log(
        "📡 CrossTabTokenSync: Another tab refreshed token successfully:",
        data.tabId,
      )
    }
    // Could implement logic to update local token state
    // Note: NextAuth handles this automatically through session sync
  }

  /**
   * Handle token refresh error from another tab
   */
  private handleTokenRefreshError(data: any): void {
    if (process.env.NODE_ENV === "development") {
      console.log(
        "📡 CrossTabTokenSync: Another tab failed to refresh token:",
        data.tabId,
        data.error,
      )
    }
    // Could implement logic to handle shared error state
  }

  /**
   * Generate unique tab ID
   */
  public generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * Check if cross-tab sync is supported
   */
  public isBroadcastSupported(): boolean {
    return this.isSupported
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.channel) {
      this.channel.close()
      this.channel = null
    }
  }
}

export const crossTabTokenSync = CrossTabTokenSync.getInstance()
